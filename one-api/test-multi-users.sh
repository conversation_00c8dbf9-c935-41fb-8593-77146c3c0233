#!/bin/bash

echo "🧪 测试多用户API Key区分功能"
echo "============================"

# 定义三个不同的API Key
API_KEY_1="sk-80mzX2FLRDdQ5nmQ0951F968Cf644d23B7351852D25c7644"
API_KEY_2="sk-ZncNB9PXFIf5JiDO68Bf0dEc2aA744E49551D4A491Cf9897"
API_KEY_3="sk-sPVKUn4GVZaQG2go1e74A84520E7461e86201859C51e89Ae"

PROXY_URL="http://localhost:3001"

echo "测试的API Key:"
echo "1. $API_KEY_1 (期望用户: Henry - User ID 2)"
echo "2. $API_KEY_2 (期望用户: 未知 - User ID 1)"
echo "3. $API_KEY_3 (期望用户: Henry - User ID 2)"
echo ""

# 清理之前的缓存
echo "🔄 清理用户缓存..."
curl -s "$PROXY_URL/api/status" > /dev/null

echo ""
echo "📊 检查代理服务状态..."
STATUS_RESPONSE=$(curl -s "$PROXY_URL/api/status")
echo "$STATUS_RESPONSE" | jq '{
  proxy_status,
  database_connected,
  user_resolution_method,
  cached_users
}'

echo ""
echo "=== 测试 API Key 1 ==="
echo "🔑 API Key: $API_KEY_1"

# 创建测试请求1
cat > test_request_1.json << 'EOF'
{
  "model": "qwen-plus",
  "messages": [
    {
      "role": "user", 
      "content": "你好，我是用户1，请简单回复。"
    }
  ],
  "max_tokens": 20
}
EOF

echo "📝 发送聊天请求..."
CHAT_RESPONSE_1=$(curl -s -X POST "$PROXY_URL/v1/chat/completions" \
  -H "Authorization: Bearer $API_KEY_1" \
  -H "Content-Type: application/json" \
  -d @test_request_1.json)

echo "✅ 聊天响应:"
echo "$CHAT_RESPONSE_1" | jq '.choices[0].message.content'

sleep 2

echo ""
echo "=== 测试 API Key 2 ==="
echo "🔑 API Key: $API_KEY_2"

# 创建测试请求2
cat > test_request_2.json << 'EOF'
{
  "model": "qwen-plus",
  "messages": [
    {
      "role": "user", 
      "content": "你好，我是用户2，请告诉我你是谁。"
    }
  ],
  "max_tokens": 20
}
EOF

echo "📝 发送聊天请求..."
CHAT_RESPONSE_2=$(curl -s -X POST "$PROXY_URL/v1/chat/completions" \
  -H "Authorization: Bearer $API_KEY_2" \
  -H "Content-Type: application/json" \
  -d @test_request_2.json)

echo "✅ 聊天响应:"
echo "$CHAT_RESPONSE_2" | jq '.choices[0].message.content'

sleep 2

echo ""
echo "=== 测试 API Key 3 ==="
echo "🔑 API Key: $API_KEY_3"

# 创建测试请求3
cat > test_request_3.json << 'EOF'
{
  "model": "qwen-plus",
  "messages": [
    {
      "role": "user", 
      "content": "你好，我是用户3，请简单聊聊天气。"
    }
  ],
  "max_tokens": 20
}
EOF

echo "📝 发送聊天请求..."
CHAT_RESPONSE_3=$(curl -s -X POST "$PROXY_URL/v1/chat/completions" \
  -H "Authorization: Bearer $API_KEY_3" \
  -H "Content-Type: application/json" \
  -d @test_request_3.json)

echo "✅ 聊天响应:"
echo "$CHAT_RESPONSE_3" | jq '.choices[0].message.content'

sleep 3

echo ""
echo "📊 等待日志写入完成..."

echo ""
echo "📝 查看最近3条对话记录 (用户区分测试):"
LOGS_RESPONSE=$(curl -s "$PROXY_URL/logs?limit=3")
echo "$LOGS_RESPONSE" | jq '.logs[] | {
  timestamp: .timestamp,
  user_info: {
    username: .user_info.username,
    user_id: .user_info.id,
    display_name: .user_info.display_name,
    token_name: .user_info.token_name,
    source: .user_info.source
  },
  api_key_prefix: (.metadata.api_key[:12] + "..."),
  user_message: .request.messages[0].content,
  model: .request.model
}' | jq -s 'reverse'

echo ""
echo "📊 用户统计分析:"
echo "$LOGS_RESPONSE" | jq '.logs | group_by(.user_info.username) | map({
  username: .[0].user_info.username,
  user_id: .[0].user_info.id,
  count: length,
  api_keys: [.[].metadata.api_key] | unique | map(.[:12] + "...")
})'

echo ""
echo "📄 检查本地日志文件最新3条记录:"
if [ -f "conversation_logs.jsonl" ]; then
    echo "最新3条记录的用户信息:"
    tail -3 conversation_logs.jsonl | jq '{
      timestamp,
      username: .user_info.username,
      user_id: .user_info.id,
      api_key_prefix: (.metadata.api_key[:12] + "..."),
      source: .user_info.source
    }'
else
    echo "本地日志文件不存在"
fi

# 清理临时文件
rm -f test_request_1.json test_request_2.json test_request_3.json

echo ""
echo "🎯 测试结果分析:"
echo "1. 检查是否正确识别了不同的用户"
echo "2. 检查相同用户的不同API Key是否被正确关联"
echo "3. 检查用户信息来源是否为database"
echo "4. 检查API Key是否被完整记录"

echo ""
echo "📋 期望结果:"
echo "- API Key 1 (80mzX2FL...): 应该显示 Henry (User ID 2)"
echo "- API Key 2 (ZncNB9PX...): 应该显示不同的用户 (User ID 1)"
echo "- API Key 3 (sPVKUn4G...): 应该显示 Henry (User ID 2)"
echo "- 相同用户的不同API Key应该被正确关联"
echo "- 不同用户应该被明确区分"

echo ""
echo "🌐 查看Web界面: $PROXY_URL"
