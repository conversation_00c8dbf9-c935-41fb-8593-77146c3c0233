#!/usr/bin/env python3
"""
比较不同计算方式生成的报告差异
"""

import re
import os
from datetime import datetime

class ReportComparator:
    """报告比较器"""
    
    def __init__(self):
        """初始化比较器"""
        self.differences = []
    
    def compare_reports(self, file1, file2, label1, label2):
        """比较两个报告文件"""
        print(f"\n🔍 比较报告: {label1} vs {label2}")
        print("="*60)
        
        # 读取文件内容
        content1 = self._read_report(file1)
        content2 = self._read_report(file2)
        
        if not content1 or not content2:
            print("❌ 无法读取报告文件")
            return
        
        # 提取关键数据
        data1 = self._extract_key_data(content1)
        data2 = self._extract_key_data(content2)
        
        # 比较数据
        differences = self._compare_data(data1, data2, label1, label2)
        
        if differences:
            print(f"⚠️  发现 {len(differences)} 个差异:")
            for diff in differences:
                print(f"  • {diff}")
            self.differences.extend(differences)
        else:
            print("✅ 报告内容完全一致")
    
    def _read_report(self, filepath):
        """读取报告文件"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"❌ 读取文件失败 {filepath}: {str(e)}")
            return None
    
    def _extract_key_data(self, content):
        """提取报告中的关键数据"""
        data = {}
        
        # 提取趋势强度
        trend_match = re.search(r'整体趋势\s+.*?（强度([\d.]+)）', content)
        if trend_match:
            data['trend_strength'] = float(trend_match.group(1))
        
        # 提取历史波动率
        vol_match = re.search(r'历史波动率\s+([\d.]+)', content)
        if vol_match:
            data['historical_volatility'] = float(vol_match.group(1))
        
        # 提取ATR波动率
        atr_match = re.search(r'近14日平均ATR波动率\s+([\d.]+)', content)
        if atr_match:
            data['atr_volatility'] = float(atr_match.group(1))
        
        # 提取波动率百分位
        percentile_match = re.search(r'波动率百分位\s+([\d.]+)%', content)
        if percentile_match:
            data['volatility_percentile'] = float(percentile_match.group(1))
        
        # 提取14天日均波动幅度
        atr_points_match = re.search(r'14天日均波动幅度\s+([\d.]+)点', content)
        if atr_points_match:
            data['atr_points'] = float(atr_points_match.group(1))
        
        # 提取止损止盈价格
        stop_loss_long_match = re.search(r'做多止损：([\d.]+)', content)
        if stop_loss_long_match:
            data['stop_loss_long'] = float(stop_loss_long_match.group(1))
        
        take_profit_long_match = re.search(r'做多止盈：([\d.]+)', content)
        if take_profit_long_match:
            data['take_profit_long'] = float(take_profit_long_match.group(1))
        
        stop_loss_short_match = re.search(r'做空止损：([\d.]+)', content)
        if stop_loss_short_match:
            data['stop_loss_short'] = float(stop_loss_short_match.group(1))
        
        take_profit_short_match = re.search(r'做空止盈：([\d.]+)', content)
        if take_profit_short_match:
            data['take_profit_short'] = float(take_profit_short_match.group(1))
        
        # 提取当前价格
        current_price_match = re.search(r'当前价\s+([\d.]+)', content)
        if current_price_match:
            data['current_price'] = float(current_price_match.group(1))
        
        # 提取压力位和支撑位
        resistance_matches = re.findall(r'压力位\s+([\d.]+)\s+.*?([+-][\d.]+)%', content)
        if resistance_matches:
            data['resistance_levels'] = [(float(price), float(pct)) for price, pct in resistance_matches]
        
        support_matches = re.findall(r'支撑位\s+([\d.]+)\s+.*?([+-][\d.]+)%', content)
        if support_matches:
            data['support_levels'] = [(float(price), float(pct)) for price, pct in support_matches]
        
        return data
    
    def _compare_data(self, data1, data2, label1, label2):
        """比较两组数据"""
        differences = []
        
        # 比较数值字段
        numeric_fields = [
            'trend_strength', 'historical_volatility', 'atr_volatility', 
            'volatility_percentile', 'atr_points', 'stop_loss_long', 
            'take_profit_long', 'stop_loss_short', 'take_profit_short', 
            'current_price'
        ]
        
        for field in numeric_fields:
            if field in data1 and field in data2:
                val1 = data1[field]
                val2 = data2[field]
                
                # 计算相对差异
                if val1 != 0:
                    relative_diff = abs(val1 - val2) / abs(val1) * 100
                else:
                    relative_diff = abs(val2) * 100
                
                if relative_diff > 0.1:  # 0.1%的容差
                    differences.append(
                        f"{field}: {label1}={val1:.4f}, {label2}={val2:.4f} "
                        f"(差异: {relative_diff:.2f}%)"
                    )
        
        # 比较支撑压力位
        if 'resistance_levels' in data1 and 'resistance_levels' in data2:
            res1 = data1['resistance_levels']
            res2 = data2['resistance_levels']
            
            if len(res1) != len(res2):
                differences.append(f"压力位数量不同: {label1}={len(res1)}, {label2}={len(res2)}")
            else:
                for i, ((price1, pct1), (price2, pct2)) in enumerate(zip(res1, res2)):
                    if abs(price1 - price2) > 0.01 or abs(pct1 - pct2) > 0.01:
                        differences.append(
                            f"压力位{i+1}: {label1}=({price1:.2f}, {pct1:.1f}%), "
                            f"{label2}=({price2:.2f}, {pct2:.1f}%)"
                        )
        
        if 'support_levels' in data1 and 'support_levels' in data2:
            sup1 = data1['support_levels']
            sup2 = data2['support_levels']
            
            if len(sup1) != len(sup2):
                differences.append(f"支撑位数量不同: {label1}={len(sup1)}, {label2}={len(sup2)}")
            else:
                for i, ((price1, pct1), (price2, pct2)) in enumerate(zip(sup1, sup2)):
                    if abs(price1 - price2) > 0.01 or abs(pct1 - pct2) > 0.01:
                        differences.append(
                            f"支撑位{i+1}: {label1}=({price1:.2f}, {pct1:.1f}%), "
                            f"{label2}=({price2:.2f}, {pct2:.1f}%)"
                        )
        
        return differences
    
    def save_comparison_report(self, filename="report_comparison.txt"):
        """保存比较报告"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("技术指标计算方式报告比较\n")
            f.write("="*60 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总差异数量: {len(self.differences)}\n\n")
            
            if self.differences:
                for i, diff in enumerate(self.differences, 1):
                    f.write(f"{i}. {diff}\n")
            else:
                f.write("所有计算方式的报告结果完全一致！\n")
        
        print(f"\n📄 比较报告已保存: {filename}")

def main():
    """主函数"""
    print("📊 技术指标计算方式报告比较")
    print("="*60)
    
    # 报告文件路径
    reports = {
        'Local': 'analysis_results/comprehensive_analysis_CFFEX_IF2509_20250724_111203.txt',
        'TA-Lib': 'analysis_results/comprehensive_analysis_CFFEX_IF2509_20250724_141854.txt',
        'NumPy': 'analysis_results/comprehensive_analysis_CFFEX_IF2509_20250724_142134.txt'
    }
    
    # 检查文件是否存在
    existing_reports = {}
    for label, path in reports.items():
        if os.path.exists(path):
            existing_reports[label] = path
        else:
            print(f"⚠️  报告文件不存在: {path}")
    
    if len(existing_reports) < 2:
        print("❌ 需要至少两个报告文件进行比较")
        return
    
    comparator = ReportComparator()
    
    # 进行两两比较
    labels = list(existing_reports.keys())
    for i in range(len(labels)):
        for j in range(i + 1, len(labels)):
            label1, label2 = labels[i], labels[j]
            file1, file2 = existing_reports[label1], existing_reports[label2]
            comparator.compare_reports(file1, file2, label1, label2)
    
    # 生成总结
    print(f"\n📊 比较总结")
    print("="*60)
    print(f"比较的报告数量: {len(existing_reports)}")
    print(f"发现的差异数量: {len(comparator.differences)}")
    
    # 保存比较报告
    comparator.save_comparison_report()
    
    if len(comparator.differences) == 0:
        print("\n🎉 所有计算方式的报告结果完全一致！")
    else:
        print(f"\n⚠️  发现 {len(comparator.differences)} 个差异，请查看比较报告")

if __name__ == "__main__":
    main()
