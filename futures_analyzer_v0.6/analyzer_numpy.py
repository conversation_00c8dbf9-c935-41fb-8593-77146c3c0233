#!/usr/bin/env python3
"""
期货技术分析系统 - NumPy版本
基于NumPy/SciPy自主实现的独立分析脚本
"""

import sys
import os
import argparse
import logging
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from remote_data_loader import RemoteDataLoader
from calculator_comparison_tests.numpy_technical_indicators import NumpyTechnicalIndicators
from enhanced_analysis_engine import EnhancedAnalysisEngine
from enhanced_report_generator import EnhancedReportGenerator

# 配置日志
logging.basicConfig(
    level=logging.ERROR,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('analysis_numpy.log')
    ]
)

def validate_contract_format(contract_str):
    """验证合约格式"""
    if '/' not in contract_str:
        raise argparse.ArgumentTypeError(
            f"合约格式错误: {contract_str}. 正确格式: 交易所/合约代码 (如: CFFEX/IF2509)"
        )
    
    exchange, contract = contract_str.split('/', 1)
    
    # 验证交易所
    valid_exchanges = ['CFFEX', 'SHFE', 'DCE', 'ZCE', 'GFEX']
    if exchange not in valid_exchanges:
        raise argparse.ArgumentTypeError(
            f"不支持的交易所: {exchange}. 支持的交易所: {', '.join(valid_exchanges)}"
        )
    
    return contract_str

def check_data_availability(exchange, contract, loader):
    """检查数据可用性"""
    try:
        # 检查网络连接
        if not loader.test_connection():
            return False, "无法连接到数据服务器，请检查网络连接"
        
        # 检查交易所是否支持
        available_exchanges = loader.list_available_exchanges()
        if exchange not in available_exchanges:
            return False, f"交易所 {exchange} 不在支持列表中，支持的交易所: {', '.join(available_exchanges)}"
        
        # 检查必需的数据周期是否支持
        periods = loader.get_available_periods(exchange, contract)
        required_periods = ['15分钟线', '日线']
        missing_periods = [p for p in required_periods if p not in periods]
        
        if missing_periods:
            return False, f"缺少必需的数据周期: {', '.join(missing_periods)}"
        
        return True, ""
        
    except Exception as e:
        return False, f"数据检查失败: {str(e)}"

def load_and_validate_data(loader, exchange, contract):
    """加载和验证数据"""
    print("📊 加载数据...")
    
    # 加载15分钟线数据
    minute_data = loader.load_contract_data(exchange, contract, '15分钟线')
    if minute_data is None or len(minute_data) < 100:
        raise ValueError("15分钟线数据不足，需要至少100条记录")
    
    # 加载日线数据
    daily_data = loader.load_contract_data(exchange, contract, '日线')
    if daily_data is None or len(daily_data) < 60:
        raise ValueError("日线数据不足，需要至少60条记录")
    
    print(f"  ✅ 15分钟线数据: {len(minute_data)} 条记录")
    print(f"  ✅ 日线数据: {len(daily_data)} 条记录")
    
    return minute_data, daily_data

def calculate_indicators(calculator, minute_data, daily_data):
    """计算技术指标"""
    print("🔧 计算技术指标 (NumPy/SciPy)...")
    
    # 计算15分钟线指标
    minute_indicators = calculator.calculate_all_indicators(minute_data)
    if minute_indicators is None:
        raise ValueError("15分钟线指标计算失败")
    print("  ✅ 15分钟线指标计算完成")
    
    # 计算日线指标
    daily_indicators = calculator.calculate_all_indicators(daily_data)
    if daily_indicators is None:
        raise ValueError("日线指标计算失败")
    print("  ✅ 日线指标计算完成")
    
    return minute_indicators, daily_indicators

def generate_analysis(engine, minute_indicators, daily_indicators, minute_data, daily_data, exchange, contract):
    """生成综合分析"""
    print("📝 生成综合分析...")

    analysis = engine.generate_comprehensive_analysis(
        exchange, contract, minute_data, daily_data,
        minute_indicators, daily_indicators
    )

    if analysis is None:
        raise ValueError("分析生成失败")

    print("  ✅ 综合分析生成完成")
    return analysis

def save_report(report_generator, analysis):
    """保存分析报告"""
    print("💾 保存分析报告...")

    report_path = report_generator.save_comprehensive_report(analysis)
    if report_path is None:
        raise ValueError("报告生成失败")

    print(f"  ✅ 报告已保存: {report_path}")
    return report_path

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='期货技术分析系统 - NumPy版本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python analyzer_numpy.py CFFEX/IF2509
  python analyzer_numpy.py SHFE/au2510 --verbose
  python analyzer_numpy.py DCE/a2509 --output-dir numpy_reports

支持的交易所:
  CFFEX - 中国金融期货交易所
  SHFE  - 上海期货交易所  
  DCE   - 大连商品交易所
  ZCE   - 郑州商品交易所
  GFEX  - 广州期货交易所

特点:
  - 基于NumPy/SciPy自主实现
  - 完全开源，无第三方依赖
  - 可自定义指标计算逻辑
        """
    )
    
    parser.add_argument(
        'contract',
        type=validate_contract_format,
        help='合约代码，格式: 交易所/合约代码 (如: CFFEX/IF2509)'
    )
    
    parser.add_argument(
        '--output-dir',
        default='analysis_results',
        help='输出目录 (默认: analysis_results)'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='显示详细输出'
    )
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.INFO)
    
    print("🚀 期货技术分析系统 v0.5 (NumPy版本)")
    print("🔬 基于NumPy/SciPy自主实现")
    print("="*60)
    
    try:
        # 解析合约代码
        exchange, contract = args.contract.split('/')
        
        print(f"📈 分析合约: {args.contract}")
        print(f"   交易所: {exchange}")
        print(f"   合约代码: {contract}")
        
        # 初始化组件
        print("🔗 初始化远程数据连接...")
        loader = RemoteDataLoader()
        
        print("🔧 初始化NumPy技术指标计算器...")
        calculator = NumpyTechnicalIndicators()
        
        engine = EnhancedAnalysisEngine()
        report_generator = EnhancedReportGenerator(args.output_dir)
        
        # 检查数据可用性
        is_available, error_msg = check_data_availability(exchange, contract, loader)
        if not is_available:
            print(f"❌ {error_msg}")
            sys.exit(1)
        
        # 加载数据
        minute_data, daily_data = load_and_validate_data(loader, exchange, contract)
        
        # 计算指标
        minute_indicators, daily_indicators = calculate_indicators(
            calculator, minute_data, daily_data
        )
        
        # 生成分析
        analysis = generate_analysis(
            engine, minute_indicators, daily_indicators,
            minute_data, daily_data, exchange, contract
        )
        
        # 保存报告
        report_path = save_report(report_generator, analysis)
        
        print(f"\n✅ 分析完成！")
        print(f"📁 报告文件: {report_path}")
        
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 分析失败: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
