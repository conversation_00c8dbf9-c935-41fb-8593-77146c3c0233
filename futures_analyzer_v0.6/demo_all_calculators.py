#!/usr/bin/env python3
"""
期货技术分析系统 - 三种计算器演示脚本
展示Local、TA-Lib、NumPy三种计算方式的使用
"""

import subprocess
import sys
import os
from datetime import datetime

def run_analysis(script_name, contract, calculator_type):
    """运行分析脚本"""
    print(f"\n{'='*60}")
    print(f"🚀 运行 {calculator_type} 分析")
    print(f"📈 合约: {contract}")
    print(f"🔧 脚本: {script_name}")
    print(f"{'='*60}")
    
    try:
        # 运行分析脚本
        result = subprocess.run(
            [sys.executable, script_name, contract],
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        
        if result.returncode == 0:
            print("✅ 分析成功完成")
            # 提取报告文件路径
            output_lines = result.stdout.split('\n')
            for line in output_lines:
                if '报告文件:' in line:
                    report_path = line.split('报告文件:')[-1].strip()
                    print(f"📁 报告文件: {report_path}")
                    return report_path
        else:
            print("❌ 分析失败")
            print(f"错误信息: {result.stderr}")
            return None
            
    except subprocess.TimeoutExpired:
        print("⏰ 分析超时")
        return None
    except Exception as e:
        print(f"❌ 运行异常: {str(e)}")
        return None

def compare_reports(reports):
    """比较报告文件"""
    print(f"\n{'='*60}")
    print("📊 报告对比总结")
    print(f"{'='*60}")
    
    valid_reports = [r for r in reports if r is not None]
    
    if len(valid_reports) == 0:
        print("❌ 没有成功生成的报告")
        return
    
    print(f"✅ 成功生成 {len(valid_reports)} 个报告:")
    for i, report in enumerate(valid_reports, 1):
        print(f"  {i}. {report}")
    
    if len(valid_reports) == 3:
        print("\n🎉 三种计算器都成功运行！")
        print("💡 您可以比较这些报告来验证计算一致性")
    elif len(valid_reports) == 2:
        print("\n⚠️  有一种计算器运行失败")
        print("💡 请检查依赖是否正确安装")
    else:
        print("\n⚠️  只有一种计算器成功运行")
        print("💡 请检查其他计算器的依赖安装")

def main():
    """主函数"""
    print("🚀 期货技术分析系统 - 三种计算器演示")
    print("📊 Local vs TA-Lib vs NumPy 计算器对比")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试合约
    test_contract = "CFFEX/IF2509"
    
    print(f"\n📈 测试合约: {test_contract}")
    print("🔧 将依次运行三种计算器...")
    
    # 存储报告路径
    reports = []
    
    # 1. Local计算器 (基础版本)
    report1 = run_analysis("analyzer_cli.py", test_contract, "Local计算器")
    reports.append(report1)
    
    # 2. TA-Lib计算器
    report2 = run_analysis("analyzer_talib.py", test_contract, "TA-Lib计算器")
    reports.append(report2)
    
    # 3. NumPy计算器
    report3 = run_analysis("analyzer_numpy.py", test_contract, "NumPy计算器")
    reports.append(report3)
    
    # 对比总结
    compare_reports(reports)
    
    print(f"\n⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print(f"\n📁 所有报告保存在 analysis_results/ 目录中")

if __name__ == "__main__":
    main()
