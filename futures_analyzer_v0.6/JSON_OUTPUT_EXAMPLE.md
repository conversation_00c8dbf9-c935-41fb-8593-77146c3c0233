# 期货技术分析系统 v0.6 - JSON输出格式示例

## 📋 概述

v0.6版本新增了JSON格式输出功能，基于numpy版本的计算实现，提供标准化的JSON格式分析报告。

## 🔧 使用方法

```bash
# 基本使用
python analyzer_numpy_json.py CFFEX/IF2509

# 指定请求ID
python analyzer_numpy_json.py CFFEX/IF2509 --req-id "12345"

# 指定输出目录
python analyzer_numpy_json.py SHFE/au2510 --output-dir json_reports

# 详细输出模式
python analyzer_numpy_json.py DCE/a2509 --verbose
```

## 📊 JSON输出格式

### 顶层结构
```json
{
  "func_id": "3001",
  "req_id": "用户提供或自动生成的UUID",
  "error_id": "1",
  "data": { ... }
}
```

### 字段说明
- **func_id**: 固定值 "3001"
- **req_id**: 请求ID，用户可通过 `--req-id` 参数指定，否则自动生成UUID
- **error_id**: 状态标识，"1"表示成功，"0"表示失败
- **data**: 分析数据主体

## 📈 完整JSON示例

```json
{
  "func_id": "3001",
  "req_id": "test-12345",
  "error_id": "1",
  "data": {
    "contract_code": "IF2509",
    "analysis_time": "2025-07-29 10:09:26",
    "trend_analysis": {
      "overall_trend": {
        "direction": "上升趋势",
        "strength": 0.8,
        "description": "上升趋势较为确定"
      },
      "multi_period_trend": {
        "short_term": "下降",
        "medium_term": "上升",
        "long_term": "上升"
      }
    },
    "momentum_analysis": {
      "market_momentum": "市场动量平衡，多空力量相当",
      "consistency_statement": "注意：虽然日K线趋势强度较高(0.8)，但短期动量指标显示中性，可能表明趋势在短期内出现调整或整理"
    },
    "volatility_analysis": {
      "volatility_level": "正常",
      "volatility_description": "市场波动正常，价格变化适中，风险可控",
      "historical_volatility": 0.2401,
      "atr_volatility": 0.0228,
      "volatility_percentile": "65.6%",
      "percentile_interpretation": "当前波动率适中，市场活跃度正常",
      "risk_control": {
        "long_stop_loss": 2304.12,
        "long_take_profit": 2578.82,
        "short_stop_loss": 2523.88,
        "short_take_profit": 2249.18,
        "avg_fluctuation_last_10_days": 54.94
      }
    },
    "volume_analysis": {
      "volume_status": "成交量正常，市场参与度处于常规水平",
      "price_volume_relation": "价量配合，价格与成交量走势配合良好，趋势相对可靠",
      "obv_divergence": "OBV与价格走势一致，验证当前趋势"
    },
    "open_interest_analysis": {
      "status": "持仓量小幅下降，市场参与度下降",
      "signal": "多头平仓",
      "change_rate_last_10_days": -3.96
    },
    "support_resistance_analysis": {
      "current_price": 2412.0,
      "resistance_levels": [
        {
          "type": "最近压力位",
          "price": 2427.0,
          "distance": "0.6%",
          "strength": 23
        },
        {
          "type": "最强压力位",
          "price": 2437.0,
          "distance": "0.9%",
          "strength": 33
        }
      ],
      "support_levels": [
        {
          "type": "最近支撑位",
          "price": 2398.0,
          "distance": "0.6%",
          "strength": 19
        },
        {
          "type": "最强支撑位",
          "price": 2373.0,
          "distance": "1.6%",
          "strength": 91
        }
      ],
      "breakout_analysis": "接近关键压力位2427.0，但成交量不足，突破存疑；接近关键支撑位2398.0，成交量正常，支撑有效"
    },
    "summary": {
      "comprehensive_result": "建议适度做多，趋势较为明确",
      "key_signals": [
        "上涨信号：各周期均线呈完美多头排列，价格位于所有均线之上，上涨趋势非常强劲",
        "突破信号：价格位于布林带中轨附近，处于震荡状态；布林带较窄，可能酝酿突破行情"
      ],
      "risk_warnings": [
        "市场方向不明确，可能出现反复震荡，不适合追涨杀跌",
        "价格接近重要支撑位，需要关注是否会跌破支撑"
      ]
    },
    "disclaimer": "本报告仅供参考，投资有风险，决策需谨慎"
  }
}
```

## 🎯 数据字段详解

### 1. 趋势分析 (trend_analysis)
- **overall_trend**: 整体趋势信息
  - `direction`: 趋势方向 (如: "上升趋势", "下降趋势", "震荡")
  - `strength`: 趋势强度 (0-1之间的数值)
  - `description`: 趋势描述
- **multi_period_trend**: 多周期趋势
  - `short_term`: 短期趋势
  - `medium_term`: 中期趋势
  - `long_term`: 长期趋势

### 2. 动量分析 (momentum_analysis)
- **market_momentum**: 市场动量状态描述
- **consistency_statement**: 一致性说明

### 3. 波动性分析 (volatility_analysis)
- **volatility_level**: 波动性等级 (如: "正常", "较高", "较低")
- **volatility_description**: 波动性描述
- **historical_volatility**: 历史波动率 (数值)
- **atr_volatility**: ATR波动率 (数值)
- **volatility_percentile**: 波动率百分位 (百分比字符串)
- **percentile_interpretation**: 百分位解释
- **risk_control**: 风险控制建议
  - `long_stop_loss`: 做多止损价格
  - `long_take_profit`: 做多止盈价格
  - `short_stop_loss`: 做空止损价格
  - `short_take_profit`: 做空止盈价格
  - `avg_fluctuation_last_10_days`: 近10日平均波动

### 4. 成交量分析 (volume_analysis)
- **volume_status**: 成交量状态
- **price_volume_relation**: 价量关系
- **obv_divergence**: OBV背离情况

### 5. 持仓量分析 (open_interest_analysis)
- **status**: 持仓量状态描述
- **signal**: 市场信号 (如: "多头建仓", "多头平仓", "观望")
- **change_rate_last_10_days**: 近期变化率

### 6. 支撑压力位分析 (support_resistance_analysis)
- **current_price**: 当前价格
- **resistance_levels**: 压力位数组
- **support_levels**: 支撑位数组
- **breakout_analysis**: 突破分析

### 7. 综合总结 (summary)
- **comprehensive_result**: 综合投资建议
- **key_signals**: 关键信号数组
- **risk_warnings**: 风险警告数组

## 🔄 与文本格式对比

### 优势
1. **标准化**: 统一的JSON格式，便于程序化处理
2. **结构化**: 清晰的数据层次结构
3. **可扩展**: 易于添加新的分析字段
4. **集成友好**: 便于与其他系统API集成

### 适用场景
- API集成和系统对接
- 批量分析和自动化处理
- 数据存储和后续分析
- 程序化交易系统集成

## ⚠️ 注意事项

1. **数据格式**: 所有数值保持原始精度，价格保留2位小数
2. **错误处理**: 分析失败时error_id为"0"，data字段可能为空或包含错误信息
3. **请求ID**: 建议使用有意义的请求ID便于追踪和关联
4. **文件命名**: JSON文件按 `analysis_{交易所}_{合约}_{时间戳}.json` 格式命名
