#!/usr/bin/env python3
"""
测试JSON报告生成器
"""

import json
from datetime import datetime
from json_report_generator import JsonReportGenerator

def create_mock_analysis():
    """创建模拟分析数据"""
    return {
        'exchange': 'CFFEX',
        'contract': 'IF2509',
        'timestamp': datetime.now(),
        'current_price': 3918.00,
        'trend': {
            'overall_direction_qualitative': '上升趋势',
            'trend_strength_quantitative': 0.8,
            'short_term': '下降',
            'medium_term': '上升',
            'long_term': '上升',
            'ma_analysis': {
                'status': '完美多头排列'
            }
        },
        'momentum': {
            'overall_momentum_qualitative': '中性',
            'consistency_note': '注意：虽然日K线趋势强度较高(0.8)，但短期动量指标显示中性，可能表明趋势在短期内出现调整或整理'
        },
        'volatility': {
            'level': '正常',
            'historical_volatility': 0.2401,
            'atr_volatility': 0.0228,
            'percentile': 65.6,
            'bollinger_description': '价格位于布林带中轨附近，处于震荡状态；布林带较窄，可能酝酿突破行情',
            'stop_loss_profit': {
                'long_stop_loss': 2304.12,
                'long_take_profit': 2578.82,
                'short_stop_loss': 2523.88,
                'short_take_profit': 2249.18,
                'atr_value': 54.94
            }
        },
        'volume': {
            'minute_analysis': {
                'status': '成交量正常',
                'price_volume_relation': '价量配合，价格与成交量走势配合良好，趋势相对可靠；OBV与价格走势一致，验证当前趋势'
            }
        },
        'open_interest': {
            'change_rate': -3.96,
            'summary': '持仓量显著下降，呈现价跌量减格局'
        },
        'support_resistance': {
            'current_price': 2412.00,
            'resistance_levels': {
                'nearest': {
                    'price': 2427.00,
                    'distance_pct': 0.6,
                    'strength': 23
                },
                'strongest': {
                    'price': 2437.00,
                    'distance_pct': 0.9,
                    'strength': 33
                }
            },
            'support_levels': {
                'nearest': {
                    'price': 2398.00,
                    'distance_pct': 0.6,
                    'strength': 19
                },
                'strongest': {
                    'price': 2373.00,
                    'distance_pct': 1.6,
                    'strength': 91
                }
            },
            'breakthrough_analysis': '接近关键压力位2427.00，但成交量不足，突破存疑；接近关键支撑位2398.00，成交量正常，支撑有效'
        },
        'overall_assessment': {
            'risk_analysis': ['市场方向不明确，可能出现反复震荡']
        }
    }

def test_json_generator():
    """测试JSON报告生成器"""
    print("🧪 测试JSON报告生成器...")
    
    # 创建模拟数据
    analysis = create_mock_analysis()
    
    # 创建JSON报告生成器
    generator = JsonReportGenerator("test_output")
    
    # 生成JSON报告
    report_path = generator.save_json_report(analysis, req_id="test-12345")
    
    print(f"✅ JSON报告已生成: {report_path}")
    
    # 读取并验证JSON文件
    with open(report_path, 'r', encoding='utf-8') as f:
        json_data = json.load(f)
    
    print("📊 JSON报告结构验证:")
    print(f"  func_id: {json_data.get('func_id')}")
    print(f"  req_id: {json_data.get('req_id')}")
    print(f"  error_id: {json_data.get('error_id')}")
    print(f"  contract_code: {json_data['data'].get('contract_code')}")
    print(f"  analysis_time: {json_data['data'].get('analysis_time')}")
    
    # 验证主要字段
    data = json_data['data']
    required_fields = [
        'contract_code', 'analysis_time', 'trend_analysis', 
        'momentum_analysis', 'volatility_analysis', 'volume_analysis',
        'open_interest_analysis', 'support_resistance_analysis', 
        'summary', 'disclaimer'
    ]
    
    print("\n🔍 字段完整性检查:")
    for field in required_fields:
        if field in data:
            print(f"  ✅ {field}")
        else:
            print(f"  ❌ {field} - 缺失")
    
    # 显示部分内容
    print(f"\n📈 趋势分析示例:")
    trend = data.get('trend_analysis', {})
    print(f"  整体趋势: {trend.get('overall_trend', {}).get('direction')}")
    print(f"  趋势强度: {trend.get('overall_trend', {}).get('strength')}")
    
    print(f"\n📊 波动性分析示例:")
    volatility = data.get('volatility_analysis', {})
    print(f"  波动性等级: {volatility.get('volatility_level')}")
    print(f"  历史波动率: {volatility.get('historical_volatility')}")
    
    print(f"\n🎯 综合总结示例:")
    summary = data.get('summary', {})
    print(f"  综合结论: {summary.get('comprehensive_result')}")
    
    return json_data

if __name__ == "__main__":
    test_json_generator()
