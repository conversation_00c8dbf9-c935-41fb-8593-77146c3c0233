#!/usr/bin/env python3
"""
测试一致性检查逻辑
"""

import sys
import os

# 直接导入一致性检查方法，避免依赖问题
def check_and_reconcile_consistency(analysis):
    """检查并调和分析结论的一致性"""
    consistency_notes = []

    # 获取关键数据
    trend_strength = analysis['trend']['trend_strength_quantitative']
    trend_direction = analysis['trend']['overall_direction_qualitative']
    momentum_type = analysis['momentum']['overall_momentum_qualitative']
    short_term = analysis['trend']['short_term']
    medium_term = analysis['trend']['medium_term']
    long_term = analysis['trend']['long_term']

    # 1. 检查趋势强度与动量的一致性
    if trend_strength > 0.7 and '中性' in momentum_type:
        consistency_notes.append(
            f"注意：虽然日K线趋势强度较高({trend_strength:.3f})，但短期动量指标显示中性，"
            "可能表明趋势在短期内出现调整或整理"
        )

    # 2. 检查多周期趋势的一致性
    trends = [short_term, medium_term, long_term]
    unique_trends = set(trends)
    if len(unique_trends) > 1:
        # 趋势不一致
        if len(unique_trends) == 3:
            consistency_notes.append(
                f"多周期趋势存在分歧：短期{short_term}、中期{medium_term}、长期{long_term}，"
                "市场方向不明确，建议谨慎操作"
            )
        elif len(unique_trends) == 2:
            # 找出不一致的周期
            trend_counts = {trend: trends.count(trend) for trend in unique_trends}
            majority_trend = max(trend_counts, key=trend_counts.get)
            minority_trend = min(trend_counts, key=trend_counts.get)

            if trend_counts[majority_trend] == 2:
                consistency_notes.append(
                    f"多周期趋势部分分歧：主要趋势为{majority_trend}，但存在{minority_trend}信号，"
                    "可能处于趋势转换期"
                )

    # 3. 检查趋势方向与强度的一致性
    if '上升' in trend_direction and trend_strength < 0.3:
        consistency_notes.append(
            f"趋势方向显示{trend_direction}，但趋势强度较弱({trend_strength:.3f})，"
            "上涨动力不足，需要关注是否能持续"
        )
    elif '下降' in trend_direction and trend_strength < 0.3:
        consistency_notes.append(
            f"趋势方向显示{trend_direction}，但趋势强度较弱({trend_strength:.3f})，"
            "下跌动力不足，可能出现反弹"
        )

    # 4. 检查价量关系的一致性
    volume_analysis = analysis.get('volume', {})
    minute_pv = volume_analysis.get('minute_analysis', {}).get('price_volume_relation', '')
    if '背离' in minute_pv:
        consistency_notes.append(
            "价量关系出现背离，价格走势与成交量变化不一致，"
            "可能预示趋势即将发生变化，需要密切关注"
        )

    # 合并所有一致性说明
    if consistency_notes:
        analysis['momentum']['consistency_note'] = '；'.join(consistency_notes)
    else:
        analysis['momentum']['consistency_note'] = ''

    return analysis

def test_consistency_scenarios():
    """测试不同的一致性场景"""
    
    print("🧪 测试一致性检查逻辑")
    print("="*50)
    
    # 场景1：多周期趋势分歧
    print("\n📊 场景1：多周期趋势分歧")
    analysis1 = {
        'trend': {
            'trend_strength_quantitative': 0.1,
            'overall_direction_qualitative': '下降趋势',
            'short_term': '上升',
            'medium_term': '下降',
            'long_term': '下降'
        },
        'momentum': {
            'overall_momentum_qualitative': '中性'
        },
        'volume': {
            'minute_analysis': {
                'price_volume_relation': '价量配合'
            }
        }
    }
    
    result1 = engine._check_and_reconcile_consistency(analysis1)
    print(f"一致性说明: {result1['momentum'].get('consistency_note', '无')}")
    
    # 场景2：趋势强度与方向不一致
    print("\n📊 场景2：趋势强度与方向不一致")
    analysis2 = {
        'trend': {
            'trend_strength_quantitative': 0.2,
            'overall_direction_qualitative': '上升趋势',
            'short_term': '上升',
            'medium_term': '上升',
            'long_term': '上升'
        },
        'momentum': {
            'overall_momentum_qualitative': '看多'
        },
        'volume': {
            'minute_analysis': {
                'price_volume_relation': '价量配合'
            }
        }
    }
    
    result2 = engine._check_and_reconcile_consistency(analysis2)
    print(f"一致性说明: {result2['momentum'].get('consistency_note', '无')}")
    
    # 场景3：价量背离
    print("\n📊 场景3：价量背离")
    analysis3 = {
        'trend': {
            'trend_strength_quantitative': 0.6,
            'overall_direction_qualitative': '上升趋势',
            'short_term': '上升',
            'medium_term': '上升',
            'long_term': '上升'
        },
        'momentum': {
            'overall_momentum_qualitative': '看多'
        },
        'volume': {
            'minute_analysis': {
                'price_volume_relation': 'OBV与价格走势背离'
            }
        }
    }
    
    result3 = engine._check_and_reconcile_consistency(analysis3)
    print(f"一致性说明: {result3['momentum'].get('consistency_note', '无')}")
    
    # 场景4：高趋势强度但动量中性
    print("\n📊 场景4：高趋势强度但动量中性")
    analysis4 = {
        'trend': {
            'trend_strength_quantitative': 0.8,
            'overall_direction_qualitative': '上升趋势',
            'short_term': '上升',
            'medium_term': '上升',
            'long_term': '上升'
        },
        'momentum': {
            'overall_momentum_qualitative': '中性'
        },
        'volume': {
            'minute_analysis': {
                'price_volume_relation': '价量配合'
            }
        }
    }
    
    result4 = engine._check_and_reconcile_consistency(analysis4)
    print(f"一致性说明: {result4['momentum'].get('consistency_note', '无')}")
    
    # 场景5：完全一致（无一致性问题）
    print("\n📊 场景5：完全一致")
    analysis5 = {
        'trend': {
            'trend_strength_quantitative': 0.7,
            'overall_direction_qualitative': '上升趋势',
            'short_term': '上升',
            'medium_term': '上升',
            'long_term': '上升'
        },
        'momentum': {
            'overall_momentum_qualitative': '看多'
        },
        'volume': {
            'minute_analysis': {
                'price_volume_relation': '价量配合'
            }
        }
    }
    
    result5 = engine._check_and_reconcile_consistency(analysis5)
    print(f"一致性说明: {result5['momentum'].get('consistency_note', '无')}")

if __name__ == "__main__":
    test_consistency_scenarios()
