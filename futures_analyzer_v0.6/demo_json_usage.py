#!/usr/bin/env python3
"""
演示如何使用JSON格式的期货分析报告
"""

import json
import os
from datetime import datetime

def load_json_report(file_path):
    """加载JSON分析报告"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载JSON文件失败: {e}")
        return None

def extract_key_info(json_data):
    """提取关键信息"""
    if not json_data or json_data.get('error_id') != '1':
        print("❌ 分析失败或数据无效")
        return None
    
    data = json_data['data']
    
    # 提取关键信息
    key_info = {
        'contract': data.get('contract_code'),
        'analysis_time': data.get('analysis_time'),
        'current_price': data.get('support_resistance_analysis', {}).get('current_price'),
        'trend_direction': data.get('trend_analysis', {}).get('overall_trend', {}).get('direction'),
        'trend_strength': data.get('trend_analysis', {}).get('overall_trend', {}).get('strength'),
        'volatility_level': data.get('volatility_analysis', {}).get('volatility_level'),
        'investment_advice': data.get('summary', {}).get('comprehensive_result'),
        'key_signals': data.get('summary', {}).get('key_signals', []),
        'risk_warnings': data.get('summary', {}).get('risk_warnings', [])
    }
    
    return key_info

def format_analysis_summary(key_info):
    """格式化分析摘要"""
    if not key_info:
        return "无法生成分析摘要"
    
    summary = f"""
📊 期货分析摘要 - {key_info['contract']}
{'='*50}
分析时间: {key_info['analysis_time']}
当前价格: {key_info['current_price']}

📈 趋势分析:
  方向: {key_info['trend_direction']}
  强度: {key_info['trend_strength']:.1f}

📊 波动性: {key_info['volatility_level']}

🎯 投资建议: {key_info['investment_advice']}

🔑 关键信号:
"""
    
    for i, signal in enumerate(key_info['key_signals'], 1):
        summary += f"  {i}. {signal}\n"
    
    summary += "\n⚠️ 风险提示:\n"
    for i, warning in enumerate(key_info['risk_warnings'], 1):
        summary += f"  {i}. {warning}\n"
    
    return summary

def demo_json_processing():
    """演示JSON处理流程"""
    print("🚀 期货分析JSON处理演示")
    print("="*50)
    
    # 查找最新的JSON文件
    output_dir = "test_output"
    if not os.path.exists(output_dir):
        print(f"❌ 输出目录 {output_dir} 不存在")
        return
    
    json_files = [f for f in os.listdir(output_dir) if f.endswith('.json')]
    if not json_files:
        print(f"❌ 在 {output_dir} 中未找到JSON文件")
        return
    
    # 使用最新的JSON文件
    latest_file = max(json_files, key=lambda x: os.path.getctime(os.path.join(output_dir, x)))
    file_path = os.path.join(output_dir, latest_file)
    
    print(f"📁 处理文件: {latest_file}")
    
    # 加载JSON数据
    json_data = load_json_report(file_path)
    if not json_data:
        return
    
    print(f"✅ JSON数据加载成功")
    print(f"   func_id: {json_data.get('func_id')}")
    print(f"   req_id: {json_data.get('req_id')}")
    print(f"   error_id: {json_data.get('error_id')}")
    
    # 提取关键信息
    key_info = extract_key_info(json_data)
    if not key_info:
        return
    
    # 显示分析摘要
    summary = format_analysis_summary(key_info)
    print(summary)
    
    # 演示如何访问具体数据
    print("\n🔍 详细数据访问示例:")
    data = json_data['data']
    
    # 访问风险控制数据
    risk_control = data.get('volatility_analysis', {}).get('risk_control', {})
    if risk_control:
        print(f"📊 风险控制建议:")
        print(f"   做多止损: {risk_control.get('long_stop_loss')}")
        print(f"   做多止盈: {risk_control.get('long_take_profit')}")
        print(f"   做空止损: {risk_control.get('short_stop_loss')}")
        print(f"   做空止盈: {risk_control.get('short_take_profit')}")
    
    # 访问支撑压力位数据
    sr_analysis = data.get('support_resistance_analysis', {})
    resistance_levels = sr_analysis.get('resistance_levels', [])
    support_levels = sr_analysis.get('support_levels', [])
    
    if resistance_levels:
        print(f"\n📈 压力位信息:")
        for level in resistance_levels:
            print(f"   {level['type']}: {level['price']} (距离: {level['distance']}, 强度: {level['strength']})")
    
    if support_levels:
        print(f"\n📉 支撑位信息:")
        for level in support_levels:
            print(f"   {level['type']}: {level['price']} (距离: {level['distance']}, 强度: {level['strength']})")

def demo_api_integration():
    """演示API集成示例"""
    print("\n\n🔗 API集成示例")
    print("="*50)
    
    # 模拟API响应处理
    sample_response = {
        "func_id": "3001",
        "req_id": "api-request-123",
        "error_id": "1",
        "data": {
            "contract_code": "IF2509",
            "summary": {
                "comprehensive_result": "建议适度做多，趋势较为明确"
            }
        }
    }
    
    print("📡 模拟API响应处理:")
    print(f"   请求ID: {sample_response['req_id']}")
    print(f"   功能ID: {sample_response['func_id']}")
    print(f"   状态: {'成功' if sample_response['error_id'] == '1' else '失败'}")
    
    if sample_response['error_id'] == '1':
        contract = sample_response['data']['contract_code']
        advice = sample_response['data']['summary']['comprehensive_result']
        print(f"   合约: {contract}")
        print(f"   建议: {advice}")
    
    print("\n💡 集成建议:")
    print("   1. 检查error_id字段确认分析是否成功")
    print("   2. 使用req_id字段追踪请求")
    print("   3. 解析data字段获取具体分析结果")
    print("   4. 根据summary字段获取投资建议")

if __name__ == "__main__":
    demo_json_processing()
    demo_api_integration()
