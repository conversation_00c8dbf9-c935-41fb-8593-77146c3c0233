#!/usr/bin/env python3
"""
OneAPI代理服务 - 简化版
功能：代理OneAPI请求，记录用户对话，提供Web界面
"""

import os
import json
import time
import uuid
import logging
from datetime import datetime
from typing import Dict, Any, Optional

import httpx
import uvicorn
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import JSONResponse, HTMLResponse, Response
from fastapi.middleware.cors import CORSMiddleware

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入数据库用户解析器
try:
    from db_user_resolver import get_user_info_from_database, test_database_connection
    DATABASE_AVAILABLE = True
    logger.info("✅ 数据库用户解析器已加载")
except ImportError as e:
    DATABASE_AVAILABLE = False
    logger.warning(f"⚠️ 数据库用户解析器不可用: {e}")

# 配置
ONEAPI_BASE_URL = os.getenv("ONEAPI_BASE_URL", "http://localhost:3000")
PROXY_PORT = int(os.getenv("PROXY_PORT", "3001"))
LOG_FILE = "conversation_logs.jsonl"

# 初始化FastAPI
app = FastAPI(
    title="OneAPI代理服务",
    description="代理OneAPI请求并记录用户对话",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 用户信息缓存
user_cache: Dict[str, Dict] = {}

# 加载用户映射文件
def load_user_mapping():
    """加载用户映射文件"""
    try:
        if os.path.exists("user-mapping.json"):
            with open("user-mapping.json", "r", encoding="utf-8") as f:
                return json.load(f)
    except Exception as e:
        logger.error(f"加载用户映射文件失败: {e}")
    return {}

# 全局用户映射
user_mapping = load_user_mapping()

def get_web_interface():
    """返回Web界面HTML"""
    return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OneAPI代理服务 - 对话记录</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .status { display: flex; gap: 20px; margin-bottom: 20px; }
        .status-card { flex: 1; padding: 15px; background: #f8f9fa; border-radius: 6px; border-left: 4px solid #007bff; }
        .logs { margin-top: 20px; }
        .log-entry { background: #f8f9fa; margin: 10px 0; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745; }
        .log-meta { color: #666; font-size: 0.9em; margin-bottom: 10px; }
        .log-content { margin: 10px 0; }
        .user-msg { background: #e3f2fd; padding: 8px; margin: 5px 0; border-radius: 4px; }
        .assistant-msg { background: #f3e5f5; padding: 8px; margin: 5px 0; border-radius: 4px; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .error { color: #dc3545; }
        .success { color: #28a745; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 OneAPI代理服务</h1>
            <p>实时监控用户对话记录</p>
        </div>
        
        <div class="status" id="status">
            <div class="status-card">
                <h3>服务状态</h3>
                <p id="proxy-status">检查中...</p>
            </div>
            <div class="status-card">
                <h3>OneAPI连接</h3>
                <p id="oneapi-status">检查中...</p>
            </div>
            <div class="status-card">
                <h3>对话记录</h3>
                <p id="log-count">加载中...</p>
            </div>
        </div>
        
        <div>
            <button class="btn" onclick="refreshLogs()">🔄 刷新日志</button>
            <button class="btn" onclick="clearLogs()">🗑️ 清空日志</button>
            <button class="btn" onclick="downloadLogs()">📥 下载日志</button>
        </div>
        
        <div class="logs">
            <h3>📝 最近对话记录</h3>
            <div id="logs-container">加载中...</div>
        </div>
    </div>

    <script>
        async function checkStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                document.getElementById('proxy-status').innerHTML = 
                    `<span class="success">✅ 运行正常</span>`;
                document.getElementById('oneapi-status').innerHTML = 
                    data.oneapi_connected ? 
                    `<span class="success">✅ 连接正常</span>` : 
                    `<span class="error">❌ 连接失败</span>`;
                document.getElementById('log-count').innerHTML = 
                    `缓存用户: ${data.cached_users}`;
            } catch (error) {
                document.getElementById('proxy-status').innerHTML = 
                    `<span class="error">❌ 服务异常</span>`;
            }
        }
        
        async function loadLogs() {
            try {
                const response = await fetch('/logs?limit=10');
                const data = await response.json();
                const container = document.getElementById('logs-container');
                
                if (data.logs && data.logs.length > 0) {
                    container.innerHTML = data.logs.map(log => `
                        <div class="log-entry">
                            <div class="log-meta">
                                🕒 ${new Date(log.timestamp).toLocaleString()} |
                                👤 ${log.user_info ? log.user_info.username : '未知用户'} |
                                🔑 ${log.metadata.api_key || log.metadata.api_key_hash || '未知Key'} |
                                🤖 ${log.request.model} |
                                ⏱️ ${log.metadata.duration_ms}ms
                            </div>
                            <div class="log-content">
                                ${log.request.messages.map(msg => `
                                    <div class="${msg.role === 'user' ? 'user-msg' : 'assistant-msg'}">
                                        <strong>${msg.role}:</strong> ${msg.content}
                                    </div>
                                `).join('')}
                                ${log.response.choices.map(choice => `
                                    <div class="assistant-msg">
                                        <strong>assistant:</strong> ${choice.message ? choice.message.content : ''}
                                    </div>
                                `).join('')}
                            </div>
                            <div class="log-meta">
                                📊 Tokens: ${log.response.usage ? 
                                    `输入${log.response.usage.prompt_tokens} + 输出${log.response.usage.completion_tokens} = 总计${log.response.usage.total_tokens}` : 
                                    '未知'}
                            </div>
                        </div>
                    `).join('');
                } else {
                    container.innerHTML = '<p>暂无对话记录</p>';
                }
            } catch (error) {
                document.getElementById('logs-container').innerHTML = 
                    '<p class="error">加载日志失败</p>';
            }
        }
        
        function refreshLogs() {
            checkStatus();
            loadLogs();
        }
        
        async function clearLogs() {
            if (confirm('确定要清空所有日志吗？')) {
                try {
                    await fetch('/clear-logs', { method: 'POST' });
                    alert('日志已清空');
                    loadLogs();
                } catch (error) {
                    alert('清空日志失败');
                }
            }
        }
        
        function downloadLogs() {
            window.open('/download-logs', '_blank');
        }
        
        // 初始加载
        checkStatus();
        loadLogs();
        
        // 自动刷新
        setInterval(refreshLogs, 30000);
    </script>
</body>
</html>
    """

async def get_user_info_from_token(api_key: str) -> Optional[Dict]:
    """从OneAPI获取用户信息 - 生产环境版本"""
    if api_key in user_cache:
        return user_cache[api_key]

    # 优先尝试数据库直连方案
    if DATABASE_AVAILABLE:
        try:
            logger.info(f"🔍 尝试从数据库获取用户信息...")
            user_info = await get_user_info_from_database(api_key)
            if user_info:
                user_cache[api_key] = user_info
                logger.info(f"✅ 从数据库获取用户信息成功: {user_info.get('username', 'unknown')}")
                return user_info
            else:
                logger.info(f"⚠️ 数据库中未找到API Key对应的用户")
        except Exception as e:
            logger.error(f"❌ 数据库查询失败: {e}")

    # 备选方案：API查询
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {api_key}"}

            # 方法1: 尝试直接获取当前用户信息
            logger.info(f"尝试获取用户信息...")
            response = await client.get(f"{ONEAPI_BASE_URL}/api/user/self", headers=headers)
            logger.info(f"用户信息API响应: {response.status_code}")

            if response.status_code == 200:
                user_info = response.json()
                logger.info(f"用户信息响应内容: {user_info}")
                if user_info.get("success"):
                    user_data = user_info.get("data", {})
                    user_cache[api_key] = user_data
                    logger.info(f"✅ 从OneAPI获取用户信息: {user_data.get('username', 'unknown')}")
                    return user_data

            # 方法2: 尝试通过不同的API端点获取token信息
            endpoints_to_try = [
                "/api/token/self",
                "/api/token",
                "/api/tokens",
                "/api/user/token"
            ]

            for endpoint in endpoints_to_try:
                logger.info(f"尝试端点: {endpoint}")
                try:
                    response = await client.get(f"{ONEAPI_BASE_URL}{endpoint}", headers=headers)
                    logger.info(f"端点 {endpoint} 响应: {response.status_code}")

                    if response.status_code == 200:
                        token_info = response.json()
                        logger.info(f"Token信息: {token_info}")

                        if token_info.get("success"):
                            # 处理不同的响应格式
                            token_data = token_info.get("data", {})

                            # 如果data是列表，取第一个
                            if isinstance(token_data, list) and len(token_data) > 0:
                                token_data = token_data[0]

                            user_id = token_data.get("user_id")
                            if user_id:
                                # 尝试通过user_id获取用户详细信息
                                user_detail = await get_user_detail_by_id(client, user_id, api_key)
                                if user_detail:
                                    user_cache[api_key] = user_detail
                                    return user_detail

                                # 如果无法获取详细信息，构造基本信息
                                user_data = {
                                    "id": user_id,
                                    "username": f"user_{user_id}",
                                    "email": f"user_{user_id}@oneapi.local",
                                    "token_name": token_data.get("name", "unknown"),
                                    "token_id": token_data.get("id"),
                                    "source": "token_api"
                                }
                                user_cache[api_key] = user_data
                                logger.info(f"✅ 通过token API获取用户信息: user_{user_id}")
                                return user_data

                except Exception as e:
                    logger.info(f"端点 {endpoint} 失败: {e}")
                    continue

            # 方法3: 尝试使用管理员权限查询（如果有的话）
            admin_user_info = await try_admin_user_lookup(client, api_key)
            if admin_user_info:
                user_cache[api_key] = admin_user_info
                return admin_user_info

    except Exception as e:
        logger.error(f"获取用户信息失败: {e}")

    # 最后的备选方案：创建基于API Key的用户信息
    logger.info(f"⚠️ 无法获取用户信息，创建基于API Key的标识")
    user_data = {
        "id": f"api_{api_key[:8]}",
        "username": f"user_{api_key[:8]}",
        "email": f"user_{api_key[:8]}@oneapi.local",
        "api_key_prefix": api_key[:8],
        "source": "generated",
        "note": "无法从OneAPI获取用户信息，基于API Key生成"
    }
    user_cache[api_key] = user_data
    return user_data

async def get_user_detail_by_id(client: httpx.AsyncClient, user_id: str, api_key: str) -> Optional[Dict]:
    """通过用户ID获取用户详细信息"""
    try:
        # 尝试不同的用户详情API端点
        user_endpoints = [
            f"/api/user/{user_id}",
            f"/api/users/{user_id}",
            f"/api/user/detail/{user_id}"
        ]

        headers = {"Authorization": f"Bearer {api_key}"}

        for endpoint in user_endpoints:
            try:
                response = await client.get(f"{ONEAPI_BASE_URL}{endpoint}", headers=headers)
                if response.status_code == 200:
                    user_info = response.json()
                    if user_info.get("success"):
                        user_data = user_info.get("data", {})
                        logger.info(f"✅ 获取到用户详细信息: {user_data.get('username', 'unknown')}")
                        return user_data
            except Exception as e:
                logger.info(f"用户详情端点 {endpoint} 失败: {e}")
                continue

    except Exception as e:
        logger.error(f"获取用户详细信息失败: {e}")

    return None

async def try_admin_user_lookup(client: httpx.AsyncClient, api_key: str) -> Optional[Dict]:
    """尝试使用管理员权限查找用户信息"""
    try:
        # 如果当前token有管理员权限，尝试查询所有token来找到对应的用户
        headers = {"Authorization": f"Bearer {api_key}"}

        admin_endpoints = [
            "/api/token?p=0&size=1000",
            "/api/tokens?p=0&size=1000",
            "/api/admin/token",
            "/api/admin/tokens"
        ]

        for endpoint in admin_endpoints:
            try:
                response = await client.get(f"{ONEAPI_BASE_URL}{endpoint}", headers=headers)
                if response.status_code == 200:
                    tokens_info = response.json()
                    if tokens_info.get("success"):
                        tokens_data = tokens_info.get("data", [])

                        # 查找当前API Key对应的token
                        for token in tokens_data:
                            if token.get("key") == api_key:
                                user_id = token.get("user_id")
                                if user_id:
                                    # 尝试获取用户详细信息
                                    user_detail = await get_user_detail_by_id(client, user_id, api_key)
                                    if user_detail:
                                        logger.info(f"✅ 通过管理员权限获取用户信息: {user_detail.get('username', 'unknown')}")
                                        return user_detail

            except Exception as e:
                logger.info(f"管理员端点 {endpoint} 失败: {e}")
                continue

    except Exception as e:
        logger.error(f"管理员权限查询失败: {e}")

    return None

def log_conversation(user_info: Optional[Dict], request_data: Dict, response_data: Dict, duration_ms: int, api_key: str):
    """记录对话到本地文件"""
    try:
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "trace_id": str(uuid.uuid4()),
            "user_info": user_info,
            "request": {
                "model": request_data.get("model"),
                "messages": request_data.get("messages", []),
                "temperature": request_data.get("temperature"),
                "max_tokens": request_data.get("max_tokens"),
                "stream": request_data.get("stream", False)
            },
            "response": {
                "choices": response_data.get("choices", []),
                "usage": response_data.get("usage", {}),
                "model": response_data.get("model")
            },
            "metadata": {
                "duration_ms": duration_ms,
                "api_key": api_key,  # 显示完整API Key
                "api_key_hash": api_key[:8] + "..." if api_key else "unknown"
            }
        }
        
        with open(LOG_FILE, "a", encoding="utf-8") as f:
            f.write(json.dumps(log_entry, ensure_ascii=False) + "\n")
        
        user_name = user_info.get("username", "unknown") if user_info else "unknown"
        logger.info(f"记录对话 - 用户: {user_name}, 模型: {request_data.get('model')}")
        
    except Exception as e:
        logger.error(f"记录对话失败: {e}")

@app.get("/", response_class=HTMLResponse)
async def web_interface():
    """Web界面"""
    return get_web_interface()

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "oneapi-proxy"}

@app.get("/api/status")
async def proxy_status():
    """代理状态"""
    try:
        # 检查OneAPI连接
        async with httpx.AsyncClient(timeout=5.0) as client:
            response = await client.get(f"{ONEAPI_BASE_URL}/api/status")
            oneapi_status = response.status_code == 200

        # 检查数据库连接
        database_status = False
        if DATABASE_AVAILABLE:
            try:
                database_status = await test_database_connection()
            except Exception as e:
                logger.error(f"数据库状态检查失败: {e}")

        return {
            "proxy_status": "running",
            "oneapi_connected": oneapi_status,
            "database_connected": database_status,
            "database_available": DATABASE_AVAILABLE,
            "oneapi_url": ONEAPI_BASE_URL,
            "log_file": LOG_FILE,
            "cached_users": len(user_cache),
            "user_resolution_method": "database" if DATABASE_AVAILABLE and database_status else "api_fallback"
        }
    except Exception as e:
        return {"error": str(e)}

@app.get("/logs")
async def get_logs(limit: int = 10):
    """获取最近的对话日志"""
    try:
        logs = []
        if os.path.exists(LOG_FILE):
            with open(LOG_FILE, "r", encoding="utf-8") as f:
                lines = f.readlines()
                for line in lines[-limit:]:
                    try:
                        logs.append(json.loads(line.strip()))
                    except:
                        continue
        
        return {"logs": logs, "total": len(logs)}
    except Exception as e:
        return {"error": str(e)}

@app.post("/clear-logs")
async def clear_logs():
    """清空日志"""
    try:
        if os.path.exists(LOG_FILE):
            os.remove(LOG_FILE)
        return {"message": "日志已清空"}
    except Exception as e:
        return {"error": str(e)}

@app.get("/download-logs")
async def download_logs():
    """下载日志文件"""
    try:
        if os.path.exists(LOG_FILE):
            with open(LOG_FILE, "r", encoding="utf-8") as f:
                content = f.read()
            return Response(
                content=content,
                media_type="application/json",
                headers={"Content-Disposition": f"attachment; filename={LOG_FILE}"}
            )
        else:
            return {"error": "日志文件不存在"}
    except Exception as e:
        return {"error": str(e)}

@app.post("/v1/chat/completions")
async def chat_completions_proxy(request: Request):
    """代理聊天完成请求"""
    start_time = time.time()

    try:
        # 获取请求体
        body = await request.body()
        request_data = json.loads(body.decode('utf-8'))

        auth_header = request.headers.get("authorization", "")
        if not auth_header.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Missing authorization header")

        api_key = auth_header[7:]
        user_info = await get_user_info_from_token(api_key)

        async with httpx.AsyncClient(timeout=60.0) as client:
            # 构建新的headers，排除可能有问题的headers
            headers = {
                "Authorization": request.headers.get("authorization"),
                "Content-Type": "application/json",
                "User-Agent": request.headers.get("user-agent", "OneAPI-Proxy/1.0")
            }

            response = await client.post(
                f"{ONEAPI_BASE_URL}/v1/chat/completions",
                json=request_data,
                headers=headers
            )

            duration_ms = int((time.time() - start_time) * 1000)

            if response.status_code == 200:
                response_data = response.json()
                log_conversation(user_info, request_data, response_data, duration_ms, api_key)
                return JSONResponse(content=response_data, status_code=200)
            else:
                # 尝试获取错误响应
                try:
                    error_data = response.json()
                except:
                    error_data = {"error": response.text, "status_code": response.status_code}

                logger.error(f"OneAPI返回错误: {response.status_code} - {error_data}")
                return JSONResponse(
                    content=error_data,
                    status_code=response.status_code
                )

    except json.JSONDecodeError as e:
        logger.error(f"JSON解析失败: {e}")
        raise HTTPException(status_code=400, detail="Invalid JSON in request body")
    except Exception as e:
        logger.error(f"代理请求失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 代理其他API端点
@app.api_route("/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH"])
async def proxy_other_requests(request: Request, path: str):
    """代理其他请求到OneAPI"""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {k: v for k, v in request.headers.items() if k.lower() != "host"}
            url = f"{ONEAPI_BASE_URL}/{path}"

            if request.method == "GET":
                response = await client.get(url, headers=headers, params=request.query_params)
            else:
                body = await request.body()
                response = await client.request(
                    request.method,
                    url,
                    headers=headers,
                    content=body,
                    params=request.query_params
                )

            # 处理响应
            content_type = response.headers.get("content-type", "")
            if content_type.startswith("application/json"):
                try:
                    content = response.json()
                    return JSONResponse(content=content, status_code=response.status_code)
                except:
                    pass

            return Response(
                content=response.content,
                status_code=response.status_code,
                headers=dict(response.headers),
                media_type=content_type
            )

    except Exception as e:
        logger.error(f"代理请求失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    logger.info(f"启动OneAPI代理服务，端口: {PROXY_PORT}")
    logger.info(f"OneAPI地址: {ONEAPI_BASE_URL}")
    logger.info(f"Web界面: http://localhost:{PROXY_PORT}")
    
    uvicorn.run(app, host="0.0.0.0", port=PROXY_PORT, log_level="info")
