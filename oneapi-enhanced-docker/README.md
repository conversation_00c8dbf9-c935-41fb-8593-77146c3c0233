# 🚀 OneAPI Enhanced - 用户对话跟踪系统

这是一个增强版的OneAPI系统，集成了完整的用户对话跟踪功能，能够自动识别用户并记录所有对话数据。

## ✨ 主要功能

- 🔄 **自动用户识别** - 通过数据库直连自动获取用户信息
- 📊 **完整对话记录** - 记录所有聊天请求和响应
- 👥 **多用户支持** - 支持多用户，每个用户可有多个API Key
- 🌐 **实时监控** - Web界面实时查看对话记录和统计
- 🔍 **详细日志** - 包含用户信息、API Key、性能数据
- 🏗️ **生产就绪** - Docker化部署，高性能、高可用

## 📦 系统架构

```
用户/应用 → 代理服务(3001) → OneAPI(3000) → LLM提供商
              ↓
         MySQL数据库 ← 用户信息获取
              ↓
         对话日志文件
```

## 🛠️ 快速部署

### 1. 环境要求

- Docker 20.10+
- Docker Compose 2.0+
- 2GB+ 内存
- 10GB+ 磁盘空间

### 2. 下载和启动

```bash
# 克隆项目
git clone https://github.com/your-repo/oneapi-enhanced.git
cd oneapi-enhanced

# 一键启动
docker-compose up -d
```

### 3. 访问服务

- **OneAPI管理界面**: http://localhost:3000
- **代理服务监控**: http://localhost:3001
- **代理API地址**: http://localhost:3001/v1/chat/completions

### 4. 配置OneAPI

1. 访问 http://localhost:3000
2. 使用默认管理员账号登录
3. 添加LLM提供商渠道
4. 创建用户和API Key

### 5. 使用代理服务

将原来的API请求地址：
```
http://localhost:3000/v1/chat/completions
```

改为代理地址：
```
http://localhost:3001/v1/chat/completions
```

## 📊 监控和日志

### Web监控界面

访问 http://localhost:3001 查看：
- 实时对话记录
- 用户统计信息
- 系统状态监控
- API Key使用情况

### 查看日志

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看代理服务日志
docker-compose logs -f oneapi-proxy

# 查看对话记录
docker exec oneapi-proxy tail -f /app/conversation_logs.jsonl
```

## 🔧 配置说明

### 环境变量

主要配置项（在 `docker-compose.yml` 中）：

```yaml
environment:
  - ONEAPI_BASE_URL=http://oneapi:3000
  - ONEAPI_DB_HOST=mysql
  - ONEAPI_DB_PORT=3306
  - ONEAPI_DB_USER=oneapi
  - ONEAPI_DB_PASSWORD=123456
  - ONEAPI_DB_NAME=one-api
  - PROXY_PORT=3001
  - LOG_LEVEL=INFO
```

### 自定义配置

如需修改配置，编辑 `docker-compose.yml` 文件中的环境变量。

## 🔍 API使用示例

### 发送聊天请求

```bash
curl -X POST http://localhost:3001/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "你好，请介绍一下自己。"}
    ]
  }'
```

### 查看对话记录

```bash
# 查看最近10条记录
curl http://localhost:3001/logs?limit=10

# 查看系统状态
curl http://localhost:3001/api/status
```

## 📈 对话记录格式

每条对话记录包含：

```json
{
  "timestamp": "2025-06-23T10:30:00.000Z",
  "trace_id": "uuid-here",
  "user_info": {
    "id": 2,
    "username": "Henry",
    "email": "<EMAIL>",
    "display_name": "Henry",
    "role": 1,
    "quota": 9999616,
    "used_quota": 384,
    "token_name": "test",
    "source": "database"
  },
  "request": {...},
  "response": {...},
  "metadata": {
    "duration_ms": 1500,
    "api_key": "sk-abc123..."
  }
}
```

## 🛑 停止系统

```bash
# 停止所有服务
docker-compose down

# 停止并删除数据
docker-compose down -v
```

## 🔒 安全建议

1. **修改默认密码**: 修改MySQL密码和OneAPI管理员密码
2. **配置防火墙**: 只开放必要的端口
3. **使用HTTPS**: 配置SSL证书
4. **定期备份**: 备份数据库和日志文件

## 🆘 故障排除

### 常见问题

1. **服务无法启动**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :3000
   netstat -tlnp | grep :3001
   
   # 查看服务日志
   docker-compose logs
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库日志
   docker-compose logs mysql
   ```

3. **代理服务无法获取用户信息**
   ```bash
   # 检查代理服务日志
   docker-compose logs oneapi-proxy
   ```

## 🎉 功能特色

- ✅ **零配置用户识别** - 自动从数据库获取用户信息
- ✅ **完整对话追踪** - 记录所有请求响应和用户信息
- ✅ **实时Web监控** - 友好的监控界面
- ✅ **高性能架构** - 连接池、缓存、异步处理
- ✅ **生产环境就绪** - Docker化部署、健康检查、日志轮转
- ✅ **易于扩展** - 可接入Langfuse等分析平台

这个系统为OneAPI提供了企业级的用户对话跟踪能力，完美适合生产环境部署！
