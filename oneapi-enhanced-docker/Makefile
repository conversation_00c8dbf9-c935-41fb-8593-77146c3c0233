# OneAPI Enhanced Makefile
# 简化常用操作的命令

.PHONY: help build start stop restart logs test clean

# 默认目标
help:
	@echo "OneAPI Enhanced - 可用命令："
	@echo ""
	@echo "  make build    - 构建所有镜像"
	@echo "  make start    - 启动所有服务"
	@echo "  make stop     - 停止所有服务"
	@echo "  make restart  - 重启所有服务"
	@echo "  make logs     - 查看所有服务日志"
	@echo "  make test     - 运行系统测试"
	@echo "  make clean    - 清理所有数据（谨慎使用）"
	@echo ""
	@echo "  make logs-oneapi  - 查看OneAPI服务日志"
	@echo "  make logs-proxy   - 查看代理服务日志"
	@echo "  make logs-mysql   - 查看MySQL服务日志"
	@echo ""
	@echo "  make status   - 查看服务状态"
	@echo "  make ps       - 查看容器状态"

# 检查Docker Compose命令
DOCKER_COMPOSE := $(shell command -v docker-compose 2> /dev/null)
ifndef DOCKER_COMPOSE
	DOCKER_COMPOSE := docker compose
endif

# 构建镜像
build:
	@echo "🔨 构建OneAPI Enhanced镜像..."
	$(DOCKER_COMPOSE) build

# 启动服务
start:
	@echo "🚀 启动OneAPI Enhanced..."
	$(DOCKER_COMPOSE) up -d
	@echo "⏳ 等待服务启动..."
	@sleep 15
	@echo "✅ 服务启动完成"
	@echo ""
	@echo "📋 访问地址："
	@echo "  OneAPI管理: http://localhost:3000"
	@echo "  代理监控: http://localhost:3001"

# 停止服务
stop:
	@echo "🛑 停止OneAPI Enhanced..."
	$(DOCKER_COMPOSE) down

# 重启服务
restart: stop start

# 查看日志
logs:
	$(DOCKER_COMPOSE) logs -f

# 查看特定服务日志
logs-oneapi:
	$(DOCKER_COMPOSE) logs -f oneapi

logs-proxy:
	$(DOCKER_COMPOSE) logs -f oneapi-proxy

logs-mysql:
	$(DOCKER_COMPOSE) logs -f mysql

# 运行测试
test:
	@echo "🧪 运行系统测试..."
	@chmod +x test.sh
	@./test.sh

# 查看服务状态
status:
	@echo "📊 服务状态："
	@curl -s http://localhost:3001/api/status | python3 -c "import sys, json; print(json.dumps(json.load(sys.stdin), indent=2))" 2>/dev/null || echo "代理服务未运行"

# 查看容器状态
ps:
	$(DOCKER_COMPOSE) ps

# 清理数据（谨慎使用）
clean:
	@echo "⚠️ 这将删除所有数据，包括数据库和日志文件"
	@read -p "确定要继续吗？(y/N): " confirm && [ "$$confirm" = "y" ]
	@echo "🗑️ 清理所有数据..."
	$(DOCKER_COMPOSE) down -v
	@rm -f conversation_logs.jsonl
	@echo "✅ 清理完成"

# 开发模式（实时重载）
dev:
	@echo "🔧 启动开发模式..."
	$(DOCKER_COMPOSE) -f docker-compose.yml -f docker-compose.dev.yml up

# 生产模式
prod:
	@echo "🏭 启动生产模式..."
	$(DOCKER_COMPOSE) -f docker-compose.yml -f docker-compose.prod.yml up -d

# 备份数据
backup:
	@echo "💾 备份数据..."
	@mkdir -p backups
	@docker exec mysql mysqldump -u oneapi -p123456 one-api > backups/oneapi_$(shell date +%Y%m%d_%H%M%S).sql
	@cp conversation_logs.jsonl backups/conversation_logs_$(shell date +%Y%m%d_%H%M%S).jsonl 2>/dev/null || true
	@echo "✅ 备份完成，文件保存在 backups/ 目录"

# 更新镜像
update:
	@echo "📦 更新镜像..."
	$(DOCKER_COMPOSE) pull
	$(DOCKER_COMPOSE) build --no-cache
	@echo "✅ 镜像更新完成"
