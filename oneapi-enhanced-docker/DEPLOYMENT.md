# 🚀 OneAPI Enhanced 部署指南

## 📋 系统概述

OneAPI Enhanced 是一个增强版的OneAPI系统，集成了完整的用户对话跟踪功能。

### 🌟 核心功能

- ✅ **自动用户识别** - 直接从OneAPI数据库获取用户信息
- ✅ **完整对话记录** - 记录所有聊天请求和响应
- ✅ **实时Web监控** - 友好的监控界面
- ✅ **多用户支持** - 支持多用户多API Key
- ✅ **Docker化部署** - 一键部署，易于管理

### 📦 系统架构

```
用户/应用 → [Nginx] → 代理服务(3001) → OneAPI(3000) → LLM提供商
                          ↓
                     MySQL数据库 ← 用户信息获取
                          ↓
                     对话日志文件
```

## 🛠️ 部署要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上（推荐8GB）
- **磁盘**: 20GB以上可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Linux (Ubuntu 18.04+, CentOS 7+)
- **Docker**: 20.10+
- **Docker Compose**: 2.0+

## 🚀 快速部署

### 1. 下载项目

```bash
# 方法1: Git克隆（推荐）
git clone https://github.com/your-repo/oneapi-enhanced.git
cd oneapi-enhanced

# 方法2: 下载压缩包
wget https://github.com/your-repo/oneapi-enhanced/archive/main.zip
unzip main.zip
cd oneapi-enhanced-main
```

### 2. 配置环境

```bash
# 复制环境变量配置
cp .env.example .env

# 编辑配置文件（可选）
nano .env
```

### 3. 启动系统

```bash
# 方法1: 使用启动脚本（推荐）
chmod +x start.sh
./start.sh

# 方法2: 使用Makefile
make start

# 方法3: 直接使用Docker Compose
docker-compose up -d
```

### 4. 验证部署

```bash
# 运行系统测试
./test.sh

# 或使用Makefile
make test
```

## 📊 服务访问

部署成功后，可以通过以下地址访问：

| 服务 | 地址 | 说明 |
|------|------|------|
| OneAPI管理界面 | http://localhost:3000 | OneAPI原生管理界面 |
| 代理服务监控 | http://localhost:3001 | 对话记录监控界面 |
| 代理API地址 | http://localhost:3001/v1/chat/completions | 替代原OneAPI地址 |

## 🔧 初始配置

### 1. 配置OneAPI

1. 访问 http://localhost:3000
2. 完成初始化设置
3. 添加LLM提供商渠道（OpenAI、Claude等）
4. 创建用户账号
5. 为用户生成API Key

### 2. 测试代理服务

```bash
# 使用代理地址发送测试请求
curl -X POST http://localhost:3001/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Hello, this is a test."}
    ]
  }'
```

### 3. 查看对话记录

- 访问 http://localhost:3001 查看Web监控界面
- 检查对话记录是否正常显示
- 验证用户信息是否正确识别

## 📈 监控和维护

### 日志管理

```bash
# 查看所有服务日志
make logs

# 查看特定服务日志
make logs-oneapi    # OneAPI服务日志
make logs-proxy     # 代理服务日志
make logs-mysql     # MySQL服务日志

# 查看对话记录
tail -f conversation_logs.jsonl
```

### 系统状态检查

```bash
# 查看系统状态
make status

# 查看容器状态
make ps

# 运行健康检查
make test
```

### 数据备份

```bash
# 备份数据
make backup

# 备份文件位置
ls backups/
```

## 🔒 安全配置

### 1. 修改默认密码

编辑 `.env` 文件，修改以下配置：

```bash
# MySQL密码
MYSQL_ROOT_PASSWORD=your_secure_root_password
MYSQL_PASSWORD=your_secure_password

# OneAPI会话密钥
SESSION_SECRET=your_random_session_secret
```

### 2. 配置防火墙

```bash
# Ubuntu/Debian
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw enable

# CentOS/RHEL
firewall-cmd --permanent --add-port=22/tcp
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp
firewall-cmd --reload
```

### 3. 配置HTTPS（可选）

1. 将SSL证书放在 `nginx/ssl/` 目录
2. 编辑 `nginx/nginx.conf`，启用HTTPS配置
3. 重启服务：`make restart`

## 🔄 更新和维护

### 更新系统

```bash
# 拉取最新代码
git pull

# 更新镜像
make update

# 重启服务
make restart
```

### 清理和重置

```bash
# 重启服务
make restart

# 清理所有数据（谨慎使用）
make clean
```

## 🆘 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :3000
   netstat -tlnp | grep :3001
   
   # 修改端口配置
   nano .env
   ```

2. **服务启动失败**
   ```bash
   # 查看服务日志
   make logs
   
   # 检查容器状态
   make ps
   ```

3. **数据库连接失败**
   ```bash
   # 查看数据库日志
   make logs-mysql
   
   # 检查数据库连接
   docker exec -it mysql mysql -u oneapi -p
   ```

4. **代理服务无法获取用户信息**
   ```bash
   # 查看代理服务日志
   make logs-proxy
   
   # 检查系统状态
   make status
   ```

### 诊断工具

```bash
# 系统信息收集
echo "=== System Info ===" > diagnostic.txt
uname -a >> diagnostic.txt
docker --version >> diagnostic.txt
docker-compose --version >> diagnostic.txt

echo "=== Service Status ===" >> diagnostic.txt
make ps >> diagnostic.txt

echo "=== System Status ===" >> diagnostic.txt
curl -s http://localhost:3001/api/status >> diagnostic.txt

echo "=== Recent Logs ===" >> diagnostic.txt
make logs --tail=50 >> diagnostic.txt
```

## 📞 技术支持

### 获取帮助

1. 查看日志文件定位问题
2. 运行诊断工具收集信息
3. 检查网络和防火墙配置
4. 参考故障排除指南

### 联系支持

如果遇到无法解决的问题，请提供：
- 系统环境信息
- 错误日志
- 诊断报告
- 具体的错误描述

## ✅ 部署检查清单

部署完成后，请确认以下项目：

- [ ] OneAPI主服务正常运行
- [ ] 代理服务正常运行
- [ ] 数据库连接正常
- [ ] Web监控界面可访问
- [ ] 用户信息能够正确识别
- [ ] 对话记录正常保存
- [ ] API请求能够正常处理
- [ ] 防火墙配置正确
- [ ] 备份策略已设置
- [ ] 监控告警已配置

🎉 **恭喜！OneAPI Enhanced 部署完成！**
