version: '3.8'

services:
  # OneAPI主服务
  oneapi:
    image: justsong/one-api:latest
    container_name: oneapi
    restart: always
    ports:
      - '3000:3000'
    volumes:
      - oneapi_data:/data
    environment:
      - SQL_DSN=oneapi:123456@tcp(mysql:3306)/one-api
      - REDIS_CONN_STRING=redis://redis:6379
      - SESSION_SECRET=random_string_here_change_in_production
      - TZ=Asia/Shanghai
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_started
    healthcheck:
      test: ["CMD-SHELL", "wget -q -O - http://localhost:3000/api/status | grep -o '\"success\":\\s*true' | awk -F: '{print $2}'"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # OneAPI代理服务（用户跟踪）
  oneapi-proxy:
    build:
      context: ./proxy
      dockerfile: Dockerfile
    container_name: oneapi-proxy
    restart: always
    ports:
      - '3001:3001'
    volumes:
      - proxy_logs:/app/logs
      - ./conversation_logs.jsonl:/app/conversation_logs.jsonl
    environment:
      - ONEAPI_BASE_URL=http://oneapi:3000
      - ONEAPI_DB_HOST=mysql
      - ONEAPI_DB_PORT=3306
      - ONEAPI_DB_USER=oneapi
      - ONEAPI_DB_PASSWORD=123456
      - ONEAPI_DB_NAME=one-api
      - PROXY_PORT=3001
      - LOG_LEVEL=INFO
      - DB_POOL_MIN_SIZE=1
      - DB_POOL_MAX_SIZE=10
    depends_on:
      oneapi:
        condition: service_healthy
      mysql:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3001/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: redis
    restart: always
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: mysql
    restart: always
    ports:
      - '3306:3306'
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    environment:
      - TZ=Asia/Shanghai
      - MYSQL_ROOT_PASSWORD=OneAPI@root2024
      - MYSQL_USER=oneapi
      - MYSQL_PASSWORD=123456
      - MYSQL_DATABASE=one-api
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "oneapi", "-p123456"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: nginx
    restart: always
    ports:
      - '80:80'
      - '443:443'
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - oneapi
      - oneapi-proxy
    profiles:
      - nginx

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  oneapi_data:
    driver: local
  proxy_logs:
    driver: local

networks:
  default:
    name: oneapi-network
