#!/bin/bash

# OneAPI Enhanced 停止脚本

set -e

echo "🛑 OneAPI Enhanced 停止脚本"
echo "========================="

# 检查Docker Compose
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose未安装"
    exit 1
fi

# 停止服务
echo "🛑 停止OneAPI Enhanced系统..."
if command -v docker-compose &> /dev/null; then
    docker-compose down
else
    docker compose down
fi

echo "✅ OneAPI Enhanced系统已停止"

# 可选：清理数据（谨慎使用）
read -p "是否要清理所有数据？这将删除数据库和日志文件 (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🗑️ 清理数据..."
    if command -v docker-compose &> /dev/null; then
        docker-compose down -v
    else
        docker compose down -v
    fi
    
    # 清理本地文件
    rm -f conversation_logs.jsonl
    
    echo "✅ 数据已清理"
    echo "⚠️ 注意：所有用户数据、对话记录和配置都已删除"
fi
