# OneAPI Enhanced 环境变量配置示例
# 复制此文件为 .env 并根据实际情况修改配置

# ===== 基础配置 =====
# OneAPI服务地址（容器内部通信）
ONEAPI_BASE_URL=http://oneapi:3000

# 代理服务端口
PROXY_PORT=3001

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# ===== 数据库配置 =====
# MySQL数据库配置
MYSQL_ROOT_PASSWORD=OneAPI@root2024
MYSQL_USER=oneapi
MYSQL_PASSWORD=123456
MYSQL_DATABASE=one-api

# OneAPI数据库连接信息（代理服务使用）
ONEAPI_DB_HOST=mysql
ONEAPI_DB_PORT=3306
ONEAPI_DB_USER=oneapi
ONEAPI_DB_PASSWORD=123456
ONEAPI_DB_NAME=one-api

# 数据库连接池配置
DB_POOL_MIN_SIZE=1
DB_POOL_MAX_SIZE=10

# ===== OneAPI配置 =====
# OneAPI会话密钥（生产环境请修改）
SESSION_SECRET=random_string_here_change_in_production

# Redis连接字符串
REDIS_CONN_STRING=redis://redis:6379

# 时区设置
TZ=Asia/Shanghai

# ===== 安全配置 =====
# 允许的来源（CORS）
ALLOWED_ORIGINS=*

# ===== 监控配置 =====
# 健康检查间隔（秒）
HEALTH_CHECK_INTERVAL=30

# 数据库连接超时（秒）
DB_TIMEOUT=10

# ===== 高级配置 =====
# 是否启用详细日志
ENABLE_VERBOSE_LOGGING=false

# 是否启用性能监控
ENABLE_PERFORMANCE_MONITORING=true

# 是否启用用户统计
ENABLE_USER_STATISTICS=true

# ===== 可选：Nginx配置 =====
# 如果使用Nginx反向代理
# NGINX_PORT=80
# NGINX_SSL_PORT=443
# DOMAIN_NAME=your-domain.com

# ===== 可选：SSL证书配置 =====
# SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
# SSL_KEY_PATH=/etc/nginx/ssl/key.pem
