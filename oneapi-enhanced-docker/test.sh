#!/bin/bash

# OneAPI Enhanced 测试脚本

set -e

echo "🧪 OneAPI Enhanced 系统测试"
echo "========================="

# 检查服务是否运行
echo "📊 检查服务状态..."

# 检查Docker容器
echo "🐳 检查Docker容器状态..."
if command -v docker-compose &> /dev/null; then
    docker-compose ps
else
    docker compose ps
fi

echo ""

# 测试OneAPI主服务
echo "🔍 测试OneAPI主服务..."
if curl -s http://localhost:3000/api/status | grep -q "success"; then
    echo "✅ OneAPI主服务正常"
else
    echo "❌ OneAPI主服务异常"
    exit 1
fi

# 测试代理服务健康检查
echo "🔍 测试代理服务健康检查..."
if curl -s http://localhost:3001/health | grep -q "healthy"; then
    echo "✅ 代理服务健康检查正常"
else
    echo "❌ 代理服务健康检查异常"
    exit 1
fi

# 测试代理服务状态
echo "🔍 测试代理服务状态..."
STATUS_RESPONSE=$(curl -s http://localhost:3001/api/status)
if echo "$STATUS_RESPONSE" | grep -q '"proxy_status":"running"'; then
    echo "✅ 代理服务状态正常"
else
    echo "❌ 代理服务状态异常"
    echo "状态响应: $STATUS_RESPONSE"
    exit 1
fi

# 测试数据库连接
echo "🔍 测试数据库连接..."
if echo "$STATUS_RESPONSE" | grep -q '"database_connected":true'; then
    echo "✅ 数据库连接正常"
else
    echo "⚠️ 数据库连接可能有问题"
    echo "状态响应: $STATUS_RESPONSE"
fi

# 测试Web界面
echo "🔍 测试Web监控界面..."
if curl -s http://localhost:3001/ | grep -q "OneAPI"; then
    echo "✅ Web监控界面正常"
else
    echo "⚠️ Web监控界面可能有问题"
fi

echo ""
echo "🎉 基础测试完成！"
echo ""
echo "📋 系统状态详情："
echo "$STATUS_RESPONSE" | python3 -c "import sys, json; print(json.dumps(json.load(sys.stdin), indent=2))" 2>/dev/null || echo "$STATUS_RESPONSE"

echo ""
echo "📝 测试聊天功能："
echo "请先在OneAPI中配置模型和API Key，然后使用以下命令测试："
echo ""
echo "curl -X POST http://localhost:3001/v1/chat/completions \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer YOUR_API_KEY\" \\"
echo "  -d '{"
echo "    \"model\": \"gpt-3.5-turbo\","
echo "    \"messages\": ["
echo "      {\"role\": \"user\", \"content\": \"Hello, this is a test.\"}"
echo "    ]"
echo "  }'"

echo ""
echo "📊 查看对话记录："
echo "curl http://localhost:3001/logs?limit=5"

echo ""
echo "🌐 访问Web界面："
echo "  OneAPI管理: http://localhost:3000"
echo "  代理监控: http://localhost:3001"
