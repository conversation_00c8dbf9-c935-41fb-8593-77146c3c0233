#!/bin/bash

# OneAPI Enhanced 打包脚本
# 用于创建可分发的压缩包

set -e

echo "📦 OneAPI Enhanced 打包脚本"
echo "=========================="

# 定义变量
PACKAGE_NAME="oneapi-enhanced-$(date +%Y%m%d-%H%M%S)"
CURRENT_DIR=$(pwd)

echo "📁 创建打包目录: $PACKAGE_NAME"
mkdir -p "/tmp/$PACKAGE_NAME"

# 复制所有文件
echo "📋 复制项目文件..."
cp -r ./* "/tmp/$PACKAGE_NAME/"

# 清理不需要的文件
echo "🧹 清理临时文件..."
rm -rf "/tmp/$PACKAGE_NAME/node_modules" 2>/dev/null || true
rm -rf "/tmp/$PACKAGE_NAME/.git" 2>/dev/null || true
rm -f "/tmp/$PACKAGE_NAME/.DS_Store" 2>/dev/null || true
rm -f "/tmp/$PACKAGE_NAME/*.log" 2>/dev/null || true
rm -f "/tmp/$PACKAGE_NAME/*.pid" 2>/dev/null || true
rm -f "/tmp/$PACKAGE_NAME/conversation_logs.jsonl" 2>/dev/null || true

# 创建版本信息文件
echo "📝 创建版本信息文件..."
cat > "/tmp/$PACKAGE_NAME/VERSION" << EOF
OneAPI Enhanced
==============
Package: $PACKAGE_NAME
Build Date: $(date)
Build Host: $(hostname)

Features:
- Automatic user identification via database
- Complete conversation tracking
- Real-time web monitoring
- Multi-user support
- Docker-based deployment
EOF

# 创建压缩包
echo "🗜️ 创建压缩包..."
cd /tmp
tar -czf "$PACKAGE_NAME.tar.gz" "$PACKAGE_NAME"

# 移动到当前目录
mv "$PACKAGE_NAME.tar.gz" "$CURRENT_DIR/"

# 清理临时目录
rm -rf "$PACKAGE_NAME"

echo ""
echo "✅ 打包完成！"
echo ""
echo "📦 压缩包: $CURRENT_DIR/$PACKAGE_NAME.tar.gz"
echo "📊 文件大小: $(du -h "$CURRENT_DIR/$PACKAGE_NAME.tar.gz" | cut -f1)"
echo ""
echo "🚀 部署步骤:"
echo "1. 将压缩包上传到服务器"
echo "2. 解压: tar -xzf $PACKAGE_NAME.tar.gz"
echo "3. 进入目录: cd $PACKAGE_NAME"
echo "4. 启动系统: ./start.sh"
echo ""
echo "📖 详细说明请查看 DEPLOYMENT.md 文件"
