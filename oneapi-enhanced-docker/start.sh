#!/bin/bash

# OneAPI Enhanced 启动脚本

set -e

echo "🚀 OneAPI Enhanced 启动脚本"
echo "=========================="

# 检查Docker和Docker Compose
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 检查docker-compose.yml文件
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ 未找到 docker-compose.yml 文件"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p nginx/ssl
mkdir -p mysql/data
mkdir -p logs

# 检查环境变量文件
if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        echo "📝 复制环境变量配置文件..."
        cp .env.example .env
        echo "⚠️ 请编辑 .env 文件，修改生产环境配置"
    else
        echo "⚠️ 未找到环境变量配置文件"
    fi
fi

# 拉取最新镜像
echo "📦 拉取Docker镜像..."
if command -v docker-compose &> /dev/null; then
    docker-compose pull
else
    docker compose pull
fi

# 构建代理服务镜像
echo "🔨 构建OneAPI代理服务镜像..."
if command -v docker-compose &> /dev/null; then
    docker-compose build oneapi-proxy
else
    docker compose build oneapi-proxy
fi

# 启动服务
echo "🚀 启动OneAPI Enhanced系统..."
if command -v docker-compose &> /dev/null; then
    docker-compose up -d
else
    docker compose up -d
fi

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "📊 检查服务状态..."
if command -v docker-compose &> /dev/null; then
    docker-compose ps
else
    docker compose ps
fi

# 健康检查
echo "🏥 执行健康检查..."
sleep 10

# 检查OneAPI主服务
echo "🔍 检查OneAPI主服务..."
if curl -f http://localhost:3000/api/status &> /dev/null; then
    echo "✅ OneAPI主服务运行正常"
else
    echo "❌ OneAPI主服务启动失败，请检查日志"
    if command -v docker-compose &> /dev/null; then
        docker-compose logs oneapi
    else
        docker compose logs oneapi
    fi
fi

# 检查代理服务
echo "🔍 检查代理服务..."
if curl -f http://localhost:3001/health &> /dev/null; then
    echo "✅ OneAPI代理服务运行正常"
else
    echo "❌ OneAPI代理服务启动失败，请检查日志"
    if command -v docker-compose &> /dev/null; then
        docker-compose logs oneapi-proxy
    else
        docker compose logs oneapi-proxy
    fi
fi

echo ""
echo "🎉 OneAPI Enhanced 启动完成！"
echo ""
echo "📋 服务地址："
echo "  OneAPI管理界面: http://localhost:3000"
echo "  代理服务监控: http://localhost:3001"
echo "  代理API地址: http://localhost:3001/v1/chat/completions"
echo ""
echo "🔑 默认管理员信息："
echo "  访问OneAPI管理界面进行初始化设置"
echo ""
echo "📝 使用说明："
echo "  1. 访问 http://localhost:3000 配置OneAPI"
echo "  2. 创建用户和API Key"
echo "  3. 使用代理地址 http://localhost:3001/v1/chat/completions 发送请求"
echo "  4. 在 http://localhost:3001 查看对话记录和用户统计"
echo ""
echo "📊 查看日志："
if command -v docker-compose &> /dev/null; then
    echo "  docker-compose logs -f"
else
    echo "  docker compose logs -f"
fi
echo ""
echo "🛑 停止服务："
echo "  ./stop.sh"
