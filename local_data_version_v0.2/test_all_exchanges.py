#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试所有交易所的数据清洗能力
检查是否对所有交易所文件夹的日线和15分钟K线都能进行异常数据检测和清理
"""

import sys
import os
import pandas as pd
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from local_data_loader import LocalDataLoader

def test_all_exchanges_data_cleaning():
    """测试所有交易所的数据清洗能力"""
    print("🔍 全面测试所有交易所的数据清洗能力")
    print("="*80)
    
    loader = LocalDataLoader()
    
    # 获取所有可用交易所
    exchanges = loader.list_available_exchanges()
    print(f"📋 发现 {len(exchanges)} 个交易所: {exchanges}")
    
    total_stats = {
        'exchanges_tested': 0,
        'contracts_tested': 0,
        'files_tested': 0,
        'total_records': 0,
        'dirty_records_cleaned': 0,
        'invalid_records_removed': 0,
        'successful_files': 0,
        'failed_files': 0
    }
    
    exchange_results = {}
    
    for exchange in exchanges:
        print(f"\n🏢 测试交易所: {exchange}")
        print("-" * 60)
        
        # 获取该交易所的合约
        contracts = loader.list_contracts_by_exchange(exchange)
        print(f"📊 发现 {len(contracts)} 个合约")
        
        exchange_stats = {
            'contracts': len(contracts),
            'files_tested': 0,
            'successful_files': 0,
            'failed_files': 0,
            'total_records': 0,
            'dirty_cleaned': 0,
            'invalid_removed': 0,
            'periods_tested': {'日线': 0, '15分钟线': 0}
        }
        
        # 测试每个合约的不同周期
        for i, contract in enumerate(contracts[:3], 1):  # 只测试前3个合约以节省时间
            print(f"  📈 [{i}/3] 测试合约: {contract}")
            
            # 获取可用周期
            periods = loader.get_available_periods(exchange, contract)
            
            for period in periods:
                print(f"    🕐 测试周期: {period}")
                exchange_stats['files_tested'] += 1
                exchange_stats['periods_tested'][period] += 1
                
                try:
                    # 重置统计
                    loader.cleaning_stats = {
                        'total_processed': 0,
                        'dirty_records_cleaned': 0,
                        'invalid_records_removed': 0
                    }
                    
                    # 加载数据
                    data = loader.load_contract_data(exchange, contract, period)
                    
                    if data is not None and len(data) > 0:
                        # 获取清洗统计
                        stats = loader.get_cleaning_stats()
                        
                        exchange_stats['successful_files'] += 1
                        exchange_stats['total_records'] += stats['total_processed']
                        exchange_stats['dirty_cleaned'] += stats['dirty_records_cleaned']
                        exchange_stats['invalid_removed'] += stats['invalid_records_removed']
                        
                        print(f"      ✅ 成功: {len(data)} 条记录, "
                              f"清理脏数据: {stats['dirty_records_cleaned']}, "
                              f"移除无效: {stats['invalid_records_removed']}")
                        
                        # 检查数据质量
                        quality_issues = check_data_quality(data)
                        if quality_issues:
                            print(f"      ⚠️ 数据质量问题: {quality_issues}")
                    else:
                        exchange_stats['failed_files'] += 1
                        print(f"      ❌ 加载失败或数据为空")
                        
                except Exception as e:
                    exchange_stats['failed_files'] += 1
                    print(f"      ❌ 异常: {str(e)}")
        
        # 汇总交易所统计
        exchange_results[exchange] = exchange_stats
        
        # 更新总统计
        total_stats['exchanges_tested'] += 1
        total_stats['contracts_tested'] += len(contracts)
        total_stats['files_tested'] += exchange_stats['files_tested']
        total_stats['total_records'] += exchange_stats['total_records']
        total_stats['dirty_records_cleaned'] += exchange_stats['dirty_cleaned']
        total_stats['invalid_records_removed'] += exchange_stats['invalid_removed']
        total_stats['successful_files'] += exchange_stats['successful_files']
        total_stats['failed_files'] += exchange_stats['failed_files']
        
        # 显示交易所汇总
        success_rate = (exchange_stats['successful_files'] / exchange_stats['files_tested'] * 100) if exchange_stats['files_tested'] > 0 else 0
        print(f"  📊 {exchange} 汇总: 成功率 {success_rate:.1f}% "
              f"({exchange_stats['successful_files']}/{exchange_stats['files_tested']})")
    
    # 显示总体结果
    print(f"\n🎯 总体测试结果")
    print("="*80)
    
    overall_success_rate = (total_stats['successful_files'] / total_stats['files_tested'] * 100) if total_stats['files_tested'] > 0 else 0
    
    print(f"📈 测试覆盖:")
    print(f"  交易所数量: {total_stats['exchanges_tested']}")
    print(f"  合约数量: {total_stats['contracts_tested']}")
    print(f"  文件数量: {total_stats['files_tested']}")
    print(f"  总成功率: {overall_success_rate:.1f}%")
    
    print(f"\n🧹 数据清洗效果:")
    print(f"  总记录数: {total_stats['total_records']:,}")
    print(f"  清理脏数据: {total_stats['dirty_records_cleaned']:,}")
    print(f"  移除无效记录: {total_stats['invalid_records_removed']:,}")
    
    if total_stats['total_records'] > 0:
        clean_rate = ((total_stats['total_records'] - total_stats['invalid_records_removed']) / total_stats['total_records']) * 100
        print(f"  整体清洗率: {clean_rate:.2f}%")
    
    print(f"\n📋 各交易所详细结果:")
    for exchange, stats in exchange_results.items():
        success_rate = (stats['successful_files'] / stats['files_tested'] * 100) if stats['files_tested'] > 0 else 0
        print(f"  {exchange:6s}: 成功率 {success_rate:5.1f}% | "
              f"日线 {stats['periods_tested']['日线']} | "
              f"15分钟线 {stats['periods_tested']['15分钟线']} | "
              f"脏数据 {stats['dirty_cleaned']} | "
              f"无效数据 {stats['invalid_removed']}")
    
    # 结论
    print(f"\n🏆 结论:")
    if overall_success_rate >= 95:
        print("✅ 优秀！数据清洗系统对所有交易所都有很好的支持")
    elif overall_success_rate >= 80:
        print("✅ 良好！数据清洗系统基本满足所有交易所的需求")
    elif overall_success_rate >= 60:
        print("⚠️ 一般！部分交易所可能需要特殊处理")
    else:
        print("❌ 需要改进！数据清洗系统需要进一步优化")
    
    return exchange_results, total_stats

def check_data_quality(data):
    """检查数据质量"""
    issues = []
    
    try:
        # 检查价格数据
        for idx, row in data.iterrows():
            if row['high'] < row['low']:
                issues.append("价格逻辑错误")
                break
            if row['volume'] < 0:
                issues.append("成交量异常")
                break
        
        # 检查时间连续性（简单检查）
        if len(data) > 1:
            time_gaps = data.index.to_series().diff().dropna()
            if time_gaps.max().days > 10:  # 超过10天的间隔
                issues.append("时间间隔异常")
        
        # 检查数据完整性
        null_counts = data.isnull().sum()
        if null_counts.sum() > 0:
            issues.append("存在空值")
            
    except Exception as e:
        issues.append(f"质量检查异常: {e}")
    
    return issues

if __name__ == "__main__":
    try:
        exchange_results, total_stats = test_all_exchanges_data_cleaning()
        
        # 保存测试结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_file = f"data_cleaning_test_result_{timestamp}.txt"
        
        with open(result_file, 'w', encoding='utf-8') as f:
            f.write("数据清洗能力测试结果\n")
            f.write("="*50 + "\n")
            f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("总体统计:\n")
            for key, value in total_stats.items():
                f.write(f"  {key}: {value}\n")
            
            f.write("\n各交易所详细结果:\n")
            for exchange, stats in exchange_results.items():
                f.write(f"\n{exchange}:\n")
                for key, value in stats.items():
                    f.write(f"  {key}: {value}\n")
        
        print(f"\n📄 详细结果已保存至: {result_file}")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
