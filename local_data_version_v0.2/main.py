#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货技术分析系统 v0.2 主程序
简化版本：直接输入合约代码，自动分析，结果保存到文件
"""

import sys
import os
import logging

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from futures_analyzer import FuturesAnalyzer

# 配置日志 - 只记录错误
logging.basicConfig(
    level=logging.ERROR,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('analysis.log')
    ]
)
logger = logging.getLogger(__name__)

def validate_symbol_input(symbol, analyzer):
    """
    验证合约代码输入

    Args:
        symbol: 用户输入的合约代码
        analyzer: 分析器实例

    Returns:
        tuple: (is_valid, error_message)
    """
    if not symbol or '/' not in symbol:
        return False, "格式错误：请使用 '交易所/合约代码' 格式，如 CFFEX/IF2509"

    try:
        exchange, contract = analyzer.parse_symbol(symbol)

        # 检查交易所是否支持
        supported_exchanges = ['SHFE', 'DCE', 'ZCE', 'CFFEX', 'GFEX']
        if exchange not in supported_exchanges:
            return False, f"不支持的交易所: {exchange}，支持的交易所: {', '.join(supported_exchanges)}"

        # 检查合约是否存在
        if not analyzer.validate_symbol(symbol):
            return False, f"合约 {symbol} 不存在或无可用数据"

        return True, ""

    except Exception as e:
        return False, f"验证失败: {str(e)}"
def main():
    """简化的主程序 - 直接输入合约代码进行分析"""
    print("🚀 期货技术分析系统 v0.2")
    print("📊 自动选择最佳K线周期进行长短期分析")
    print("📁 分析结果保存到文件")
    print("="*60)

    # 创建分析器
    analyzer = FuturesAnalyzer()

    # 获取用户输入
    while True:
        symbol = input("\n请输入合约代码 (格式: 交易所/合约代码，如 CFFEX/IF2509): ").strip().upper()

        if not symbol:
            print("❌ 请输入有效的合约代码")
            continue

        # 验证输入
        is_valid, error_msg = validate_symbol_input(symbol, analyzer)

        if not is_valid:
            print(f"❌ {error_msg}")
            continue

        # 显示合约信息
        try:
            exchange, contract = analyzer.parse_symbol(symbol)
            availability = analyzer.check_data_availability(exchange, contract)

            print(f"\n📊 合约信息:")
            print(f"   交易所: {exchange}")
            print(f"   合约代码: {contract}")
            print(f"   可用数据: {', '.join(availability['available_periods'])}")

        except Exception as e:
            print(f"❌ 获取合约信息失败: {e}")
            continue

        # 开始分析
        print(f"\n🔍 开始分析 {symbol}...")
        print("⏳ 正在进行长短期技术分析，请稍候...")

        # 使用智能周期选择进行分析
        result = analyzer.analyze(symbol, periods_count=200, save_to_file=True)

        if result['success']:
            print(f"\n✅ 分析完成！")
            print(f"📊 分析周期: {', '.join(result['analyzed_periods'])}")
            print(f"📁 结果文件:")
            for file_path in result['saved_files']:
                print(f"   - {file_path}")
        else:
            print(f"\n❌ 分析失败: {result['error']}")

        # 分析完成，程序结束
        print(f"\n🎉 分析完成，程序结束")
        break

if __name__ == "__main__":
    main()
