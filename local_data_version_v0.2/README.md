# 期货技术分析库 v0.2

专业的期货技术分析工具库，提供简化的API接口和强大的分析功能。

## ✨ 主要特性

- 🏢 **多交易所支持**: SHFE, DCE, ZCE, CFFEX, GFEX
- 🧹 **自动数据清洗**: 智能处理脏数据和异常记录
- 📊 **全面技术指标**: RSI, MACD, KDJ, 布林带, ATR等
- 🔍 **智能分析引擎**: 自动选择最佳K线周期
- 📄 **详细报告生成**: 结果保存到文件，不在终端显示
- 📚 **Python库形式**: 方便集成到其他项目

## 🚀 快速开始

### 安装

```bash
# 克隆项目
git clone <repository-url>
cd local_data_version_v0.2

# 安装依赖
pip install -r requirements.txt

# 安装库（可选）
pip install -e .
```

### 基本使用

#### 1. 命令行使用

```bash
python main.py
```

#### 2. Python库使用

```python
from futures_analyzer import FuturesAnalyzer

# 创建分析器
analyzer = FuturesAnalyzer()

# 分析单个合约
result = analyzer.analyze("CFFEX/IF2509")

if result['success']:
    print(f"分析成功！")
    print(f"分析的周期: {result['analyzed_periods']}")
    print(f"保存的文件: {result['saved_files']}")
else:
    print(f"分析失败: {result['error']}")
```

#### 3. 快速分析函数

```python
from local_data_version_v0_2 import quick_analyze

# 快速分析
result = quick_analyze("SHFE/ag2508", periods=200)
```

## 📋 支持的合约格式

合约代码格式：`交易所/合约代码`

### 支持的交易所

- **SHFE** (上海期货交易所): `SHFE/ag2508`, `SHFE/cu2506`
- **DCE** (大连商品交易所): `DCE/m2509`, `DCE/c2509`
- **ZCE** (郑州商品交易所): `ZCE/CF509`, `ZCE/SR509`
- **CFFEX** (中国金融期货交易所): `CFFEX/IF2509`, `CFFEX/IC2509`
- **GFEX** (广州期货交易所): `GFEX/SI2509`

## 📊 分析功能

### 自动周期选择

系统会自动选择最佳的K线周期进行分析：

1. **优先使用日线数据** - 适合长期趋势分析
2. **补充15分钟线数据** - 适合短期交易信号
3. **智能数据量判断** - 根据可用数据自动调整

### 技术指标

- **趋势指标**: 移动平均线(MA), 指数移动平均线(EMA)
- **动量指标**: RSI, MACD, KDJ
- **波动性指标**: 布林带, ATR
- **成交量指标**: OBV, 成交量分析
- **持仓量指标**: 持仓量趋势, 价量关系

### 分析内容

- 📈 趋势分析 (短期/中期/长期)
- ⚡ 动量分析 (超买超卖状态)
- 📊 波动性分析 (风险评估)
- 📦 成交量分析 (资金流向)
- 📊 持仓量分析 (市场情绪)
- 📍 支撑压力位识别
- 🎯 综合评估和建议

## 📁 输出文件

分析结果保存在 `analysis_results` 文件夹中，文件命名格式：

```
analysis_{交易所}_{合约}_{周期}_{时间戳}.txt
```

例如：`analysis_CFFEX_IF2509_日线_20231201_143022.txt`

## 🔧 API 参考

### FuturesAnalyzer 类

```python
class FuturesAnalyzer:
    def __init__(self, data_dir="data_path", output_dir="analysis_results")
    def analyze(self, symbol, periods=200, save_to_file=True)
    def get_available_symbols()
    def validate_symbol(self, symbol)
    def check_data_availability(self, exchange, contract)
```

### 主要方法

#### analyze(symbol, periods=200, save_to_file=True)

分析指定合约的技术指标。

**参数:**
- `symbol` (str): 合约代码，格式为 "交易所/合约代码"
- `periods` (int): 分析的数据周期数，默认200
- `save_to_file` (bool): 是否保存到文件，默认True

**返回:**
```python
{
    'success': True/False,
    'symbol': '合约代码',
    'exchange': '交易所',
    'contract': '合约',
    'analyzed_periods': ['日线', '15分钟线'],
    'saved_files': ['文件路径1', '文件路径2'],
    'error': '错误信息'  # 仅在失败时
}
```

## 📝 更新日志

### v0.2.0 (2023-12-01)

- ✨ 新增简化的输入方式 (交易所/合约代码)
- 🔄 自动选择最佳K线周期
- 📁 结果保存到文件，不在终端显示
- 📚 提供Python库形式的API
- 🧹 增强的数据清洗功能
- 📊 新增持仓量分析

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License
