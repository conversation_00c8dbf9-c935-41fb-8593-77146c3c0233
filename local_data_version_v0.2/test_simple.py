#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from local_data_loader import LocalDataLoader
from local_technical_indicators import LocalTechnicalIndicators

def test_simple():
    """简单测试"""
    print("🧪 简单测试")
    
    # 1. 测试数据加载
    loader = LocalDataLoader()
    data = loader.load_contract_data('CFFEX', 'IF2509', '日线')
    
    if data is not None:
        print(f"✅ 数据加载成功: {len(data)} 条记录")
        print(f"数据列: {list(data.columns)}")
        print(f"最新价格: {data['close'].iloc[-1]}")
        
        # 2. 测试技术指标计算
        calculator = LocalTechnicalIndicators()
        
        try:
            indicators = calculator.calculate_all_indicators(data)
            print(f"✅ 技术指标计算成功")
            print(f"指标类型: {list(indicators.keys())}")
            
            # 检查每个指标
            for key, value in indicators.items():
                if isinstance(value, dict):
                    print(f"  {key}: {list(value.keys())}")
                elif isinstance(value, list):
                    print(f"  {key}: 列表长度 {len(value)}")
                else:
                    print(f"  {key}: {type(value)}")
                    
        except Exception as e:
            print(f"❌ 技术指标计算失败: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("❌ 数据加载失败")

if __name__ == "__main__":
    test_simple()
