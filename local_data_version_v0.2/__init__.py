#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货技术分析库 v0.2

这是一个专门用于期货技术分析的Python库，提供以下功能：

1. 支持多种交易所数据 (SHFE, DCE, ZCE, CFFEX, GFEX)
2. 自动数据清洗和异常处理
3. 全面的技术指标计算
4. 智能分析引擎
5. 详细的分析报告生成

主要类：
- FuturesAnalyzer: 主要分析器，提供简化的API接口

使用示例：
```python
from local_data_version_v0_2 import FuturesAnalyzer

# 创建分析器
analyzer = FuturesAnalyzer()

# 分析单个合约
result = analyzer.analyze("CFFEX/IF2509")

# 检查结果
if result['success']:
    print(f"分析成功，保存的文件: {result['saved_files']}")
else:
    print(f"分析失败: {result['error']}")
```
"""

from .futures_analyzer import FuturesAnalyzer

__version__ = "0.2.0"
__author__ = "Futures Analysis Team"
__email__ = "<EMAIL>"

__all__ = [
    'FuturesAnalyzer'
]

# 版本信息
VERSION_INFO = {
    'major': 0,
    'minor': 2,
    'patch': 0,
    'release': 'stable'
}

def get_version():
    """获取版本信息"""
    return __version__

def get_supported_exchanges():
    """获取支持的交易所列表"""
    return ['SHFE', 'DCE', 'ZCE', 'CFFEX', 'GFEX']

def quick_analyze(symbol, periods=200, data_dir=None, output_dir=None):
    """
    快速分析函数
    
    Args:
        symbol: 合约代码，格式为 "交易所/合约代码"
        periods: 分析周期数
        data_dir: 数据目录路径
        output_dir: 输出目录路径
        
    Returns:
        dict: 分析结果
    """
    kwargs = {}
    if data_dir:
        kwargs['data_dir'] = data_dir
    if output_dir:
        kwargs['output_dir'] = output_dir
    
    analyzer = FuturesAnalyzer(**kwargs)
    return analyzer.analyze(symbol, periods)

# 库信息
LIBRARY_INFO = {
    'name': '期货技术分析库',
    'version': __version__,
    'description': '专业的期货技术分析工具库',
    'features': [
        '多交易所数据支持',
        '自动数据清洗',
        '全面技术指标',
        '智能分析引擎',
        '详细报告生成'
    ],
    'supported_exchanges': get_supported_exchanges(),
    'supported_periods': ['日线', '15分钟线'],
    'output_formats': ['TXT报告文件']
}

def print_library_info():
    """打印库信息"""
    info = LIBRARY_INFO
    print(f"📊 {info['name']} v{info['version']}")
    print(f"📝 {info['description']}")
    print(f"\n✨ 主要功能:")
    for feature in info['features']:
        print(f"  • {feature}")
    print(f"\n🏢 支持的交易所: {', '.join(info['supported_exchanges'])}")
    print(f"📈 支持的周期: {', '.join(info['supported_periods'])}")
    print(f"📄 输出格式: {', '.join(info['output_formats'])}")
