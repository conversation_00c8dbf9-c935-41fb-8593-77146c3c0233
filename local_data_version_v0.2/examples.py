#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货技术分析库 v0.2 使用示例
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from futures_analyzer import FuturesAnalyzer

def example_basic_analysis():
    """基本分析示例"""
    print("📊 基本分析示例")
    print("="*50)
    
    # 创建分析器
    analyzer = FuturesAnalyzer()
    
    # 分析单个合约
    symbol = "CFFEX/IF2509"
    print(f"分析合约: {symbol}")
    
    result = analyzer.analyze(symbol, periods_count=100)
    
    if result['success']:
        print(f"✅ 分析成功！")
        print(f"📊 分析的周期: {', '.join(result['analyzed_periods'])}")
        print(f"📁 保存的文件:")
        for file_path in result['saved_files']:
            print(f"   - {file_path}")
    else:
        print(f"❌ 分析失败: {result['error']}")

def example_batch_analysis():
    """批量分析示例"""
    print("\n📊 批量分析示例")
    print("="*50)
    
    analyzer = FuturesAnalyzer()
    
    # 要分析的合约列表
    symbols = [
        "CFFEX/IF2509",
        "SHFE/ag2508",
        "DCE/m2509"
    ]
    
    results = []
    for symbol in symbols:
        print(f"分析 {symbol}...")
        result = analyzer.analyze(symbol, periods_count=150)
        results.append(result)
        
        if result['success']:
            print(f"  ✅ 成功")
        else:
            print(f"  ❌ 失败: {result['error']}")
    
    # 统计结果
    success_count = sum(1 for r in results if r['success'])
    print(f"\n📈 批量分析完成: {success_count}/{len(symbols)} 成功")

def example_symbol_validation():
    """合约代码验证示例"""
    print("\n🔍 合约代码验证示例")
    print("="*50)
    
    analyzer = FuturesAnalyzer()
    
    # 测试不同的合约代码
    test_symbols = [
        "CFFEX/IF2509",  # 有效
        "SHFE/ag2508",   # 有效
        "INVALID/XX123", # 无效交易所
        "CFFEX/INVALID", # 无效合约
        "CFFEX-IF2509",  # 格式错误
    ]
    
    for symbol in test_symbols:
        is_valid = analyzer.validate_symbol(symbol)
        status = "✅ 有效" if is_valid else "❌ 无效"
        print(f"  {symbol:15s} - {status}")

def example_available_symbols():
    """查看可用合约示例"""
    print("\n📋 查看可用合约示例")
    print("="*50)
    
    analyzer = FuturesAnalyzer()
    
    # 获取所有可用合约
    symbols = analyzer.get_available_symbols()
    
    for exchange, contracts in symbols.items():
        print(f"\n🏢 {exchange} 交易所 ({len(contracts)} 个合约):")
        # 只显示前3个合约
        for symbol in contracts[:3]:
            print(f"   - {symbol}")
        if len(contracts) > 3:
            print(f"   ... 还有 {len(contracts) - 3} 个合约")

def example_data_availability():
    """数据可用性检查示例"""
    print("\n📊 数据可用性检查示例")
    print("="*50)
    
    analyzer = FuturesAnalyzer()
    
    # 检查几个合约的数据可用性
    test_symbols = ["CFFEX/IF2509", "SHFE/ag2508", "DCE/m2509"]
    
    for symbol in test_symbols:
        try:
            exchange, contract = analyzer.parse_symbol(symbol)
            availability = analyzer.check_data_availability(exchange, contract)
            
            print(f"\n📈 {symbol}:")
            print(f"   日线数据: {'✅' if availability['daily'] else '❌'}")
            print(f"   15分钟线: {'✅' if availability['minute15'] else '❌'}")
            print(f"   可用周期: {', '.join(availability['available_periods'])}")
            
        except Exception as e:
            print(f"\n📈 {symbol}: ❌ 错误 - {e}")

def example_custom_settings():
    """自定义设置示例"""
    print("\n⚙️ 自定义设置示例")
    print("="*50)
    
    # 使用自定义数据目录和输出目录
    custom_analyzer = FuturesAnalyzer(
        data_dir="/Users/<USER>/Downloads/data_index_0704",
        output_dir="my_analysis_results"
    )
    
    print("📁 使用自定义设置创建分析器")
    print("   数据目录: /Users/<USER>/Downloads/data_index_0704")
    print("   输出目录: my_analysis_results")
    
    # 分析一个合约
    result = custom_analyzer.analyze("CFFEX/IF2509", periods_count=50)
    
    if result['success']:
        print(f"✅ 分析成功，文件保存到自定义目录")
    else:
        print(f"❌ 分析失败: {result['error']}")

def main():
    """运行所有示例"""
    print("🚀 期货技术分析库 v0.2 使用示例")
    print("="*60)
    
    try:
        # 运行各种示例
        example_available_symbols()
        example_symbol_validation()
        example_data_availability()
        example_basic_analysis()
        example_batch_analysis()
        example_custom_settings()
        
        print("\n🎉 所有示例运行完成！")
        
    except Exception as e:
        print(f"❌ 示例运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
