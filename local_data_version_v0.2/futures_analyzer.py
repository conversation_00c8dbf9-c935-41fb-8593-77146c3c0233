#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货分析库 v0.2
提供简化的API接口，支持直接输入交易所/合约代码进行分析
自动选择合适的K线周期，结果保存到文件
"""

import os
import sys
import logging
from datetime import datetime
from typing import Optional, Dict, Any, Tuple

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from local_data_loader import LocalDataLoader
from local_technical_indicators import LocalTechnicalIndicators
from local_analysis_engine import LocalAnalysisEngine
from local_report_generator import LocalReportGenerator

# 配置日志
logging.basicConfig(level=logging.WARNING, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FuturesAnalyzer:
    """
    期货分析器 v0.2
    
    主要功能：
    1. 支持 交易所/合约代码 格式输入 (如: CFFEX/IF2509)
    2. 自动选择最佳K线周期进行分析
    3. 结果保存到文件，不在终端显示
    4. 提供Python库形式的API
    """
    
    def __init__(self, data_dir: str = "/Users/<USER>/Downloads/data_index_0704", 
                 output_dir: str = "analysis_results"):
        """
        初始化分析器
        
        Args:
            data_dir: 数据目录路径
            output_dir: 输出目录路径
        """
        self.data_loader = LocalDataLoader(data_dir)
        self.indicator_calculator = LocalTechnicalIndicators()
        self.analysis_engine = LocalAnalysisEngine()
        self.report_generator = LocalReportGenerator()
        
        # 设置输出目录
        self.output_dir = output_dir
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 支持的交易所
        self.supported_exchanges = ['SHFE', 'DCE', 'ZCE', 'CFFEX', 'GFEX']
        
        logger.info("期货分析器 v0.2 初始化完成")
    
    def parse_symbol(self, symbol: str) -> Tuple[str, str]:
        """
        解析合约代码
        
        Args:
            symbol: 合约代码，格式为 "交易所/合约代码" 如 "CFFEX/IF2509"
            
        Returns:
            tuple: (交易所, 合约代码)
            
        Raises:
            ValueError: 格式错误时抛出异常
        """
        if '/' not in symbol:
            raise ValueError(f"合约代码格式错误，应为 '交易所/合约代码'，如 'CFFEX/IF2509'，实际输入: {symbol}")
        
        parts = symbol.split('/')
        if len(parts) != 2:
            raise ValueError(f"合约代码格式错误，应为 '交易所/合约代码'，实际输入: {symbol}")
        
        exchange, contract = parts[0].strip().upper(), parts[1].strip()
        
        if exchange not in self.supported_exchanges:
            raise ValueError(f"不支持的交易所: {exchange}，支持的交易所: {self.supported_exchanges}")
        
        return exchange, contract
    
    def check_data_availability(self, exchange: str, contract: str) -> Dict[str, bool]:
        """
        检查数据可用性
        
        Args:
            exchange: 交易所代码
            contract: 合约代码
            
        Returns:
            dict: 各周期数据的可用性
        """
        periods = self.data_loader.get_available_periods(exchange, contract)
        return {
            'daily': '日线' in periods,
            'minute15': '15分钟线' in periods,
            'available_periods': periods
        }
    
    def select_optimal_periods(self, exchange: str, contract: str) -> list:
        """
        选择最佳的分析周期
        
        策略：
        1. 优先使用日线数据进行长期分析
        2. 如果有15分钟线数据，用于短期分析
        3. 根据数据量决定分析策略
        
        Args:
            exchange: 交易所代码
            contract: 合约代码
            
        Returns:
            list: 选择的周期列表
        """
        availability = self.check_data_availability(exchange, contract)
        selected_periods = []
        
        # 优先选择日线数据
        if availability['daily']:
            selected_periods.append('日线')
        
        # 如果有15分钟线数据，也加入分析
        if availability['minute15']:
            selected_periods.append('15分钟线')
        
        if not selected_periods:
            raise ValueError(f"未找到 {exchange}/{contract} 的任何可用数据")
        
        return selected_periods
    
    def analyze_single_period(self, exchange: str, contract: str, period: str, 
                            periods_count: int = 200) -> Optional[Dict[str, Any]]:
        """
        分析单个周期的数据
        
        Args:
            exchange: 交易所代码
            contract: 合约代码
            period: 数据周期
            periods_count: 分析的数据量
            
        Returns:
            dict: 分析结果，失败返回None
        """
        try:
            # 加载数据
            data = self.data_loader.get_latest_data(exchange, contract, period, periods_count)
            
            if data is None or len(data) < 60:
                logger.warning(f"{exchange}/{contract} {period} 数据不足")
                return None
            
            # 计算技术指标
            indicators = self.indicator_calculator.calculate_all_indicators(data)
            
            if indicators is None:
                logger.warning(f"{exchange}/{contract} {period} 技术指标计算失败")
                return None
            
            # 进行分析
            contract_full_name = f"{exchange}.{contract}"
            analysis = self.analysis_engine.generate_comprehensive_analysis(
                indicators, data, contract_full_name, period
            )

            return analysis
            
        except Exception as e:
            logger.error(f"分析 {exchange}/{contract} {period} 时发生错误: {str(e)}")
            return None
    
    def analyze(self, symbol: str, periods_count: int = 200, 
                save_to_file: bool = True) -> Dict[str, Any]:
        """
        主要分析方法
        
        Args:
            symbol: 合约代码，格式为 "交易所/合约代码" 如 "CFFEX/IF2509"
            periods_count: 分析的数据量
            save_to_file: 是否保存到文件
            
        Returns:
            dict: 分析结果和文件路径信息
        """
        try:
            # 解析合约代码
            exchange, contract = self.parse_symbol(symbol)
            
            # 选择最佳分析周期
            selected_periods = self.select_optimal_periods(exchange, contract)
            
            # 执行分析
            results = {}
            analysis_results = {}
            
            for period in selected_periods:
                logger.info(f"分析 {exchange}/{contract} {period} 数据")
                analysis = self.analyze_single_period(exchange, contract, period, periods_count)
                
                if analysis:
                    analysis_results[period] = analysis
                    results[f'{period}_success'] = True
                else:
                    results[f'{period}_success'] = False
            
            if not analysis_results:
                return {
                    'success': False,
                    'error': f'所有周期的数据分析都失败',
                    'symbol': symbol
                }
            
            # 保存结果到文件
            saved_files = []
            if save_to_file:
                for period, analysis in analysis_results.items():
                    file_path = self._save_analysis_to_file(analysis, exchange, contract, period)
                    if file_path:
                        saved_files.append(file_path)
            
            return {
                'success': True,
                'symbol': symbol,
                'exchange': exchange,
                'contract': contract,
                'analyzed_periods': list(analysis_results.keys()),
                'saved_files': saved_files,
                'analysis_results': analysis_results if not save_to_file else None
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'symbol': symbol
            }
    
    def _save_analysis_to_file(self, analysis: Dict[str, Any], exchange: str, 
                              contract: str, period: str) -> Optional[str]:
        """
        保存分析结果到文件
        
        Args:
            analysis: 分析结果
            exchange: 交易所代码
            contract: 合约代码
            period: 数据周期
            
        Returns:
            str: 保存的文件路径，失败返回None
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"analysis_{exchange}_{contract}_{period}_{timestamp}.txt"
            file_path = os.path.join(self.output_dir, filename)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(f"期货技术分析报告 v0.2\n")
                f.write("="*80 + "\n")
                f.write(f"合约代码: {exchange}/{contract}\n")
                f.write(f"数据周期: {period}\n")
                f.write(f"分析时间: {analysis['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"当前价格: {analysis['current_price']:.2f}\n")
                f.write(f"当前成交量: {analysis['current_volume']:,}\n")
                f.write(f"当前持仓量: {analysis['current_open_interest']:,}\n")
                f.write(f"数据范围: {analysis['data_period']}\n")
                f.write(f"数据量: {analysis['total_records']} 条记录\n")
                f.write("="*80 + "\n\n")
                
                # 详细分析内容
                self._write_detailed_analysis(f, analysis)
            
            return file_path

        except Exception as e:
            logger.error(f"保存分析结果失败: {str(e)}")
            return None

    def _write_detailed_analysis(self, f, analysis: Dict[str, Any]):
        """写入详细分析内容"""
        # 趋势分析
        trend = analysis.get('trend', {})
        f.write("📈 趋势分析:\n")
        f.write(f"  整体方向: {trend.get('overall_direction', 'N/A')}\n")
        f.write(f"  短期趋势: {trend.get('short_term', 'N/A')}\n")
        f.write(f"  中期趋势: {trend.get('medium_term', 'N/A')}\n")
        f.write(f"  长期趋势: {trend.get('long_term', 'N/A')}\n")
        f.write(f"  均线状态: {trend.get('ma_alignment', 'N/A')}\n")
        f.write(f"  趋势强度: {trend.get('strength', 'N/A')}\n\n")

        # 动量分析
        momentum = analysis.get('momentum', {})
        f.write("⚡ 动量分析:\n")
        f.write(f"  整体动量: {momentum.get('overall_momentum', 'N/A')}\n")
        if 'rsi' in momentum:
            f.write(f"  RSI: {momentum['rsi'].get('current', 'N/A')}\n")
        if 'kdj' in momentum:
            kdj = momentum['kdj']
            f.write(f"  KDJ: K={kdj.get('k', 'N/A')}, D={kdj.get('d', 'N/A')}, J={kdj.get('j', 'N/A')}\n")
        if 'macd' in momentum:
            f.write(f"  MACD信号: {momentum['macd'].get('signal', 'N/A')}\n")
        f.write("\n")

        # 波动性分析
        volatility = analysis.get('volatility', {})
        f.write("📊 波动性分析:\n")
        f.write(f"  波动性等级: {volatility.get('level', 'N/A')}\n")
        f.write(f"  历史波动率: {volatility.get('historical_volatility', 'N/A')}\n")
        f.write(f"  ATR波动率: {volatility.get('atr_volatility', 'N/A')}\n")
        f.write(f"  布林带位置: {volatility.get('bollinger_position', 'N/A')}\n")
        f.write(f"  交易建议: {volatility.get('trading_suggestion', 'N/A')}\n\n")

        # 成交量分析
        volume = analysis.get('volume', {})
        f.write("📦 成交量分析:\n")
        f.write(f"  成交量状态: {volume.get('status', 'N/A')}\n")
        f.write(f"  价量关系: {volume.get('price_volume_relationship', 'N/A')}\n")
        f.write(f"  OBV状态: {volume.get('obv_status', 'N/A')}\n")
        if volume.get('volume_surge'):
            f.write("  ⚠️ 成交量异常放大\n")
        if volume.get('divergence'):
            f.write("  ⚠️ 价量背离\n")
        f.write("\n")

        # 持仓量分析
        oi_analysis = analysis.get('open_interest', {})
        if oi_analysis and oi_analysis.get('status') != '无持仓量数据':
            f.write("📊 持仓量分析:\n")
            f.write(f"  当前持仓量: {oi_analysis.get('current_oi', 'N/A'):,}\n")

            oi_trend = oi_analysis.get('oi_trend', {})
            f.write(f"  持仓量趋势: {oi_trend.get('direction', 'N/A')}\n")
            f.write(f"  趋势描述: {oi_trend.get('description', 'N/A')}\n")

            price_oi = oi_analysis.get('price_oi_relation', {})
            if 'relation' in price_oi:
                f.write(f"  价量关系: {price_oi['relation']}\n")
                f.write(f"  市场信号: {price_oi['signal']}\n")

            f.write(f"  日变化率: {oi_analysis.get('oi_change_rate', 'N/A'):.2f}%\n\n")

        # 支撑压力位
        sr = analysis.get('support_resistance', {})
        f.write("📍 支撑压力位:\n")
        f.write(f"  当前价格: {analysis.get('current_price', 'N/A')}\n")

        if 'resistance_levels' in sr and sr['resistance_levels']:
            f.write("  关键压力位:\n")
            for i, level in enumerate(sr['resistance_levels'][:3], 1):
                distance = ((level['price'] - analysis['current_price']) / analysis['current_price']) * 100
                f.write(f"    {i}. {level['price']:.2f} (上方 {distance:.1f}%, 强度: {level['strength']})\n")

        if 'support_levels' in sr and sr['support_levels']:
            f.write("  关键支撑位:\n")
            for i, level in enumerate(sr['support_levels'][:3], 1):
                distance = ((analysis['current_price'] - level['price']) / analysis['current_price']) * 100
                f.write(f"    {i}. {level['price']:.2f} (下方 {distance:.1f}%, 强度: {level['strength']})\n")
        f.write("\n")

        # 综合评估
        assessment = analysis.get('overall_assessment', {})
        f.write("🎯 综合评估:\n")
        f.write(f"  综合评分: {assessment.get('score', 'N/A')}/100\n")
        f.write(f"  投资建议: {assessment.get('recommendation', 'N/A')}\n")
        f.write(f"  风险等级: {assessment.get('risk_level', 'N/A')}\n")
        f.write("="*80 + "\n")

    def get_available_symbols(self) -> Dict[str, list]:
        """
        获取所有可用的合约代码

        Returns:
            dict: 按交易所分组的合约列表
        """
        result = {}
        exchanges = self.data_loader.list_available_exchanges()

        for exchange in exchanges:
            contracts = self.data_loader.list_contracts_by_exchange(exchange)
            result[exchange] = [f"{exchange}/{contract}" for contract in contracts]

        return result

    def validate_symbol(self, symbol: str) -> bool:
        """
        验证合约代码是否有效

        Args:
            symbol: 合约代码

        Returns:
            bool: 是否有效
        """
        try:
            exchange, contract = self.parse_symbol(symbol)
            availability = self.check_data_availability(exchange, contract)
            return availability['daily'] or availability['minute15']
        except:
            return False
