#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试期货分析库 v0.2 的库形式使用
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 测试库的导入
try:
    from futures_analyzer import FuturesAnalyzer
    print("✅ 成功导入 FuturesAnalyzer")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

def test_library_usage():
    """测试库的使用"""
    print("🧪 测试期货分析库 v0.2")
    print("="*50)
    
    # 1. 创建分析器
    print("1️⃣ 创建分析器...")
    analyzer = FuturesAnalyzer()
    print("   ✅ 分析器创建成功")
    
    # 2. 获取可用合约
    print("\n2️⃣ 获取可用合约...")
    symbols = analyzer.get_available_symbols()
    total_contracts = sum(len(contracts) for contracts in symbols.values())
    print(f"   ✅ 发现 {len(symbols)} 个交易所，共 {total_contracts} 个合约")
    
    # 3. 验证合约代码
    print("\n3️⃣ 验证合约代码...")
    test_symbol = "CFFEX/IF2509"
    is_valid = analyzer.validate_symbol(test_symbol)
    print(f"   {test_symbol}: {'✅ 有效' if is_valid else '❌ 无效'}")
    
    # 4. 检查数据可用性
    print("\n4️⃣ 检查数据可用性...")
    if is_valid:
        exchange, contract = analyzer.parse_symbol(test_symbol)
        availability = analyzer.check_data_availability(exchange, contract)
        print(f"   日线数据: {'✅' if availability['daily'] else '❌'}")
        print(f"   15分钟线: {'✅' if availability['minute15'] else '❌'}")
    
    # 5. 进行分析
    print("\n5️⃣ 进行技术分析...")
    if is_valid:
        result = analyzer.analyze(test_symbol, periods_count=100)
        
        if result['success']:
            print("   ✅ 分析成功！")
            print(f"   📊 分析周期: {', '.join(result['analyzed_periods'])}")
            print(f"   📁 生成文件: {len(result['saved_files'])} 个")
            for file_path in result['saved_files']:
                print(f"      - {os.path.basename(file_path)}")
        else:
            print(f"   ❌ 分析失败: {result['error']}")
    
    print("\n🎉 库测试完成！")

def test_quick_api():
    """测试快速API"""
    print("\n🚀 测试快速API")
    print("="*30)
    
    # 直接使用分析器
    analyzer = FuturesAnalyzer()
    
    # 快速分析
    symbol = "SHFE/ag2508"
    print(f"快速分析: {symbol}")
    
    result = analyzer.analyze(symbol, periods_count=50)
    
    if result['success']:
        print("✅ 快速分析成功！")
        return True
    else:
        print(f"❌ 快速分析失败: {result['error']}")
        return False

if __name__ == "__main__":
    try:
        test_library_usage()
        test_quick_api()
        
        print("\n🏆 所有测试通过！")
        print("📚 期货分析库 v0.2 可以正常使用")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
