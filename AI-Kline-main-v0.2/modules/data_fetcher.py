import akshare as ak
import pandas as pd
from datetime import datetime, timedelta
import time
import re
from tqdm import tqdm
import requests
import json
import ssl
import urllib3
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class StockDataFetcher:
    """
    期货数据获取类，负责从AKShare获取期货的历史K线数据、合约信息、分钟K线数据和新闻信息
    """
    
    def __init__(self):
        self.today = datetime.now().strftime('%Y%m%d')
    
    def fetch_stock_data(self, stock_code, period='1年'):
        """
        获取期货的历史K线数据，使用多种策略确保数据获取成功

        参数:
            stock_code (str): 期货代码，如 'cu2506,要求输入为小写'
            period (str): 获取数据的时间周期，默认为'1年'

        返回:
            pandas.DataFrame: 包含期货历史数据的DataFrame
        """

        # 计算开始日期
        if period == '1年':
            start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
        elif period == '6个月':
            start_date = (datetime.now() - timedelta(days=183)).strftime('%Y%m%d')
        elif period == '3个月':
            start_date = (datetime.now() - timedelta(days=91)).strftime('%Y%m%d')
        elif period == '1个月':
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        elif period == '1周':
            start_date = (datetime.now() - timedelta(days=7)).strftime('%Y%m%d')
        else:
            start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')

        # 尝试多种数据获取方法
        methods = [
            self._fetch_with_futures_hist_em,
            self._fetch_with_futures_zh_spot,
            self._fetch_with_alternative_source
        ]

        for i, method in enumerate(methods, 1):
            try:
                print(f"尝试方法 {i}: {method.__name__}")
                stock_data = method(stock_code, start_date, self.today)

                if not stock_data.empty:
                    print(f"方法 {i} 成功获取到 {len(stock_data)} 条数据")
                    return stock_data
                else:
                    print(f"方法 {i} 返回空数据")

            except Exception as e:
                print(f"方法 {i} 失败: {e}")
                continue

        print("所有数据获取方法都失败了")
        return pd.DataFrame()

    def _fetch_with_futures_hist_em(self, stock_code, start_date, end_date):
        """使用 futures_hist_em 获取数据"""
        stock_data = ak.futures_hist_em(symbol=stock_code, period="daily",
                                       start_date=start_date, end_date=end_date)
        return self._standardize_columns(stock_data)

    def _fetch_with_futures_zh_spot(self, stock_code, start_date, end_date):
        """使用 futures_zh_spot 获取数据"""
        # 先获取期货合约列表，找到对应的合约
        try:
            # 获取期货合约信息
            contracts = ak.futures_zh_spot()
            contract_info = contracts[contracts['symbol'].str.lower() == stock_code.lower()]

            if not contract_info.empty:
                # 使用 futures_zh_daily_sina 获取历史数据
                stock_data = ak.futures_zh_daily_sina(symbol=stock_code.upper())

                # 过滤日期范围
                stock_data['date'] = pd.to_datetime(stock_data['date'])
                start_dt = pd.to_datetime(start_date)
                end_dt = pd.to_datetime(end_date)
                stock_data = stock_data[(stock_data['date'] >= start_dt) & (stock_data['date'] <= end_dt)]

                return stock_data
        except:
            pass

        return pd.DataFrame()

    def _fetch_with_alternative_source(self, stock_code, start_date, end_date):
        """使用备用数据源获取数据"""
        try:
            # 配置requests会话，处理SSL问题
            session = requests.Session()
            session.verify = False  # 禁用SSL验证

            # 设置更宽松的SSL上下文
            import ssl
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            # 尝试使用不同的User-Agent
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }

            # 临时修改akshare的请求配置
            original_get = requests.get

            def patched_get(*args, **kwargs):
                kwargs.update({
                    'verify': False,
                    'headers': headers,
                    'timeout': 30
                })
                return original_get(*args, **kwargs)

            # 应用补丁
            requests.get = patched_get

            try:
                # 重试 futures_hist_em
                stock_data = ak.futures_hist_em(symbol=stock_code, period="daily",
                                               start_date=start_date, end_date=end_date)
                return self._standardize_columns(stock_data)
            finally:
                # 恢复原始函数
                requests.get = original_get

        except Exception as e:
            print(f"备用方法也失败了: {e}")

        return pd.DataFrame()

    def _standardize_columns(self, stock_data):
        """标准化列名"""
        if stock_data.empty:
            return stock_data

        # 重命名列以便后续处理
        column_mapping = {
            '时间': 'date',
            '开盘': 'open',
            '收盘': 'close',
            '最高': 'high',
            '最低': 'low',
            '成交量': 'volume',
            '成交额': 'amount',
            '涨跌': 'amplitude',
            '涨跌幅': 'pct_change',
            '持仓量': 'position',
            # 英文列名映射
            'date': 'date',
            'open': 'open',
            'close': 'close',
            'high': 'high',
            'low': 'low',
            'volume': 'volume'
        }

        # 只重命名存在的列
        existing_columns = {k: v for k, v in column_mapping.items() if k in stock_data.columns}
        stock_data.rename(columns=existing_columns, inplace=True)

        # 将日期列转换为日期时间格式
        if 'date' in stock_data.columns:
            stock_data['date'] = pd.to_datetime(stock_data['date'])

        return stock_data
    
    def fetch_financial_data(self, stock_code):
        """
        获取合约信息数据
        
        参数:
            stock_code (str): 合约代码，如 'CU2506'
            
        返回:
            dict: 包含数据的字典
        """
        stock_code = stock_code.upper()  # 将合约字母转换为大写
        financial_data = {}
        
        try:
            # 获取合约基本信息
            stock_info = ak.futures_contract_detail(symbol=stock_code)
            if not stock_info.empty:
                financial_data['基本信息'] = stock_info.set_index('item').to_dict()['value']

            return financial_data
            
        except Exception as e:
            print(f"获取合约信息数据时出错: {e}")
            return financial_data
    
    def fetch_news_data(self, stock_code, max_items=10):
        """
        获取与期货相关的新闻信息
        
        参数:
            stock_code (str): 合约信息，如 'cu2506'
            max_items (int): 最大获取新闻条数
            
        返回:
            list: 包含新闻数据的列表
        """
        news_list = []
        stock_code = stock_code.upper()  # 将合约字母转换为大写

        try:
            # 获取合约名称
            stock_info = ak.futures_contract_detail(symbol=stock_code)
            if not stock_info.empty:
                stock_name = stock_info.loc[stock_info['item'] == '交易品种', 'value'].values[0]
                
                # 获取期货相关新闻
                news_data = ak.futures_news_shmet(symbol=stock_name)
                
                if not news_data.empty:
                    # 限制新闻条数，取最新max_items条新闻
                    news_data = news_data.tail(max_items)
                    
                    for index, row in news_data.iterrows():
                        # 获取完整的行数据
                        full_text = str(row) 
                        
                        # 提取时间戳（格式：YYYY-MM-DD HH:MM:SS+08:00）
                        timestamp_match = re.search(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\+\d{2}:\d{2}', full_text)
                        if timestamp_match:
                            timestamp = timestamp_match.group(0)
                            # 获取时间戳之后的内容
                            remaining = full_text[timestamp_match.end():].strip()
                            
                            # 提取标题（在【】中的内容）
                            title_start = remaining.find('【')
                            title_end = remaining.find('】')
                            if title_start != -1 and title_end != -1:
                                title = remaining[title_start+1:title_end]
                                # 提取内容（标题之后的内容）
                                content = remaining[title_end+1:].strip()
                                # 移除可能存在的Pandas技术信息
                                content = re.sub(r'Name: \d+, dtype: object$', '', content).strip()
                                
                                news_item = {
                                    'title': title,
                                    'date': timestamp,
                                    'content': content
                                }
                                news_list.append(news_item)

            return news_list
            
        except Exception as e:
            print(f"获取新闻数据时出错: {e}")
            return news_list

    def fetch_minute_data(self, stock_code):
        """
        获取分钟级别的数据
        """
        try:
            # 添加重试机制
            max_retries = 3
            retry_delay = 2  # 秒
            
            for attempt in range(max_retries):
                try:
                    # 构建URL
                    url = f'https://stock2.finance.sina.com.cn/futures/api/jsonp.php/=/InnerFuturesNewService.getFewMinLine?symbol={stock_code}&type=5'
                    
                    # 设置请求头
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                        'Connection': 'keep-alive'
                    }
                    
                    # 发送请求
                    response = requests.get(url, headers=headers, timeout=10, verify=False)
                    response.raise_for_status()
                    
                    # 解析JSONP响应
                    text = response.text
                    json_str = text[text.index('(') + 1:text.rindex(')')]
                    data = json.loads(json_str)
                    
                    # 转换为DataFrame
                    df = pd.DataFrame(data)
                    if not df.empty:
                        df['date'] = pd.to_datetime(df['d'])
                        df['close'] = pd.to_numeric(df['c'])
                        df['volume'] = pd.to_numeric(df['v'])
                        df['position'] = pd.to_numeric(df['p'])
                        return df
                    
                    return pd.DataFrame()
                    
                except (requests.exceptions.RequestException, json.JSONDecodeError) as e:
                    if attempt < max_retries - 1:
                        print(f"第{attempt + 1}次尝试失败: {str(e)}，{retry_delay}秒后重试...")
                        time.sleep(retry_delay)
                        retry_delay *= 2  # 指数退避
                    else:
                        print(f"获取分钟数据失败，已重试{max_retries}次: {str(e)}")
                        return pd.DataFrame()
                        
        except Exception as e:
            print(f"获取分钟数据时出错: {str(e)}")
            return pd.DataFrame()
