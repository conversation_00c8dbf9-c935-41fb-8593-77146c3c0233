apiVersion: v1
kind: LLMAPIRequester
metadata:
  name: xai-chat-completions
  label:
    en_US: xAI
    zh_Hans: xAI
  icon: xai.svg
spec:
  config:
    - name: base_url
      label:
        en_US: Base URL
        zh_Hans: 基础 URL
      type: string
      required: true
      default: "https://api.x.ai/v1"
    - name: timeout
      label:
        en_US: Timeout
        zh_Hans: 超时时间
      type: integer
      required: true
      default: 120
execution:
  python:
    path: ./xaichatcmpl.py
    attr: XaiChatCompletions
