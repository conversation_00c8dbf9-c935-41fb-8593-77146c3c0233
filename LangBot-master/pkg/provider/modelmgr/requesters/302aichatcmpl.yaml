apiVersion: v1
kind: LLMAPIRequester
metadata:
  name: 302-ai-chat-completions
  label:
    en_US: 302.AI
    zh_Hans: 302.AI
  icon: 302ai.png
spec:
  config:
    - name: base_url
      label:
        en_US: Base URL
        zh_Hans: 基础 URL
      type: string
      required: true
      default: "https://api.302.ai/v1"
    - name: timeout
      label:
        en_US: Timeout
        zh_Hans: 超时时间
      type: integer
      required: true
      default: 120
execution:
  python:
    path: ./302aichatcmpl.py
    attr: AI302ChatCompletions