apiVersion: v1
kind: LLMAPIRequester
metadata:
  name: compshare-chat-completions
  label:
    en_US: CompShare
    zh_Hans: 优云智算
  icon: compshare.png
spec:
  config:
    - name: base_url
      label:
        en_US: Base URL
        zh_Hans: 基础 URL
      type: string
      required: true
      default: "https://api.modelverse.cn/v1"
    - name: timeout
      label:
        en_US: Timeout
        zh_Hans: 超时时间
      type: integer
      required: true
      default: 120
execution:
  python:
    path: ./compsharechatcmpl.py
    attr: CompShareChatCompletions