apiVersion: v1
kind: LLMAPIRequester
metadata:
  name: gemini-chat-completions
  label:
    en_US: Google Gemini
    zh_Hans: Google Gemini
  icon: gemini.svg
spec:
  config:
    - name: base_url
      label:
        en_US: Base URL
        zh_Hans: 基础 URL
      type: string
      required: true
      default: "https://generativelanguage.googleapis.com/v1beta/openai"
    - name: timeout
      label:
        en_US: Timeout
        zh_Hans: 超时时间
      type: integer
      required: true
      default: 120
execution:
  python:
    path: ./geminichatcmpl.py
    attr: GeminiChatCompletions
