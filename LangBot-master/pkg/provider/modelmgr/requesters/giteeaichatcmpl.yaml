apiVersion: v1
kind: LLMAPIRequester
metadata:
  name: gitee-ai-chat-completions
  label:
    en_US: Gitee AI
    zh_Hans: Gitee AI
  icon: giteeai.svg
spec:
  config:
    - name: base_url
      label:
        en_US: Base URL
        zh_Hans: 基础 URL
      type: string
      required: true
      default: "https://ai.gitee.com/v1"
    - name: timeout
      label:
        en_US: Timeout
        zh_Hans: 超时时间
      type: integer
      required: true
      default: 120
execution:
  python:
    path: ./giteeaichatcmpl.py
    attr: GiteeAIChatCompletions