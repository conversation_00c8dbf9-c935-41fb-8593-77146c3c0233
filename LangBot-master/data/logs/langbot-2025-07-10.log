[37m[07-10 15:09:43.593] mgr.py (39) - [INFO] : Initializing database...[0m
[37m[07-10 15:09:43.651] mgr.py (57) - [INFO] : Creating initial metadata...[0m
[37m[07-10 15:09:43.660] mgr.py (71) - [INFO] : Creating default pipeline...[0m
[37m[07-10 15:09:43.667] mgr.py (118) - [INFO] : Migration 1 completed.[0m
[37m[07-10 15:09:43.673] mgr.py (118) - [INFO] : Migration 2 completed.[0m
[37m[07-10 15:09:43.679] mgr.py (118) - [INFO] : Migration 3 completed.[0m
[37m[07-10 15:09:43.680] mgr.py (120) - [INFO] : Successfully upgraded database to version 3.[0m
[37m[07-10 15:09:43.680] manager.py (73) - [INFO] : Loading all plugins...[0m
[37m[07-10 15:09:43.699] modelmgr.py (58) - [INFO] : Loading models from db...[0m
[37m[07-10 15:09:44.009] botmgr.py (200) - [INFO] : Loading bots from db...[0m
[37m[07-10 15:09:44.011] pipelinemgr.py (227) - [INFO] : Loading pipelines from db...[0m
[37m[07-10 15:09:44.020] app.py (185) - [INFO] : =======================================[0m
[37m[07-10 15:09:44.021] app.py (185) - [INFO] : ✨ Access WebUI / 访问管理面板[0m
[37m[07-10 15:09:44.021] app.py (185) - [INFO] : [0m
[37m[07-10 15:09:44.021] app.py (185) - [INFO] : 🏠 Local Address: http://127.0.0.1:5300/[0m
[37m[07-10 15:09:44.022] app.py (185) - [INFO] : 🌐 Public Address: http://<Your Public IP>:5300/[0m
[37m[07-10 15:09:44.022] app.py (185) - [INFO] : [0m
[37m[07-10 15:09:44.022] app.py (185) - [INFO] : 📌 Running this program in a container? Please ensure that the 5300 port is exposed[0m
[37m[07-10 15:09:44.022] app.py (185) - [INFO] : =======================================[0m
