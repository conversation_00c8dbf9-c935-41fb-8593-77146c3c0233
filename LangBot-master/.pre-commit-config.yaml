repos:
  - repo: https://github.com/astral-sh/ruff-pre-commit
    # Ruff version.
    rev: v0.11.7
    hooks:
      # Run the linter of backend.
      - id: ruff
        args: [--fix]
      # Run the formatter of backend.
      - id: ruff-format

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        types_or: [javascript, jsx, ts, tsx, css, scss]
        additional_dependencies:
          - prettier@3.1.0

  - repo: local
    hooks:
      - id: lint-staged
        name: lint-staged
        entry: cd web && pnpm lint-staged
        language: system
        types: [javascript, jsx, ts, tsx]
        pass_filenames: false
