## 概述 / Overview

> 请在此部分填写你实现/解决/优化的内容:  
> Summary of what you implemented/solved/optimized:

## 检查清单 / Checklist

### PR 作者完成 / For PR author

*请在方括号间写`x`以打勾 / Please tick the box with `x`*

- [ ] 阅读仓库[贡献指引](https://github.com/langbot-app/LangBot/blob/master/CONTRIBUTING.md)了吗？ / Have you read the [contribution guide](https://github.com/langbot-app/LangBot/blob/master/CONTRIBUTING.md)?
- [ ] 与项目所有者沟通过了吗？ / Have you communicated with the project maintainer?
- [ ] 我确定已自行测试所作的更改，确保功能符合预期。 / I have tested the changes and ensured they work as expected.

### 项目维护者完成 / For project maintainer

- [ ] 相关 issues 链接了吗？ / Have you linked the related issues?
- [ ] 配置项写好了吗？迁移写好了吗？生效了吗？ / Have you written the configuration items? Have you written the migration? Has it taken effect?
- [ ] 依赖加到 pyproject.toml 和 core/bootutils/deps.py 了吗 / Have you added the dependencies to pyproject.toml and core/bootutils/deps.py?
- [ ] 文档编写了吗？ / Have you written the documentation?