name: 漏洞反馈
description: 【供中文用户】报错或漏洞请使用这个模板创建，不使用此模板创建的异常、漏洞相关issue将被直接关闭。由于自己操作不当/不甚了解所用技术栈引起的网络连接问题恕无法解决，请勿提 issue。容器间网络连接问题，参考文档 https://docs.langbot.app/zh/workshop/network-details.html  
title: "[Bug]: "
labels: ["bug?"]
body:
  - type: input
    attributes:
      label: 运行环境
      description: LangBot 版本、操作系统、系统架构、**Python版本**、**主机地理位置**
      placeholder: 例如：v3.3.0、CentOS x64 Python 3.10.3、Docker
    validations:
      required: true
  - type: textarea
    attributes:
      label: 异常情况
      description: 完整描述异常情况，什么时候发生的、发生了什么。**请附带日志信息。** 
    validations:
      required: true
  - type: textarea
    attributes:
      label: 复现步骤
      description: 提供越多信息，我们会越快解决问题，建议多提供配置截图；**如果你不认真填写（只一两句话概括），我们会很生气并且立即关闭 issue 或两年后才回复你**
    validations:
      required: false
  - type: textarea
    attributes:
      label: 启用的插件
      description: 有些情况可能和插件功能有关，建议提供插件启用情况。
    validations:
      required: false
