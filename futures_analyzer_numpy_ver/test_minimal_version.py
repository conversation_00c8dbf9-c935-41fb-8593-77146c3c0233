#!/usr/bin/env python3
"""
测试NumPy最小版本的功能
"""

import os
import sys
import json
from datetime import datetime

def test_imports():
    """测试所有必需模块的导入"""
    print("🔧 测试模块导入...")
    
    try:
        import numpy as np
        print("  ✅ numpy 导入成功")
    except ImportError as e:
        print(f"  ❌ numpy 导入失败: {e}")
        return False
    
    try:
        import scipy
        print("  ✅ scipy 导入成功")
    except ImportError as e:
        print(f"  ❌ scipy 导入失败: {e}")
        return False
    
    try:
        import pandas as pd
        print("  ✅ pandas 导入成功")
    except ImportError as e:
        print(f"  ❌ pandas 导入失败: {e}")
        return False
    
    try:
        import requests
        print("  ✅ requests 导入成功")
    except ImportError as e:
        print(f"  ❌ requests 导入失败: {e}")
        return False
    
    return True

def test_core_modules():
    """测试核心模块导入"""
    print("\n🔧 测试核心模块导入...")
    
    try:
        from remote_data_loader import RemoteDataLoader
        print("  ✅ RemoteDataLoader 导入成功")
    except ImportError as e:
        print(f"  ❌ RemoteDataLoader 导入失败: {e}")
        return False
    
    try:
        from numpy_technical_indicators import NumpyTechnicalIndicators
        print("  ✅ NumpyTechnicalIndicators 导入成功")
    except ImportError as e:
        print(f"  ❌ NumpyTechnicalIndicators 导入失败: {e}")
        return False
    
    try:
        from enhanced_analysis_engine import EnhancedAnalysisEngine
        print("  ✅ EnhancedAnalysisEngine 导入成功")
    except ImportError as e:
        print(f"  ❌ EnhancedAnalysisEngine 导入失败: {e}")
        return False
    
    try:
        from json_report_generator import JsonReportGenerator
        print("  ✅ JsonReportGenerator 导入成功")
    except ImportError as e:
        print(f"  ❌ JsonReportGenerator 导入失败: {e}")
        return False
    
    return True

def test_data_connection():
    """测试数据连接"""
    print("\n🌐 测试数据连接...")
    
    try:
        from remote_data_loader import RemoteDataLoader
        loader = RemoteDataLoader()
        
        if loader.test_connection():
            print("  ✅ 数据服务器连接成功")
            return True
        else:
            print("  ❌ 数据服务器连接失败")
            return False
    except Exception as e:
        print(f"  ❌ 数据连接测试失败: {e}")
        return False

def test_json_generation():
    """测试JSON报告生成"""
    print("\n📊 测试JSON报告生成...")
    
    try:
        from json_report_generator import JsonReportGenerator
        
        # 创建模拟数据
        mock_analysis = {
            'exchange': 'CFFEX',
            'contract': 'IF2509',
            'timestamp': datetime.now(),
            'current_price': 3918.00,
            'trend': {
                'overall_direction_qualitative': '上升趋势',
                'trend_strength_quantitative': 0.8,
                'short_term': '上升',
                'medium_term': '上升',
                'long_term': '上升',
                'ma_analysis': {'status': '多头排列'}
            },
            'momentum': {
                'overall_momentum_qualitative': '看多',
                'consistency_note': ''
            },
            'volatility': {
                'level': '正常',
                'historical_volatility': 0.24,
                'atr_volatility': 0.023,
                'percentile': 65.6,
                'stop_loss_profit': {
                    'long_stop_loss': 3800.0,
                    'long_take_profit': 4000.0,
                    'short_stop_loss': 4000.0,
                    'short_take_profit': 3800.0,
                    'atr_value': 50.0
                }
            },
            'volume': {
                'minute_analysis': {
                    'status': '成交量正常',
                    'price_volume_relation': '价量配合'
                }
            },
            'open_interest': {
                'change_rate': 2.5,
                'summary': '持仓量稳定'
            },
            'support_resistance': {
                'current_price': 3918.0,
                'resistance_levels': {
                    'nearest': {'price': 3950.0, 'distance_pct': 0.8, 'strength': 25},
                    'strongest': {'price': 4000.0, 'distance_pct': 2.1, 'strength': 45}
                },
                'support_levels': {
                    'nearest': {'price': 3880.0, 'distance_pct': 1.0, 'strength': 20},
                    'strongest': {'price': 3800.0, 'distance_pct': 3.0, 'strength': 60}
                }
            }
        }
        
        # 生成JSON报告
        generator = JsonReportGenerator("test_output")
        report_path = generator.save_json_report(mock_analysis, "test-minimal-version")
        
        # 验证JSON文件
        if os.path.exists(report_path):
            with open(report_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            if json_data.get('func_id') == '3001' and json_data.get('error_id') == '1':
                print("  ✅ JSON报告生成成功")
                print(f"     文件路径: {report_path}")
                return True
            else:
                print("  ❌ JSON格式验证失败")
                return False
        else:
            print("  ❌ JSON文件未生成")
            return False
            
    except Exception as e:
        print(f"  ❌ JSON报告生成失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 NumPy最小版本功能测试")
    print("="*50)
    
    # 测试结果
    results = []
    
    # 1. 测试依赖导入
    results.append(("依赖包导入", test_imports()))
    
    # 2. 测试核心模块
    results.append(("核心模块导入", test_core_modules()))
    
    # 3. 测试数据连接
    results.append(("数据连接", test_data_connection()))
    
    # 4. 测试JSON生成
    results.append(("JSON报告生成", test_json_generation()))
    
    # 显示测试结果
    print("\n📋 测试结果汇总")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！NumPy最小版本可以正常使用。")
        print("\n🚀 使用方法:")
        print("python analyzer_numpy_json.py CFFEX/IF2509 --req-id 'test-123'")
    else:
        print(f"\n⚠️ 有 {total - passed} 项测试失败，请检查环境配置。")
        
        if not results[0][1]:  # 依赖包导入失败
            print("\n💡 安装依赖包:")
            print("pip install numpy scipy pandas requests")

if __name__ == "__main__":
    main()
