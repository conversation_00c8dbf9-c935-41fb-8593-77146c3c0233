#!/usr/bin/env python3
"""
快速安装和测试脚本
"""

import subprocess
import sys
import os

def install_dependencies():
    """安装依赖包"""
    print("📦 安装依赖包...")
    
    try:
        # 读取requirements.txt
        with open('requirements.txt', 'r') as f:
            requirements = f.read().strip()
        
        print("需要安装的包:")
        for line in requirements.split('\n'):
            if line.strip() and not line.startswith('#'):
                print(f"  - {line.strip()}")
        
        # 安装依赖
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 依赖包安装成功!")
            return True
        else:
            print("❌ 依赖包安装失败!")
            print(f"错误信息: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 安装过程出错: {e}")
        return False

def run_tests():
    """运行测试"""
    print("\n🧪 运行功能测试...")
    
    try:
        result = subprocess.run([
            sys.executable, 'test_minimal_version.py'
        ], capture_output=True, text=True)
        
        print(result.stdout)
        
        if result.returncode == 0:
            print("✅ 测试完成!")
            return True
        else:
            print("❌ 测试失败!")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        return False

def run_quick_analysis():
    """运行快速分析示例"""
    print("\n🚀 运行快速分析示例...")
    
    try:
        # 使用CFFEX/IF2509作为示例
        result = subprocess.run([
            sys.executable, 'analyzer_numpy_json.py', 'CFFEX/IF2509', 
            '--req-id', 'setup-test', '--verbose'
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ 分析示例运行成功!")
            print("\n📄 输出摘要:")
            # 只显示关键输出行
            lines = result.stdout.split('\n')
            for line in lines:
                if any(keyword in line for keyword in ['✅', '📊', '📁', '分析完成']):
                    print(f"  {line}")
            return True
        else:
            print("❌ 分析示例运行失败!")
            print(f"错误信息: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 分析超时，可能是网络连接问题")
        return False
    except Exception as e:
        print(f"❌ 分析过程出错: {e}")
        return False

def main():
    """主函数"""
    print("🎯 期货技术分析系统 - NumPy最小版本")
    print("🔧 快速安装和测试脚本")
    print("="*60)
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
        print("❌ 需要Python 3.7或更高版本")
        print(f"当前版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        return
    
    print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 步骤1: 安装依赖
    if not install_dependencies():
        print("\n❌ 安装失败，请手动安装依赖:")
        print("pip install numpy scipy pandas requests")
        return
    
    # 步骤2: 运行测试
    if not run_tests():
        print("\n❌ 测试失败，请检查环境配置")
        return
    
    # 步骤3: 运行分析示例
    print("\n" + "="*60)
    choice = input("是否运行分析示例? (y/n): ").strip().lower()
    
    if choice in ['y', 'yes', '是']:
        if run_quick_analysis():
            print("\n🎉 所有步骤完成!")
            print("\n📖 使用方法:")
            print("python analyzer_numpy_json.py CFFEX/IF2509")
            print("python example_usage.py  # 交互式示例")
        else:
            print("\n⚠️ 分析示例失败，但基本功能可用")
    else:
        print("\n✅ 安装和测试完成!")
        print("\n📖 使用方法:")
        print("python analyzer_numpy_json.py CFFEX/IF2509")
        print("python example_usage.py  # 交互式示例")

if __name__ == "__main__":
    main()
