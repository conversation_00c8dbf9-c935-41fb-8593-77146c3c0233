"""
基于NumPy/SciPy的技术指标计算器
"""

import pandas as pd
import numpy as np
from scipy import stats
from scipy.signal import argrelextrema
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class NumpyTechnicalIndicators:
    """基于NumPy/SciPy的技术指标计算器"""
    
    def __init__(self):
        """初始化NumPy指标计算器"""
        self.indicators = {}
    
    def calculate_all_indicators(self, data):
        """计算所有技术指标 - 返回与本地版本兼容的结构"""
        try:
            if data is None or len(data) < 60:
                logger.error("数据不足，无法计算技术指标")
                return None

            # 准备价格数据
            high = data['high'].values.astype(float)
            low = data['low'].values.astype(float)
            close = data['close'].values.astype(float)
            open_price = data['open'].values.astype(float)
            volume = data['volume'].values.astype(float)

            # 计算各类指标
            trend_indicators = self._calculate_trend_indicators(high, low, close, open_price)
            momentum_indicators = self._calculate_momentum_indicators(high, low, close, volume)
            volatility_indicators = self._calculate_volatility_indicators(high, low, close)
            volume_indicators = self._calculate_volume_indicators(close, volume)
            sr_indicators = self._calculate_support_resistance(high, low, close)

            # 返回平铺结构以兼容现有分析引擎
            indicators = {}

            # 趋势指标
            indicators['ma20'] = trend_indicators.get('sma_20')
            indicators['ma60'] = trend_indicators.get('sma_60')
            indicators['ema20'] = trend_indicators.get('ema_12')  # 使用EMA12作为EMA20的替代
            indicators['trend'] = {
                'overall_direction': trend_indicators.get('overall_direction'),
                'trend_strength': trend_indicators.get('trend_strength'),
                'short_trend': trend_indicators.get('short_trend'),
                'medium_trend': trend_indicators.get('medium_trend'),
                'long_trend': trend_indicators.get('long_trend')
            }

            # 动量指标
            indicators['rsi'] = momentum_indicators.get('rsi')
            indicators['kdj'] = momentum_indicators.get('kdj')
            indicators['macd'] = {
                'macd': momentum_indicators.get('macd'),
                'signal': momentum_indicators.get('macd_signal'),
                'histogram': momentum_indicators.get('macd_hist')
            }
            indicators['momentum'] = {
                'momentum': momentum_indicators.get('momentum'),
                'roc': momentum_indicators.get('roc'),
                'signal': momentum_indicators.get('momentum_signal')
            }

            # 波动性指标
            indicators['bollinger'] = volatility_indicators.get('bollinger')
            indicators['atr'] = volatility_indicators.get('atr')
            indicators['volatility'] = {
                'historical_volatility': volatility_indicators.get('historical_volatility'),
                'atr_volatility': volatility_indicators.get('atr_volatility'),
                'volatility_level': volatility_indicators.get('volatility_level'),
                'volatility_percentile': volatility_indicators.get('volatility_percentile')
            }

            # 成交量指标
            indicators['obv'] = volume_indicators.get('obv')
            indicators['volume'] = {
                'obv': volume_indicators.get('obv'),
                'volume_ma': volume_indicators.get('volume_ma'),
                'volume_ratio': volume_indicators.get('volume_ratio')
            }

            # 支撑压力位
            indicators['support_resistance'] = sr_indicators

            logger.info("NumPy技术指标计算完成")
            return indicators

        except Exception as e:
            logger.error(f"NumPy指标计算失败: {str(e)}")
            return None
    
    def _calculate_trend_indicators(self, high, low, close, open_price):
        """计算趋势指标"""
        try:
            # 移动平均线
            sma_5 = self._sma(close, 5)
            sma_10 = self._sma(close, 10)
            sma_20 = self._sma(close, 20)
            sma_60 = self._sma(close, 60)
            
            # 指数移动平均线
            ema_12 = self._ema(close, 12)
            ema_26 = self._ema(close, 26)
            
            # 趋势强度计算（使用线性回归）
            short_trend = self._calculate_trend_direction(close[-5:])
            medium_trend = self._calculate_trend_direction(close[-20:])
            long_trend = self._calculate_trend_direction(close[-60:])
            
            # 整体趋势方向
            overall_direction = self._determine_overall_direction(short_trend, medium_trend, long_trend)
            
            # 趋势强度
            trend_strength = abs(np.corrcoef(np.arange(len(close[-20:])), close[-20:])[0, 1])
            
            return {
                'sma_5': sma_5,
                'sma_10': sma_10,
                'sma_20': sma_20,
                'sma_60': sma_60,
                'ema_12': ema_12,
                'ema_26': ema_26,
                'short_trend': short_trend,
                'medium_trend': medium_trend,
                'long_trend': long_trend,
                'overall_direction': overall_direction,
                'trend_strength': trend_strength
            }
            
        except Exception as e:
            logger.error(f"趋势指标计算失败: {str(e)}")
            return {}
    
    def _calculate_momentum_indicators(self, high, low, close, volume):
        """计算动量指标"""
        try:
            # RSI
            rsi = self._rsi(close, 14)
            
            # MACD
            macd, macd_signal, macd_hist = self._macd(close, 12, 26, 9)
            
            # KDJ (使用Stochastic)
            slowk, slowd = self._stochastic(high, low, close, 9, 3, 3)
            j = 3 * slowk - 2 * slowd
            
            # Momentum
            momentum = self._momentum(close, 10)
            
            # ROC
            roc = self._roc(close, 10)
            
            # Williams %R
            willr = self._williams_r(high, low, close, 14)
            
            # 信号分析
            macd_cross_signal = self._analyze_macd_cross(macd, macd_signal)
            kdj_signal = self._analyze_kdj_signal(slowk, slowd)
            momentum_signal = self._analyze_momentum_signal(momentum, roc)
            
            return {
                'rsi': rsi,
                'macd': macd,
                'macd_signal': macd_signal,
                'macd_hist': macd_hist,
                'kdj': {
                    'k': slowk,
                    'd': slowd,
                    'j': j,
                    'signal': kdj_signal
                },
                'momentum': momentum,
                'roc': roc,
                'willr': willr,
                'cross_signal': macd_cross_signal,
                'momentum_signal': momentum_signal
            }
            
        except Exception as e:
            logger.error(f"动量指标计算失败: {str(e)}")
            return {}
    
    def _calculate_volatility_indicators(self, high, low, close):
        """计算波动性指标"""
        try:
            # ATR
            atr = self._atr(high, low, close, 14)
            
            # 布林带
            bb_upper, bb_middle, bb_lower = self._bollinger_bands(close, 20, 2)
            
            # 历史波动率
            returns = np.diff(np.log(close))
            historical_volatility = np.std(returns) * np.sqrt(252)  # 年化
            
            # ATR波动率
            atr_volatility = atr[-1] / close[-1] if len(atr) > 0 and not np.isnan(atr[-1]) else 0
            
            # 波动率百分位
            volatility_percentile = self._calculate_volatility_percentile(returns)
            
            # 波动性等级
            volatility_level = self._classify_volatility_level(historical_volatility)
            
            # 布林带位置和宽度
            bb_position = (close - bb_lower) / (bb_upper - bb_lower)
            bb_width = (bb_upper - bb_lower) / bb_middle
            
            return {
                'atr': atr,
                'historical_volatility': historical_volatility,
                'atr_volatility': atr_volatility,
                'volatility_percentile': volatility_percentile,
                'volatility_level': volatility_level,
                'bollinger': {
                    'upper': bb_upper,
                    'middle': bb_middle,
                    'lower': bb_lower,
                    'position': bb_position,
                    'width': bb_width
                }
            }
            
        except Exception as e:
            logger.error(f"波动性指标计算失败: {str(e)}")
            return {}
    
    def _calculate_volume_indicators(self, close, volume):
        """计算成交量指标"""
        try:
            # OBV
            obv = self._obv(close, volume)
            
            # 成交量移动平均
            volume_ma = self._sma(volume, 20)
            
            # 成交量比率
            volume_ratio = volume / volume_ma
            
            # 价量背离检测
            price_volume_divergence = self._detect_price_volume_divergence(close, volume)
            
            return {
                'obv': obv,
                'volume_ma': volume_ma,
                'volume_ratio': volume_ratio,
                'price_volume_divergence': price_volume_divergence
            }
            
        except Exception as e:
            logger.error(f"成交量指标计算失败: {str(e)}")
            return {}
    
    def _calculate_support_resistance(self, high, low, close):
        """计算支撑压力位"""
        try:
            current_price = close[-1]
            
            # 使用局部极值方法
            resistance_levels = self._find_resistance_levels(high, close, current_price)
            support_levels = self._find_support_levels(low, close, current_price)
            
            return {
                'current_price': current_price,
                'resistance': resistance_levels,
                'support': support_levels
            }
            
        except Exception as e:
            logger.error(f"支撑压力位计算失败: {str(e)}")
            return {}
    
    # NumPy实现的技术指标方法
    def _sma(self, data, period):
        """简单移动平均"""
        result = np.full(len(data), np.nan)
        for i in range(period - 1, len(data)):
            result[i] = np.mean(data[i - period + 1:i + 1])
        return result
    
    def _ema(self, data, period):
        """指数移动平均"""
        alpha = 2.0 / (period + 1)
        result = np.full(len(data), np.nan)
        result[0] = data[0]
        
        for i in range(1, len(data)):
            result[i] = alpha * data[i] + (1 - alpha) * result[i - 1]
        
        return result
    
    def _rsi(self, close, period):
        """相对强弱指数"""
        delta = np.diff(close)
        gain = np.where(delta > 0, delta, 0)
        loss = np.where(delta < 0, -delta, 0)
        
        avg_gain = self._sma(np.concatenate([[0], gain]), period)[1:]
        avg_loss = self._sma(np.concatenate([[0], loss]), period)[1:]
        
        rs = avg_gain / np.where(avg_loss == 0, 1e-10, avg_loss)
        rsi = 100 - (100 / (1 + rs))
        
        return np.concatenate([[np.nan], rsi])
    
    def _macd(self, close, fast_period, slow_period, signal_period):
        """MACD指标"""
        ema_fast = self._ema(close, fast_period)
        ema_slow = self._ema(close, slow_period)
        macd_line = ema_fast - ema_slow
        signal_line = self._ema(macd_line, signal_period)
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
    
    def _stochastic(self, high, low, close, k_period, k_smooth, d_smooth):
        """随机指标"""
        lowest_low = np.full(len(close), np.nan)
        highest_high = np.full(len(close), np.nan)
        
        for i in range(k_period - 1, len(close)):
            lowest_low[i] = np.min(low[i - k_period + 1:i + 1])
            highest_high[i] = np.max(high[i - k_period + 1:i + 1])
        
        k_percent = 100 * (close - lowest_low) / (highest_high - lowest_low)
        k_smooth_values = self._sma(k_percent, k_smooth)
        d_smooth_values = self._sma(k_smooth_values, d_smooth)
        
        return k_smooth_values, d_smooth_values
    
    def _momentum(self, close, period):
        """动量指标"""
        result = np.full(len(close), np.nan)
        for i in range(period, len(close)):
            result[i] = close[i] - close[i - period]
        return result
    
    def _roc(self, close, period):
        """变化率指标"""
        result = np.full(len(close), np.nan)
        for i in range(period, len(close)):
            result[i] = ((close[i] - close[i - period]) / close[i - period]) * 100
        return result
    
    def _williams_r(self, high, low, close, period):
        """威廉指标"""
        result = np.full(len(close), np.nan)
        
        for i in range(period - 1, len(close)):
            highest_high = np.max(high[i - period + 1:i + 1])
            lowest_low = np.min(low[i - period + 1:i + 1])
            result[i] = -100 * (highest_high - close[i]) / (highest_high - lowest_low)
        
        return result
    
    def _atr(self, high, low, close, period):
        """平均真实波幅"""
        tr = np.maximum(high - low, 
                       np.maximum(np.abs(high - np.roll(close, 1)),
                                 np.abs(low - np.roll(close, 1))))
        tr[0] = high[0] - low[0]  # 第一个值
        
        return self._sma(tr, period)
    
    def _bollinger_bands(self, close, period, std_dev):
        """布林带"""
        sma = self._sma(close, period)
        std = np.full(len(close), np.nan)
        
        for i in range(period - 1, len(close)):
            std[i] = np.std(close[i - period + 1:i + 1])
        
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        
        return upper, sma, lower
    
    def _obv(self, close, volume):
        """能量潮指标"""
        obv = np.zeros(len(close))
        obv[0] = volume[0]
        
        for i in range(1, len(close)):
            if close[i] > close[i - 1]:
                obv[i] = obv[i - 1] + volume[i]
            elif close[i] < close[i - 1]:
                obv[i] = obv[i - 1] - volume[i]
            else:
                obv[i] = obv[i - 1]
        
        return obv

    # 辅助方法（与talib版本保持一致）
    def _calculate_trend_direction(self, prices):
        """计算趋势方向"""
        if len(prices) < 3:
            return 0
        x = np.arange(len(prices))
        slope = np.polyfit(x, prices, 1)[0]
        return 1 if slope > 0 else -1 if slope < 0 else 0

    def _determine_overall_direction(self, short, medium, long):
        """确定整体趋势方向"""
        trend_sum = short + medium + long
        if trend_sum >= 2:
            return "强势上升"
        elif trend_sum == 1:
            return "温和上升"
        elif trend_sum == -1:
            return "温和下降"
        elif trend_sum <= -2:
            return "强势下降"
        else:
            return "震荡"

    def _analyze_macd_cross(self, macd, signal):
        """分析MACD交叉信号"""
        if len(macd) < 2 or len(signal) < 2:
            return 'none'

        prev_diff = macd[-2] - signal[-2]
        curr_diff = macd[-1] - signal[-1]

        if prev_diff <= 0 and curr_diff > 0:
            return 'golden_cross'
        elif prev_diff >= 0 and curr_diff < 0:
            return 'death_cross'
        else:
            return 'none'

    def _analyze_kdj_signal(self, k, d):
        """分析KDJ信号"""
        if len(k) < 2 or len(d) < 2:
            return 'neutral'

        if k[-1] > 80 and d[-1] > 80:
            return 'overbought'
        elif k[-1] < 20 and d[-1] < 20:
            return 'oversold'
        elif k[-2] < d[-2] and k[-1] > d[-1]:
            return 'golden_cross'
        elif k[-2] > d[-2] and k[-1] < d[-1]:
            return 'death_cross'
        else:
            return 'neutral'

    def _analyze_momentum_signal(self, momentum, roc):
        """分析动量信号"""
        if len(momentum) < 2 or len(roc) < 2:
            return 'neutral'

        mom_trend = momentum[-1] - momentum[-2]
        roc_trend = roc[-1] - roc[-2]

        if mom_trend > 0 and roc_trend > 0:
            return 'strengthening'
        elif mom_trend < 0 and roc_trend < 0:
            return 'weakening'
        else:
            return 'neutral'

    def _calculate_volatility_percentile(self, returns):
        """计算波动率百分位"""
        if len(returns) < 252:
            return 50.0

        current_vol = np.std(returns[-20:]) * np.sqrt(252)
        historical_vols = [np.std(returns[i:i+20]) * np.sqrt(252)
                          for i in range(len(returns)-252, len(returns)-20)]

        percentile = (np.sum(np.array(historical_vols) < current_vol) / len(historical_vols)) * 100
        return percentile

    def _classify_volatility_level(self, volatility):
        """分类波动性等级"""
        if volatility > 0.4:
            return '极高'
        elif volatility > 0.3:
            return '较高'
        elif volatility > 0.2:
            return '正常'
        elif volatility > 0.1:
            return '较低'
        else:
            return '极低'

    def _detect_price_volume_divergence(self, close, volume):
        """检测价量背离"""
        if len(close) < 10 or len(volume) < 10:
            return False

        price_trend = 1 if close[-1] > close[-10] else -1
        volume_trend = 1 if np.mean(volume[-5:]) > np.mean(volume[-10:-5]) else -1

        return price_trend * volume_trend < 0

    def _find_resistance_levels(self, high, close, current_price, window=20):
        """寻找压力位"""
        resistance_levels = []

        # 使用scipy的argrelextrema寻找局部最大值
        maxima_indices = argrelextrema(high, np.greater, order=window)[0]

        for i in maxima_indices:
            if i >= window and i < len(high) - window:
                # 计算强度（附近相似价格点的数量）
                strength = sum(1 for j in range(max(0, i-window*3), min(len(high), i+window*3))
                             if abs(high[j] - high[i]) / high[i] <= 0.01)

                if strength >= 2 and high[i] > current_price:
                    resistance_levels.append({
                        'price': high[i],
                        'strength': strength,
                        'index': i
                    })

        # 按距离当前价格排序
        resistance_levels.sort(key=lambda x: abs(x['price'] - current_price))
        return resistance_levels[:5]  # 返回前5个

    def _find_support_levels(self, low, close, current_price, window=20):
        """寻找支撑位"""
        support_levels = []

        # 使用scipy的argrelextrema寻找局部最小值
        minima_indices = argrelextrema(low, np.less, order=window)[0]

        for i in minima_indices:
            if i >= window and i < len(low) - window:
                # 计算强度（附近相似价格点的数量）
                strength = sum(1 for j in range(max(0, i-window*3), min(len(low), i+window*3))
                             if abs(low[j] - low[i]) / low[i] <= 0.01)

                if strength >= 2 and low[i] < current_price:
                    support_levels.append({
                        'price': low[i],
                        'strength': strength,
                        'index': i
                    })

        # 按距离当前价格排序
        support_levels.sort(key=lambda x: abs(x['price'] - current_price))
        return support_levels[:5]  # 返回前5个
