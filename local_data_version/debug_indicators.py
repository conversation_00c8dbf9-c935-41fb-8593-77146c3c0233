#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试技术指标计算问题和脏数据检测
"""

import sys
import os
import pandas as pd
import numpy as np
import re

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from local_data_loader import LocalDataLoader
from local_technical_indicators import LocalTechnicalIndicators

def debug_indicators():
    """调试技术指标计算"""
    print("开始调试技术指标计算...")
    
    # 加载数据
    loader = LocalDataLoader()
    data = loader.load_contract_data('SHFE', 'ag2508', '日线')
    
    if data is None:
        print("❌ 数据加载失败")
        return
    
    print(f"✅ 数据加载成功，共 {len(data)} 条记录")
    print(f"数据类型:")
    print(data.dtypes)
    print(f"\n数据样本:")
    print(data[['open', 'high', 'low', 'close', 'volume']].head())
    
    # 获取最近100条数据
    recent_data = data.tail(100)
    print(f"\n使用最近 {len(recent_data)} 条数据进行测试")
    
    # 检查数据类型
    print(f"\n数据类型检查:")
    for col in ['open', 'high', 'low', 'close', 'volume']:
        if col in recent_data.columns:
            print(f"{col}: {recent_data[col].dtype}")
    
    # 尝试计算技术指标
    calculator = LocalTechnicalIndicators()
    
    try:
        print("\n🔢 开始计算技术指标...")
        
        # 确保数据类型
        test_data = recent_data.copy()
        for col in ['open', 'high', 'low', 'close', 'volume']:
            if col in test_data.columns:
                test_data[col] = pd.to_numeric(test_data[col], errors='coerce').astype(np.float64)
        
        print("数据类型转换后:")
        for col in ['open', 'high', 'low', 'close', 'volume']:
            if col in test_data.columns:
                print(f"{col}: {test_data[col].dtype}")
        
        # 逐个测试指标
        print("\n测试移动平均线...")
        ma20 = calculator.calculate_ma(test_data, 20)
        print(f"MA20 计算成功: {ma20[-5:]}")
        
        print("\n测试RSI...")
        rsi = calculator.calculate_rsi(test_data)
        print(f"RSI 计算成功: {rsi[-5:]}")
        
        print("\n测试布林带...")
        bb = calculator.calculate_bollinger_bands(test_data)
        print(f"布林带计算成功")
        
        print("\n测试MACD...")
        macd = calculator.calculate_macd(test_data)
        print(f"MACD 计算成功")
        
        print("\n测试KDJ...")
        kdj = calculator.calculate_kdj(test_data)
        print(f"KDJ 计算成功")
        
        print("\n测试ATR...")
        atr = calculator.calculate_atr(test_data)
        print(f"ATR 计算成功: {atr[-5:]}")
        
        print("\n✅ 所有单独指标计算成功！")
        
        # 测试综合计算
        print("\n测试综合指标计算...")
        indicators = calculator.calculate_all_indicators(test_data)
        
        if indicators:
            print("✅ 综合指标计算成功！")
            print(f"计算的指标: {list(indicators.keys())}")
        else:
            print("❌ 综合指标计算失败")
        
    except Exception as e:
        print(f"❌ 计算失败: {str(e)}")
        import traceback
        traceback.print_exc()

def check_dirty_data():
    """检查脏数据问题"""
    print("="*60)
    print("🔍 脏数据检测分析")
    print("="*60)

    # 检查CFFEX的数据文件
    cffex_file = "/Users/<USER>/Downloads/data_index_0704/CFFEX/IF2509_Day.txt"

    if not os.path.exists(cffex_file):
        print("❌ CFFEX数据文件不存在")
        return

    print(f"📁 检查文件: {cffex_file}")

    # 尝试不同编码读取文件
    encodings = ['utf-8', 'gbk', 'gb2312', 'latin1']
    content = None
    used_encoding = None

    for encoding in encodings:
        try:
            with open(cffex_file, 'r', encoding=encoding) as f:
                content = f.read()
                used_encoding = encoding
                break
        except UnicodeDecodeError:
            continue

    if content is None:
        print("❌ 无法读取文件")
        return

    print(f"✅ 使用编码: {used_encoding}")

    # 分析脏数据
    records = content.split(';')
    total_records = len(records)
    valid_records = 0
    dirty_records = 0
    dirty_examples = []

    print(f"\n📊 数据统计:")
    print(f"总记录数: {total_records}")

    for i, record in enumerate(records):
        if not record.strip():
            continue

        # 检查是否有非正常字符开头
        if record.strip() and not record.strip()[0].isalnum():
            dirty_records += 1
            if len(dirty_examples) < 5:  # 只保存前5个例子
                dirty_examples.append(f"记录 {i}: {record[:100]}...")
            continue

        # 尝试解析记录
        parts = record.split(',')
        if len(parts) >= 13:
            try:
                # 检查关键字段
                exchange_name = parts[0]
                contract_name = parts[1]
                date_str = parts[2]
                time_str = parts[3]

                # 验证交易所名称
                if exchange_name not in ['CFFEX', 'SHFE', 'DCE', 'ZCE', 'GFEX']:
                    dirty_records += 1
                    if len(dirty_examples) < 5:
                        dirty_examples.append(f"记录 {i} (无效交易所): {record[:100]}...")
                    continue

                # 验证价格数据
                open_price = float(parts[5])
                high_price = float(parts[6])
                low_price = float(parts[7])
                close_price = float(parts[8])
                volume = int(parts[9])

                # 基本数据合理性检查
                if high_price < low_price or high_price < open_price or high_price < close_price:
                    dirty_records += 1
                    if len(dirty_examples) < 5:
                        dirty_examples.append(f"记录 {i} (价格异常): {record[:100]}...")
                    continue

                if volume < 0:
                    dirty_records += 1
                    if len(dirty_examples) < 5:
                        dirty_examples.append(f"记录 {i} (成交量异常): {record[:100]}...")
                    continue

                valid_records += 1

            except (ValueError, IndexError) as e:
                dirty_records += 1
                if len(dirty_examples) < 5:
                    dirty_examples.append(f"记录 {i} (解析错误): {record[:100]}... 错误: {e}")

    print(f"有效记录数: {valid_records}")
    print(f"脏数据记录数: {dirty_records}")
    print(f"脏数据比例: {dirty_records/total_records*100:.2f}%")

    if dirty_examples:
        print(f"\n🚨 脏数据示例:")
        for example in dirty_examples:
            print(f"  {example}")

    # 测试数据加载器的处理能力
    print(f"\n🔧 测试数据加载器处理能力:")
    loader = LocalDataLoader()

    try:
        data = loader.load_contract_data('CFFEX', 'IF2509', '日线')
        if data is not None:
            print(f"✅ 数据加载成功，共 {len(data)} 条有效记录")
            print(f"📈 数据范围: {data['datetime'].min()} 至 {data['datetime'].max()}")

            # 检查数据质量
            print(f"\n📊 数据质量检查:")

            # 检查价格数据
            price_issues = 0
            for idx, row in data.iterrows():
                if row['high'] < row['low'] or row['high'] < row['open'] or row['high'] < row['close']:
                    price_issues += 1
                if row['volume'] < 0:
                    price_issues += 1

            print(f"价格异常记录: {price_issues}")
            print(f"数据完整性: {(len(data)-price_issues)/len(data)*100:.2f}%")

            # 显示数据样本
            print(f"\n📋 数据样本:")
            print(data[['datetime', 'open', 'high', 'low', 'close', 'volume']].head())

        else:
            print("❌ 数据加载失败")

    except Exception as e:
        print(f"❌ 数据加载异常: {e}")

def test_data_cleaning():
    """测试数据清洗功能"""
    print("\n" + "="*60)
    print("🧹 数据清洗测试")
    print("="*60)

    # 创建包含脏数据的测试数据
    dirty_data = [
        "°gCFFEX,IF2309,20230803,15:27:00,0,3980.2,4020,3980.2,4018.8,25611,123217,0,25611,3.07422e+10",
        "1CFFEX,IF2309,20230804,15:28:00,0,4060,4083,4027.6,4037.8,40991,129751,0,40991,4.98733e+10",
        "CFFEX,IF2309,20230807,15:30:00,0,4027.6,4027.6,4001.2,4015.4,22028,126257,0,22028,2.65147e+10",
        "CFFEX,IF2309,20230808,15:30:00,0,4015.4,4050.2,4010.1,4045.6,18500,128000,0,18500,2.1e+10",
        "INVALID,IF2309,20230809,15:30:00,0,4045.6,4000.0,4045.6,4030.2,15000,125000,0,15000,1.8e+10"  # 高价小于低价
    ]

    print(f"原始数据记录数: {len(dirty_data)}")

    # 模拟数据清洗过程
    clean_data = []
    rejected_data = []

    for i, record in enumerate(dirty_data):
        try:
            # 检查记录开头是否有异常字符
            if record.strip() and not record.strip()[0].isalnum():
                rejected_data.append(f"记录 {i}: 异常字符开头")
                continue

            parts = record.split(',')
            if len(parts) < 13:
                rejected_data.append(f"记录 {i}: 字段不足")
                continue

            exchange_name = parts[0]
            if exchange_name not in ['CFFEX', 'SHFE', 'DCE', 'ZCE', 'GFEX']:
                rejected_data.append(f"记录 {i}: 无效交易所 {exchange_name}")
                continue

            # 验证价格数据
            open_price = float(parts[5])
            high_price = float(parts[6])
            low_price = float(parts[7])
            close_price = float(parts[8])
            volume = int(parts[9])

            # 价格合理性检查
            if high_price < low_price:
                rejected_data.append(f"记录 {i}: 最高价小于最低价")
                continue

            if high_price < max(open_price, close_price):
                rejected_data.append(f"记录 {i}: 最高价小于开盘价或收盘价")
                continue

            if low_price > min(open_price, close_price):
                rejected_data.append(f"记录 {i}: 最低价大于开盘价或收盘价")
                continue

            if volume < 0:
                rejected_data.append(f"记录 {i}: 成交量为负")
                continue

            clean_data.append(record)

        except (ValueError, IndexError) as e:
            rejected_data.append(f"记录 {i}: 解析错误 - {e}")

    print(f"清洗后有效记录数: {len(clean_data)}")
    print(f"被拒绝记录数: {len(rejected_data)}")
    print(f"数据清洗率: {len(clean_data)/len(dirty_data)*100:.2f}%")

    if rejected_data:
        print(f"\n🚨 被拒绝的记录:")
        for rejection in rejected_data:
            print(f"  {rejection}")

if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 技术指标调试")
    print("2. 脏数据检测")
    print("3. 数据清洗测试")
    print("4. 全部测试")

    choice = input("请选择 (1-4): ").strip()

    if choice == '1':
        debug_indicators()
    elif choice == '2':
        check_dirty_data()
    elif choice == '3':
        test_data_cleaning()
    elif choice == '4':
        debug_indicators()
        check_dirty_data()
        test_data_cleaning()
    else:
        print("无效选择，运行技术指标调试")
        debug_indicators()
