#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货技术分析系统 v0.2 简化主程序
直接输入合约代码，自动分析，结果保存到文件
"""

import sys
import os
import logging
from datetime import datetime
import numpy as np

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from local_data_loader import LocalDataLoader
from local_technical_indicators import LocalTechnicalIndicators

# 配置日志 - 只记录错误
logging.basicConfig(
    level=logging.ERROR,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('analysis.log')
    ]
)
logger = logging.getLogger(__name__)

def validate_symbol_input(symbol, loader):
    """验证合约代码输入"""
    if not symbol or '/' not in symbol:
        return False, "格式错误：请使用 '交易所/合约代码' 格式，如 CFFEX/IF2509"
    
    try:
        parts = symbol.split('/')
        if len(parts) != 2:
            return False, "格式错误：请使用 '交易所/合约代码' 格式"
        
        exchange, contract = parts[0].strip().upper(), parts[1].strip()
        
        # 检查交易所是否支持
        supported_exchanges = ['SHFE', 'DCE', 'ZCE', 'CFFEX', 'GFEX']
        if exchange not in supported_exchanges:
            return False, f"不支持的交易所: {exchange}，支持的交易所: {', '.join(supported_exchanges)}"
        
        # 检查合约是否存在
        available_exchanges = loader.list_available_exchanges()
        if exchange not in available_exchanges:
            return False, f"交易所 {exchange} 无可用数据"
        
        contracts = loader.list_contracts_by_exchange(exchange)
        if contract not in contracts:
            return False, f"合约 {contract} 在 {exchange} 中不存在"
        
        return True, ""
        
    except Exception as e:
        return False, f"验证失败: {str(e)}"

def analyze_contract(exchange, contract, period, loader, calculator):
    """分析单个合约的单个周期"""
    try:
        print(f"  📊 分析 {period} 数据...")
        
        # 加载数据
        data = loader.load_contract_data(exchange, contract, period)
        
        if data is None or len(data) < 60:
            print(f"    ❌ {period} 数据不足")
            return None
        
        print(f"    ✅ 加载 {len(data)} 条 {period} 数据")
        
        # 计算技术指标
        indicators = calculator.calculate_all_indicators(data)
        
        if indicators is None:
            print(f"    ❌ {period} 技术指标计算失败")
            return None
        
        print(f"    ✅ {period} 技术指标计算完成")
        
        # 生成简化分析
        analysis = generate_simple_analysis(indicators, data, exchange, contract, period)
        
        return analysis
        
    except Exception as e:
        print(f"    ❌ {period} 分析失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def generate_simple_analysis(indicators, data, exchange, contract, period):
    """生成简化分析"""
    current_price = data['close'].iloc[-1]
    current_volume = data['volume'].iloc[-1]
    current_oi = data['open_interest'].iloc[-1]
    
    analysis = {
        'timestamp': datetime.now(),
        'exchange': exchange,
        'contract': contract,
        'period': period,
        'current_price': current_price,
        'current_volume': current_volume,
        'current_open_interest': current_oi,
        'data_period': f"{str(data.index[0])[:10]} 至 {str(data.index[-1])[:10]}",
        'total_records': len(data)
    }
    
    # 趋势分析
    trend = indicators.get('trend', {})
    analysis['trend'] = {
        'overall_direction': trend.get('overall_direction', '震荡'),
        'short_term': describe_trend(trend.get('short_trend', 0)),
        'medium_term': describe_trend(trend.get('medium_trend', 0)),
        'long_term': describe_trend(trend.get('long_trend', 0)),
        'strength': trend.get('trend_strength', 0.5)
    }
    
    # RSI分析
    rsi = indicators.get('rsi', [])
    if isinstance(rsi, np.ndarray) and len(rsi) > 0:
        try:
            rsi_current = float(rsi[-1])
            if not np.isnan(rsi_current):
                if rsi_current > 70:
                    rsi_status = "超买"
                elif rsi_current < 30:
                    rsi_status = "超卖"
                else:
                    rsi_status = "正常"
            else:
                rsi_current = None
                rsi_status = "数据不足"
        except:
            rsi_current = None
            rsi_status = "数据不足"
    else:
        rsi_current = None
        rsi_status = "数据不足"
    
    analysis['momentum'] = {
        'rsi': {'current': rsi_current, 'status': rsi_status}
    }
    
    # 波动性分析
    volatility = indicators.get('volatility', {})
    analysis['volatility'] = {
        'level': volatility.get('volatility_level', '未知'),
        'historical_volatility': volatility.get('historical_volatility', 0),
        'atr_volatility': volatility.get('atr_volatility', 0)
    }
    
    # 成交量分析
    volume_data = indicators.get('volume', {})
    obv = volume_data.get('obv', [])

    # 安全地检查OBV趋势
    obv_trend = '下降'  # 默认值
    try:
        if isinstance(obv, np.ndarray) and len(obv) > 1:
            if obv[-1] > obv[-2]:
                obv_trend = '上升'
        elif isinstance(obv, list) and len(obv) > 1:
            if obv[-1] > obv[-2]:
                obv_trend = '上升'
    except:
        obv_trend = '未知'

    # 安全地检查成交量比率
    volume_ratio = volume_data.get('volume_ratio', 1)
    if isinstance(volume_ratio, (np.ndarray, list)) and len(volume_ratio) > 0:
        volume_ratio = volume_ratio[-1]  # 取最新值

    analysis['volume'] = {
        'status': '正常' if volume_ratio < 2 else '放量',
        'obv_trend': obv_trend
    }
    
    return analysis

def describe_trend(trend_value):
    """描述趋势"""
    if trend_value > 0.1:
        return "上升"
    elif trend_value < -0.1:
        return "下降"
    else:
        return "震荡"

def save_analysis_to_file(analysis, output_dir="analysis_results"):
    """保存分析结果到文件"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    timestamp = analysis['timestamp'].strftime("%Y%m%d_%H%M%S")
    filename = f"analysis_{analysis['exchange']}_{analysis['contract']}_{analysis['period']}_{timestamp}.txt"
    file_path = os.path.join(output_dir, filename)
    
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(f"期货技术分析报告 v0.2\n")
            f.write("="*80 + "\n")
            f.write(f"合约代码: {analysis['exchange']}/{analysis['contract']}\n")
            f.write(f"数据周期: {analysis['period']}\n")
            f.write(f"分析时间: {analysis['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"当前价格: {analysis['current_price']:.2f}\n")
            f.write(f"当前成交量: {analysis['current_volume']:,}\n")
            f.write(f"当前持仓量: {analysis['current_open_interest']:,}\n")
            f.write(f"数据范围: {analysis['data_period']}\n")
            f.write(f"数据量: {analysis['total_records']} 条记录\n")
            f.write("="*80 + "\n\n")
            
            # 趋势分析
            trend = analysis['trend']
            f.write("📈 趋势分析:\n")
            f.write(f"  整体方向: {trend['overall_direction']}\n")
            f.write(f"  短期趋势: {trend['short_term']}\n")
            f.write(f"  中期趋势: {trend['medium_term']}\n")
            f.write(f"  长期趋势: {trend['long_term']}\n")
            f.write(f"  趋势强度: {trend['strength']:.2f}\n\n")
            
            # 动量分析
            momentum = analysis['momentum']
            f.write("⚡ 动量分析:\n")
            rsi_info = momentum['rsi']
            if rsi_info['current'] is not None:
                f.write(f"  RSI: {rsi_info['current']:.2f} ({rsi_info['status']})\n")
            else:
                f.write(f"  RSI: {rsi_info['status']}\n")
            f.write("\n")
            
            # 波动性分析
            volatility = analysis['volatility']
            f.write("📊 波动性分析:\n")
            f.write(f"  波动性等级: {volatility['level']}\n")
            if volatility['historical_volatility'] > 0:
                f.write(f"  历史波动率: {volatility['historical_volatility']:.4f}\n")
            if volatility['atr_volatility'] > 0:
                f.write(f"  ATR波动率: {volatility['atr_volatility']:.4f}\n")
            f.write("\n")
            
            # 成交量分析
            volume = analysis['volume']
            f.write("📦 成交量分析:\n")
            f.write(f"  成交量状态: {volume['status']}\n")
            f.write(f"  OBV趋势: {volume['obv_trend']}\n")
            f.write("\n")
            
            f.write("="*80 + "\n")
        
        return file_path
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
        return None

def main():
    """简化的主程序"""
    print("🚀 期货技术分析系统 v0.2")
    print("📊 自动选择最佳K线周期进行长短期分析")
    print("📁 分析结果保存到文件")
    print("="*60)
    
    # 创建组件
    loader = LocalDataLoader()
    calculator = LocalTechnicalIndicators()
    
    # 获取用户输入
    while True:
        symbol = input("\n请输入合约代码 (格式: 交易所/合约代码，如 CFFEX/IF2509): ").strip()

        # 只将交易所部分转换为大写
        if '/' in symbol:
            parts = symbol.split('/')
            symbol = f"{parts[0].upper()}/{parts[1]}"

        if not symbol:
            print("❌ 请输入有效的合约代码")
            continue
        
        # 验证输入
        is_valid, error_msg = validate_symbol_input(symbol, loader)
        
        if not is_valid:
            print(f"❌ {error_msg}")
            continue
        
        # 解析合约代码
        exchange, contract = symbol.split('/')
        exchange, contract = exchange.strip(), contract.strip()
        
        # 显示合约信息
        periods = loader.get_available_periods(exchange, contract)
        print(f"\n📊 合约信息:")
        print(f"   交易所: {exchange}")
        print(f"   合约代码: {contract}")
        print(f"   可用数据: {', '.join(periods)}")
        
        # 开始分析
        print(f"\n🔍 开始分析 {symbol}...")
        
        saved_files = []
        success_count = 0
        
        # 分析每个可用周期
        for period in periods:
            analysis = analyze_contract(exchange, contract, period, loader, calculator)
            
            if analysis:
                # 保存结果
                file_path = save_analysis_to_file(analysis)
                if file_path:
                    saved_files.append(file_path)
                    success_count += 1
        
        # 显示结果
        if success_count > 0:
            print(f"\n✅ 分析完成！成功分析 {success_count} 个周期")
            print(f"📁 结果文件:")
            for file_path in saved_files:
                print(f"   - {file_path}")
        else:
            print(f"\n❌ 分析失败")
        
        # 分析完成，程序结束
        print(f"\n🎉 分析完成，程序结束")
        break

if __name__ == "__main__":
    main()
