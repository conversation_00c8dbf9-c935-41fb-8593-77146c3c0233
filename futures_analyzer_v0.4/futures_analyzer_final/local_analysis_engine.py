"""
基于本地数据的期货技术分析引擎
"""

import pandas as pd
import numpy as np
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class LocalAnalysisEngine:
    """本地数据分析引擎"""

    def __init__(self):
        """初始化分析引擎"""
        self.analysis_results = {}

    def analyze_trend(self, indicators, data):
        """分析趋势"""
        trend_data = indicators['trend']
        ma20 = indicators['ma20']
        ma60 = indicators['ma60']

        current_price = data['close'].iloc[-1]

        # 移动平均线分析
        ma_analysis = self._analyze_moving_averages(current_price, ma20, ma60)

        # 趋势强度分析
        strength_analysis = {
            'strength': trend_data['trend_strength'],
            'level': self._classify_trend_strength(trend_data['trend_strength'])
        }

        # 趋势一致性分析
        consistency_analysis = {
            'status': trend_data['trend_consistency'],
            'description': self._describe_trend_consistency(trend_data['trend_consistency'])
        }

        return {
            'overall_direction': trend_data['overall_direction'],
            'short_term': self._describe_trend(trend_data['short_trend']),
            'medium_term': self._describe_trend(trend_data['medium_trend']),
            'long_term': self._describe_trend(trend_data['long_trend']),
            'moving_averages': ma_analysis,
            'strength': strength_analysis,
            'consistency': consistency_analysis
        }

    def analyze_momentum(self, indicators, data):
        """分析动量指标"""
        rsi = indicators['rsi']
        kdj = indicators['kdj']
        macd = indicators['macd']
        momentum = indicators['momentum']

        # RSI分析
        rsi_analysis = self._analyze_rsi(rsi)

        # KDJ分析
        kdj_analysis = self._analyze_kdj(kdj)

        # MACD分析
        macd_analysis = self._analyze_macd(macd)

        # 动量分析
        momentum_analysis = self._analyze_momentum(momentum)

        return {
            'rsi': rsi_analysis,
            'kdj': kdj_analysis,
            'macd': macd_analysis,
            'momentum': momentum_analysis,
            'overall_momentum': self._determine_overall_momentum(rsi_analysis, kdj_analysis, macd_analysis)
        }

    def analyze_volatility(self, indicators, data):
        """分析波动性"""
        volatility_data = indicators['volatility']
        bollinger = indicators['bollinger']
        atr = indicators['atr']

        # 波动率分析
        volatility_analysis = {
            'level': volatility_data['volatility_level'],
            'historical_vol': volatility_data['historical_volatility'],
            'atr_vol': volatility_data['atr_volatility'],
            'percentile': volatility_data['volatility_percentile']
        }

        # 布林带分析
        bollinger_analysis = self._analyze_bollinger_bands(bollinger, data['close'].iloc[-1])

        # ATR分析
        atr_analysis = self._analyze_atr(atr, data)

        return {
            'level': volatility_data['volatility_level'],
            'analysis': volatility_analysis,
            'bollinger': bollinger_analysis,
            'atr': atr_analysis,
            'trading_suggestion': self._generate_volatility_suggestion(volatility_data['volatility_level'])
        }

    def analyze_volume(self, indicators, data):
        """分析成交量"""
        volume_data = indicators['volume']

        # 成交量趋势
        volume_trend = self._analyze_volume_trend(volume_data)

        # 价量关系
        price_volume_relation = self._analyze_price_volume_relation(volume_data, data)

        # OBV分析
        obv_analysis = self._analyze_obv(volume_data['obv'], data)

        return {
            'trend': volume_trend,
            'price_volume_relation': price_volume_relation,
            'obv': obv_analysis,
            'volume_surge': volume_data['volume_surge'],
            'divergence': volume_data['price_volume_divergence']
        }

    def analyze_support_resistance(self, indicators, data):
        """分析支撑压力位"""
        sr_data = indicators['support_resistance']
        current_price = sr_data['current_price']

        # 分析最近的支撑压力位
        nearest_resistance = sr_data['resistance'][:3] if sr_data['resistance'] else []
        nearest_support = sr_data['support'][:3] if sr_data['support'] else []

        # 计算距离
        resistance_distances = []
        for r in nearest_resistance:
            distance_pct = (r['price'] - current_price) / current_price * 100
            resistance_distances.append({
                'price': r['price'],
                'distance_pct': distance_pct,
                'strength': r['strength']
            })

        support_distances = []
        for s in nearest_support:
            distance_pct = (current_price - s['price']) / current_price * 100
            support_distances.append({
                'price': s['price'],
                'distance_pct': distance_pct,
                'strength': s['strength']
            })

        return {
            'current_price': current_price,
            'nearest_resistance': resistance_distances,
            'nearest_support': support_distances,
            'key_levels': self._identify_key_levels(resistance_distances, support_distances)
        }

    def generate_reversal_signals(self, indicators, data):
        """生成反转信号"""
        signals = []

        # RSI反转信号
        rsi = indicators['rsi']
        if len(rsi) > 0:
            current_rsi = rsi[-1]
            if current_rsi > 80:
                signals.append({
                    'type': 'RSI超买',
                    'description': f'RSI达到{current_rsi:.1f}，可能出现回调',
                    'probability': 'medium',
                    'action': '考虑减仓'
                })
            elif current_rsi < 20:
                signals.append({
                    'type': 'RSI超卖',
                    'description': f'RSI降至{current_rsi:.1f}，可能出现反弹',
                    'probability': 'medium',
                    'action': '考虑建仓'
                })

        # KDJ反转信号
        kdj = indicators['kdj']
        if kdj['signal'] == 'overbought':
            signals.append({
                'type': 'KDJ超买',
                'description': 'KDJ指标显示超买状态',
                'probability': 'medium',
                'action': '关注回调风险'
            })
        elif kdj['signal'] == 'oversold':
            signals.append({
                'type': 'KDJ超卖',
                'description': 'KDJ指标显示超卖状态',
                'probability': 'medium',
                'action': '关注反弹机会'
            })

        # MACD反转信号
        macd = indicators['macd']
        if macd['cross_signal'] == 'golden_cross':
            signals.append({
                'type': 'MACD金叉',
                'description': 'MACD出现金叉信号',
                'probability': 'high',
                'action': '考虑做多'
            })
        elif macd['cross_signal'] == 'death_cross':
            signals.append({
                'type': 'MACD死叉',
                'description': 'MACD出现死叉信号',
                'probability': 'high',
                'action': '考虑做空'
            })

        # 布林带反转信号
        bollinger = indicators['bollinger']
        if len(bollinger['position']) > 0:
            position = bollinger['position'][-1]
            if position > 0.95:
                signals.append({
                    'type': '布林带上轨',
                    'description': '价格触及布林带上轨',
                    'probability': 'medium',
                    'action': '关注回调'
                })
            elif position < 0.05:
                signals.append({
                    'type': '布林带下轨',
                    'description': '价格触及布林带下轨',
                    'probability': 'medium',
                    'action': '关注反弹'
                })

        return signals

    def generate_comprehensive_analysis(self, indicators, data, contract_code, period):
        """生成综合分析"""
        analysis = {
            'contract': contract_code,
            'period': period,
            'timestamp': datetime.now(),
            'current_price': data['close'].iloc[-1],
            'current_volume': data['volume'].iloc[-1],
            'current_open_interest': data['open_interest'].iloc[-1],
            'data_period': f"{data['datetime'].iloc[0].strftime('%Y-%m-%d')} 至 {data['datetime'].iloc[-1].strftime('%Y-%m-%d')}",
            'total_records': len(data)
        }

        # 各项分析
        analysis['trend'] = self.analyze_trend(indicators, data)
        analysis['momentum'] = self.analyze_momentum(indicators, data)
        analysis['volatility'] = self.analyze_volatility(indicators, data)
        analysis['volume'] = self.analyze_volume(indicators, data)
        analysis['open_interest'] = self.analyze_open_interest(data)
        analysis['support_resistance'] = self.analyze_support_resistance(indicators, data)
        analysis['reversal_signals'] = self.generate_reversal_signals(indicators, data)

        # 综合评估
        analysis['overall_assessment'] = self._generate_overall_assessment(analysis)

        return analysis

    def analyze_open_interest(self, data):
        """分析持仓量"""
        if 'open_interest' not in data.columns:
            return {'status': '无持仓量数据'}

        open_interest = data['open_interest'].values
        prices = data['close'].values

        # 持仓量趋势
        oi_trend = self._analyze_oi_trend(open_interest)

        # 价格与持仓量关系
        price_oi_relation = self._analyze_price_oi_relation(prices, open_interest)

        # 持仓量变化率
        oi_change_rate = self._calculate_oi_change_rate(open_interest)

        return {
            'current_oi': open_interest[-1],
            'oi_trend': oi_trend,
            'price_oi_relation': price_oi_relation,
            'oi_change_rate': oi_change_rate,
            'oi_analysis': self._generate_oi_analysis(oi_trend, price_oi_relation, oi_change_rate)
        }

    # 辅助分析方法
    def _analyze_moving_averages(self, current_price, ma20, ma60):
        """分析移动平均线"""
        if len(ma20) == 0 or len(ma60) == 0:
            return {'status': '数据不足', 'description': '移动平均线数据不足'}

        ma20_current = ma20[-1]
        ma60_current = ma60[-1]

        # 价格与均线关系
        if current_price > ma20_current > ma60_current:
            status = '多头排列'
            description = '价格位于短期和长期均线之上，趋势向好'
        elif current_price < ma20_current < ma60_current:
            status = '空头排列'
            description = '价格位于短期和长期均线之下，趋势偏弱'
        else:
            status = '均线纠缠'
            description = '均线排列混乱，趋势不明确'

        return {
            'status': status,
            'description': description,
            'ma20': ma20_current,
            'ma60': ma60_current,
            'price_vs_ma20': (current_price - ma20_current) / ma20_current * 100,
            'price_vs_ma60': (current_price - ma60_current) / ma60_current * 100
        }

    def _classify_trend_strength(self, strength):
        """分类趋势强度"""
        if strength > 0.8:
            return '极强'
        elif strength > 0.6:
            return '较强'
        elif strength > 0.4:
            return '中等'
        elif strength > 0.2:
            return '较弱'
        else:
            return '极弱'

    def _describe_trend_consistency(self, consistency):
        """描述趋势一致性"""
        descriptions = {
            'strong_uptrend': '各周期趋势一致向上，趋势强劲',
            'strong_downtrend': '各周期趋势一致向下，下跌趋势明确',
            'sideways': '各周期均为震荡，缺乏明确方向',
            'mixed': '不同周期趋势存在分歧，需要谨慎'
        }
        return descriptions.get(consistency, '趋势状态不明确')

    def _describe_trend(self, trend_value):
        """描述趋势方向"""
        if trend_value > 0:
            return '上升'
        elif trend_value < 0:
            return '下降'
        else:
            return '震荡'

    def _analyze_rsi(self, rsi):
        """分析RSI"""
        if len(rsi) == 0:
            return {'status': '数据不足', 'value': 0, 'signal': 'neutral'}

        current_rsi = rsi[-1]

        if current_rsi > 80:
            status = '严重超买'
            signal = 'sell'
        elif current_rsi > 70:
            status = '超买'
            signal = 'caution'
        elif current_rsi < 20:
            status = '严重超卖'
            signal = 'buy'
        elif current_rsi < 30:
            status = '超卖'
            signal = 'opportunity'
        else:
            status = '正常区间'
            signal = 'neutral'

        return {
            'value': current_rsi,
            'status': status,
            'signal': signal
        }

    def _analyze_kdj(self, kdj):
        """分析KDJ"""
        signal_descriptions = {
            'overbought': '超买状态，关注回调风险',
            'oversold': '超卖状态，关注反弹机会',
            'golden_cross': 'K线上穿D线，买入信号',
            'death_cross': 'K线下穿D线，卖出信号',
            'neutral': '中性状态，观望为主'
        }

        return {
            'k': kdj['k'][-1] if len(kdj['k']) > 0 else 0,
            'd': kdj['d'][-1] if len(kdj['d']) > 0 else 0,
            'j': kdj['j'][-1] if len(kdj['j']) > 0 else 0,
            'signal': kdj['signal'],
            'description': signal_descriptions.get(kdj['signal'], '状态不明确')
        }

    def _analyze_macd(self, macd):
        """分析MACD"""
        cross_descriptions = {
            'golden_cross': 'MACD金叉，看涨信号',
            'death_cross': 'MACD死叉，看跌信号',
            'none': '无明显交叉信号'
        }

        return {
            'macd': macd['macd'][-1] if len(macd['macd']) > 0 else 0,
            'signal': macd['signal'][-1] if len(macd['signal']) > 0 else 0,
            'histogram': macd['histogram'][-1] if len(macd['histogram']) > 0 else 0,
            'cross_signal': macd['cross_signal'],
            'description': cross_descriptions.get(macd['cross_signal'], '信号不明确')
        }

    def _analyze_momentum(self, momentum):
        """分析动量指标"""
        signal_descriptions = {
            'strengthening': '动量增强，趋势可能延续',
            'weakening': '动量减弱，趋势可能转变',
            'mixed': '动量信号混合，需要观察',
            'neutral': '动量中性，无明确信号'
        }

        return {
            'momentum': momentum['momentum'][-1] if len(momentum['momentum']) > 0 else 0,
            'roc': momentum['roc'][-1] if len(momentum['roc']) > 0 else 0,
            'williams_r': momentum['williams_r'][-1] if len(momentum['williams_r']) > 0 else 0,
            'signal': momentum['momentum_signal'],
            'description': signal_descriptions.get(momentum['momentum_signal'], '信号不明确')
        }

    def _determine_overall_momentum(self, rsi_analysis, kdj_analysis, macd_analysis):
        """确定整体动量"""
        signals = [rsi_analysis['signal'], kdj_analysis['signal'], macd_analysis['cross_signal']]

        buy_signals = sum(1 for s in signals if s in ['buy', 'opportunity', 'golden_cross'])
        sell_signals = sum(1 for s in signals if s in ['sell', 'caution', 'death_cross'])

        if buy_signals > sell_signals:
            return '偏多'
        elif sell_signals > buy_signals:
            return '偏空'
        else:
            return '中性'

    def _analyze_bollinger_bands(self, bollinger, current_price):
        """分析布林带"""
        if len(bollinger['upper']) == 0:
            return {'status': '数据不足'}

        upper = bollinger['upper'][-1]
        middle = bollinger['middle'][-1]
        lower = bollinger['lower'][-1]
        position = bollinger['position'][-1]
        width = bollinger['width'][-1]

        # 判断位置
        if position > 0.8:
            position_desc = '接近上轨，可能回调'
        elif position < 0.2:
            position_desc = '接近下轨，可能反弹'
        else:
            position_desc = '位于中轨附近，震荡状态'

        # 判断宽度
        if width > 0.1:
            width_desc = '带宽较宽，波动性高'
        elif width < 0.05:
            width_desc = '带宽较窄，可能突破'
        else:
            width_desc = '带宽正常'

        return {
            'upper': upper,
            'middle': middle,
            'lower': lower,
            'position': position,
            'width': width,
            'position_desc': position_desc,
            'width_desc': width_desc
        }

    def _analyze_atr(self, atr, data):
        """分析ATR"""
        if len(atr) == 0:
            return {'status': '数据不足'}

        current_atr = atr[-1]
        current_price = data['close'].iloc[-1]
        atr_pct = current_atr / current_price * 100

        # ATR百分位
        recent_atr = atr[-20:] if len(atr) >= 20 else atr
        atr_percentile = (np.sum(recent_atr <= current_atr) / len(recent_atr)) * 100

        if atr_percentile > 80:
            level = '极高'
        elif atr_percentile > 60:
            level = '较高'
        elif atr_percentile > 40:
            level = '正常'
        elif atr_percentile > 20:
            level = '较低'
        else:
            level = '极低'

        return {
            'value': current_atr,
            'percentage': atr_pct,
            'percentile': atr_percentile,
            'level': level
        }

    def _generate_volatility_suggestion(self, volatility_level):
        """生成波动性交易建议"""
        suggestions = {
            '极高': '波动性极高，建议减少仓位，设置较宽的止损',
            '较高': '波动性较高，注意风险控制，适当调整仓位',
            '正常': '波动性正常，可以正常交易',
            '较低': '波动性较低，可以适当增加仓位，但注意突破',
            '极低': '波动性极低，关注突破机会，但要防范假突破'
        }
        return suggestions.get(volatility_level, '波动性状态不明确')

    def _analyze_volume_trend(self, volume_data):
        """分析成交量趋势"""
        volume_ratio = volume_data['volume_ratio']

        if len(volume_ratio) == 0:
            return {'status': '数据不足'}

        current_ratio = volume_ratio[-1]

        if current_ratio > 2.0:
            status = '成交量大幅放大'
            description = '成交量显著增加，市场活跃度高'
        elif current_ratio > 1.5:
            status = '成交量放大'
            description = '成交量有所增加，关注趋势延续'
        elif current_ratio < 0.5:
            status = '成交量萎缩'
            description = '成交量明显减少，市场观望情绪浓厚'
        else:
            status = '成交量正常'
            description = '成交量处于正常水平'

        return {
            'status': status,
            'description': description,
            'ratio': current_ratio
        }

    def _analyze_price_volume_relation(self, volume_data, data):
        """分析价量关系"""
        if volume_data['price_volume_divergence']:
            return {
                'status': '价量背离',
                'description': '价格与成交量走势出现背离，需要关注趋势变化',
                'signal': 'warning'
            }
        else:
            return {
                'status': '价量配合',
                'description': '价格与成交量走势配合良好',
                'signal': 'normal'
            }

    def _analyze_obv(self, obv, data):
        """分析OBV"""
        if len(obv) < 10:
            return {'status': '数据不足'}

        # OBV趋势
        recent_obv = obv[-10:]
        obv_trend = 1 if recent_obv[-1] > recent_obv[0] else -1 if recent_obv[-1] < recent_obv[0] else 0

        # 价格趋势
        recent_prices = data['close'].iloc[-10:].values
        price_trend = 1 if recent_prices[-1] > recent_prices[0] else -1 if recent_prices[-1] < recent_prices[0] else 0

        # 判断背离
        if obv_trend * price_trend < 0:
            status = 'OBV背离'
            description = 'OBV与价格走势背离，可能预示趋势转变'
        else:
            status = 'OBV配合'
            description = 'OBV与价格走势配合，趋势较为可靠'

        return {
            'status': status,
            'description': description,
            'trend': obv_trend,
            'value': obv[-1]
        }

    def _identify_key_levels(self, resistance_distances, support_distances):
        """识别关键支撑压力位"""
        key_levels = []

        # 最近的强支撑
        if support_distances:
            nearest_support = min(support_distances, key=lambda x: x['distance_pct'])
            if nearest_support['distance_pct'] < 5:  # 5%以内
                key_levels.append({
                    'type': '关键支撑',
                    'price': nearest_support['price'],
                    'distance': nearest_support['distance_pct'],
                    'strength': nearest_support['strength']
                })

        # 最近的强压力
        if resistance_distances:
            nearest_resistance = min(resistance_distances, key=lambda x: x['distance_pct'])
            if nearest_resistance['distance_pct'] < 5:  # 5%以内
                key_levels.append({
                    'type': '关键压力',
                    'price': nearest_resistance['price'],
                    'distance': nearest_resistance['distance_pct'],
                    'strength': nearest_resistance['strength']
                })

        return key_levels

    def _generate_overall_assessment(self, analysis):
        """生成综合评估"""
        # 基础评分
        score = 50

        # 趋势评分
        trend_direction = analysis['trend']['overall_direction']
        if '强势上升' in trend_direction:
            score += 25
        elif '温和上升' in trend_direction:
            score += 15
        elif '强势下降' in trend_direction:
            score -= 25
        elif '温和下降' in trend_direction:
            score -= 15

        # 动量评分
        momentum = analysis['momentum']['overall_momentum']
        if momentum == '偏多':
            score += 10
        elif momentum == '偏空':
            score -= 10

        # 成交量评分
        volume_status = analysis['volume']['trend']['status']
        if '放大' in volume_status and score > 50:
            score += 5
        elif '萎缩' in volume_status:
            score -= 5

        # 反转信号调整
        reversal_count = len(analysis['reversal_signals'])
        if reversal_count > 2:
            score -= 10

        # 确定建议
        if score >= 75:
            recommendation = '强烈看多'
        elif score >= 60:
            recommendation = '谨慎看多'
        elif score <= 25:
            recommendation = '强烈看空'
        elif score <= 40:
            recommendation = '谨慎看空'
        else:
            recommendation = '观望等待'

        # 风险等级
        volatility = analysis['volatility']['level']
        if volatility in ['极高', '较高']:
            risk_level = '较高'
        elif volatility in ['极低']:
            risk_level = '较低'
        else:
            risk_level = '中等'

        return {
            'score': max(0, min(100, score)),
            'recommendation': recommendation,
            'risk_level': risk_level,
            'key_points': self._extract_key_points(analysis)
        }

    def _extract_key_points(self, analysis):
        """提取关键要点"""
        points = []

        # 趋势要点
        trend = analysis['trend']
        points.append(f"整体趋势：{trend['overall_direction']}")

        if trend['consistency']['status'] != 'mixed':
            points.append(f"趋势一致性：{trend['consistency']['description']}")

        # 动量要点
        momentum = analysis['momentum']
        if momentum['overall_momentum'] != '中性':
            points.append(f"动量方向：{momentum['overall_momentum']}")

        # 波动性要点
        volatility = analysis['volatility']
        points.append(f"波动性：{volatility['level']}")

        # 关键位置
        sr = analysis['support_resistance']
        if sr['key_levels']:
            for level in sr['key_levels']:
                points.append(f"{level['type']}：{level['price']:.2f} (距离{level['distance']:.1f}%)")

        # 重要信号
        signals = analysis['reversal_signals']
        high_prob_signals = [s for s in signals if s['probability'] == 'high']
        if high_prob_signals:
            points.append(f"重要信号：{high_prob_signals[0]['description']}")

        return points

    def _analyze_oi_trend(self, open_interest):
        """分析持仓量趋势"""
        if len(open_interest) < 10:
            return {'status': '数据不足', 'direction': 'unknown'}

        recent_oi = open_interest[-10:]
        oi_change = (recent_oi[-1] - recent_oi[0]) / recent_oi[0] * 100

        if oi_change > 5:
            direction = '增长'
            description = '持仓量显著增长，市场参与度提高'
        elif oi_change > 2:
            direction = '温和增长'
            description = '持仓量温和增长，市场活跃度上升'
        elif oi_change < -5:
            direction = '下降'
            description = '持仓量显著下降，市场参与度降低'
        elif oi_change < -2:
            direction = '温和下降'
            description = '持仓量温和下降，市场活跃度减弱'
        else:
            direction = '稳定'
            description = '持仓量相对稳定'

        return {
            'direction': direction,
            'change_rate': oi_change,
            'description': description
        }

    def _analyze_price_oi_relation(self, prices, open_interest):
        """分析价格与持仓量关系"""
        if len(prices) < 10 or len(open_interest) < 10:
            return {'status': '数据不足'}

        # 计算最近10期的变化
        price_change = (prices[-1] - prices[-10]) / prices[-10]
        oi_change = (open_interest[-1] - open_interest[-10]) / open_interest[-10]

        # 判断关系类型
        if price_change > 0 and oi_change > 0:
            relation = '价涨量增'
            signal = '多头建仓'
            strength = 'strong_bullish'
        elif price_change > 0 and oi_change < 0:
            relation = '价涨量减'
            signal = '空头平仓'
            strength = 'weak_bullish'
        elif price_change < 0 and oi_change > 0:
            relation = '价跌量增'
            signal = '空头建仓'
            strength = 'strong_bearish'
        elif price_change < 0 and oi_change < 0:
            relation = '价跌量减'
            signal = '多头平仓'
            strength = 'weak_bearish'
        else:
            relation = '价量平衡'
            signal = '观望'
            strength = 'neutral'

        return {
            'relation': relation,
            'signal': signal,
            'strength': strength,
            'price_change': price_change * 100,
            'oi_change': oi_change * 100
        }

    def _calculate_oi_change_rate(self, open_interest):
        """计算持仓量变化率"""
        if len(open_interest) < 2:
            return 0

        return (open_interest[-1] - open_interest[-2]) / open_interest[-2] * 100

    def _generate_oi_analysis(self, oi_trend, price_oi_relation, oi_change_rate):
        """生成持仓量分析结论"""
        analysis = []

        # 持仓量趋势分析
        analysis.append(f"持仓量趋势: {oi_trend['direction']}")
        analysis.append(f"趋势描述: {oi_trend['description']}")

        # 价格持仓量关系分析
        if 'relation' in price_oi_relation:
            analysis.append(f"价量关系: {price_oi_relation['relation']}")
            analysis.append(f"市场信号: {price_oi_relation['signal']}")

        # 变化率分析
        if abs(oi_change_rate) > 3:
            analysis.append(f"持仓量日变化: {oi_change_rate:.2f}% (显著变化)")
        elif abs(oi_change_rate) > 1:
            analysis.append(f"持仓量日变化: {oi_change_rate:.2f}% (温和变化)")
        else:
            analysis.append(f"持仓量日变化: {oi_change_rate:.2f}% (变化较小)")

        return analysis