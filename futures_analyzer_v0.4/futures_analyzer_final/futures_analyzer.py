#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货技术分析库 v0.2 - 最终版本
简化的期货技术分析工具，支持直接输入合约代码进行分析
"""

import os
import sys
import logging

from typing import Optional, Dict, Any, Tuple, List

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from local_data_loader import LocalDataLoader
from local_technical_indicators import LocalTechnicalIndicators
from local_analysis_engine import LocalAnalysisEngine
from local_report_generator import LocalReportGenerator

# 配置日志
logging.basicConfig(level=logging.ERROR, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FuturesAnalyzer:
    """
    期货技术分析器 - 最终版本

    功能：
    1. 支持 交易所/合约代码 格式输入
    2. 自动选择最佳K线周期进行分析
    3. 结果保存到文件
    4. 提供Python库形式的API
    """

    def __init__(self, data_dir: str = "/Users/<USER>/Downloads/data_index_0704",
                 output_dir: str = "analysis_results"):
        """
        初始化分析器

        Args:
            data_dir: 数据目录路径
            output_dir: 输出目录路径
        """
        self.data_loader = LocalDataLoader(data_dir)
        self.calculator = LocalTechnicalIndicators()
        self.analysis_engine = LocalAnalysisEngine()
        self.report_generator = LocalReportGenerator()
        self.output_dir = output_dir
        self.supported_exchanges = ['SHFE', 'DCE', 'ZCE', 'CFFEX', 'GFEX']

        # 创建输出目录
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        logger.info("期货分析器初始化完成")
    
    def validate_symbol(self, symbol: str) -> Tuple[bool, str]:
        """验证合约代码"""
        if not symbol or '/' not in symbol:
            return False, "格式错误：请使用 '交易所/合约代码' 格式，如 CFFEX/IF2509"

        try:
            parts = symbol.split('/')
            if len(parts) != 2:
                return False, "格式错误：请使用 '交易所/合约代码' 格式"

            exchange, contract = parts[0].strip().upper(), parts[1].strip()

            if exchange not in self.supported_exchanges:
                return False, f"不支持的交易所: {exchange}，支持的交易所: {', '.join(self.supported_exchanges)}"

            # 检查交易所是否存在
            available_exchanges = self.data_loader.list_available_exchanges()
            if exchange not in available_exchanges:
                return False, f"交易所 {exchange} 无可用数据"

            # 检查合约是否存在
            contracts = self.data_loader.list_contracts_by_exchange(exchange)
            if contract not in contracts:
                return False, f"合约 {contract} 在 {exchange} 中不存在"

            return True, ""

        except Exception as e:
            return False, f"验证失败: {str(e)}"
    
    def analyze_contract(self, exchange: str, contract: str, period: str) -> Optional[Dict[str, Any]]:
        """分析单个合约的单个周期"""
        try:
            # 加载数据
            data = self.data_loader.load_contract_data(exchange, contract, period)

            if data is None or len(data) < 60:
                return None

            # 计算技术指标
            indicators = self.calculator.calculate_all_indicators(data)

            if indicators is None:
                return None

            # 使用分析引擎进行完整分析
            contract_code = f"{exchange}.{contract}"
            analysis = self.analysis_engine.generate_comprehensive_analysis(indicators, data, contract_code, period)

            return analysis

        except Exception as e:
            logger.error(f"分析 {period} 失败: {e}")
            return None



    def save_analysis_to_file(self, analysis: Dict[str, Any]) -> Optional[str]:
        """保存分析结果到文件"""
        try:
            timestamp = analysis['timestamp'].strftime("%Y%m%d_%H%M%S")
            # 从contract字段解析交易所和合约代码
            contract_parts = analysis['contract'].split('.')
            if len(contract_parts) == 2:
                exchange, contract = contract_parts
            else:
                exchange, contract = 'UNKNOWN', analysis['contract']

            filename = f"analysis_{exchange}_{contract}_{analysis['period']}_{timestamp}.txt"

            # 使用报告生成器保存完整报告
            self.report_generator.save_detailed_report(analysis, filename)

            # 返回完整路径
            file_path = os.path.join(self.output_dir, filename)
            return file_path
        except Exception as e:
            logger.error(f"保存文件失败: {e}")
            return None
    
    def analyze(self, symbol: str) -> Dict[str, Any]:
        """
        主要分析方法

        Args:
            symbol: 合约代码，格式为 "交易所/合约代码" 如 "CFFEX/IF2509"

        Returns:
            dict: 分析结果
        """
        # 验证输入
        is_valid, error_msg = self.validate_symbol(symbol)
        if not is_valid:
            return {
                'success': False,
                'error': error_msg,
                'symbol': symbol
            }

        # 解析合约代码
        exchange, contract = symbol.split('/')
        exchange, contract = exchange.strip().upper(), contract.strip()

        # 检查可用周期
        periods = self.data_loader.get_available_periods(exchange, contract)

        if not periods:
            return {
                'success': False,
                'error': f'未找到 {symbol} 的任何可用数据',
                'symbol': symbol
            }

        # 分析每个周期
        results = {}
        saved_files = []

        for period in periods:
            try:
                analysis = self.analyze_contract(exchange, contract, period)

                if analysis:
                    # 保存文件
                    file_path = self.save_analysis_to_file(analysis)
                    if file_path:
                        saved_files.append(file_path)
                        results[period] = analysis

            except Exception as e:
                logger.error(f"分析 {period} 失败: {e}")
                continue

        if results:
            return {
                'success': True,
                'symbol': symbol,
                'exchange': exchange,
                'contract': contract,
                'analyzed_periods': list(results.keys()),
                'saved_files': saved_files,
                'results': results
            }
        else:
            return {
                'success': False,
                'error': '所有周期的数据分析都失败',
                'symbol': symbol
            }

    def get_available_symbols(self) -> List[str]:
        """获取所有可用的合约代码"""
        symbols = []

        exchanges = self.data_loader.list_available_exchanges()
        for exchange in exchanges:
            contracts = self.data_loader.list_contracts_by_exchange(exchange)
            for contract in contracts:
                symbols.append(f"{exchange}/{contract}")

        return symbols

    def _describe_trend(self, trend_value: float) -> str:
        """描述趋势"""
        if trend_value > 0.01:
            return "上升"
        elif trend_value < -0.01:
            return "下降"
        else:
            return "震荡"

# 便捷函数
def analyze_futures(symbol: str, data_dir: str = "/Users/<USER>/Downloads/data_index_0704",
                   output_dir: str = "analysis_results") -> Dict[str, Any]:
    """
    便捷的期货分析函数

    Args:
        symbol: 合约代码，格式为 "交易所/合约代码"
        data_dir: 数据目录路径
        output_dir: 输出目录路径

    Returns:
        dict: 分析结果
    """
    analyzer = FuturesAnalyzer(data_dir, output_dir)
    return analyzer.analyze(symbol)
