#!/usr/bin/env python3
"""
测试远程数据可用性
"""

from remote_data_loader import RemoteDataLoader

def test_contracts():
    """测试不同合约的数据可用性"""
    loader = RemoteDataLoader()
    
    # 测试合约列表
    test_contracts = [
        ('CFFEX', 'IF2509'),
        ('GFEX', 'lc2509'),
        ('SHFE', 'ag2508'),
        ('SHFE', 'au2510'),
        ('DCE', 'a2509'),
        ('ZCE', 'MA509')
    ]
    
    print("🔍 测试远程数据可用性")
    print("="*60)
    
    for exchange, contract in test_contracts:
        print(f"\n📊 测试 {exchange}/{contract}")
        
        # 测试日线数据
        daily_data = loader.load_contract_data(exchange, contract, '日线')
        daily_count = len(daily_data) if daily_data is not None else 0
        
        # 测试15分钟线数据
        minute_data = loader.load_contract_data(exchange, contract, '15分钟线')
        minute_count = len(minute_data) if minute_data is not None else 0
        
        print(f"  日线数据: {daily_count} 条")
        print(f"  15分钟线数据: {minute_count} 条")
        
        # 判断是否可用于分析
        if daily_count >= 60 and minute_count >= 100:
            print(f"  ✅ 可用于分析")
        else:
            print(f"  ❌ 数据不足 (需要日线≥60条，15分钟线≥100条)")

if __name__ == "__main__":
    test_contracts()
