"""
本地数据版本的报告生成器
"""

import os
from datetime import datetime

class LocalReportGenerator:
    """本地数据报告生成器"""
    
    def __init__(self, output_dir="analysis_results"):
        """初始化报告生成器"""
        self.output_dir = output_dir
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
    
    def print_analysis_report(self, analysis):
        """打印分析报告到控制台"""
        print("="*80)
        print(f"📊 期货技术分析报告 - {analysis['contract']}")
        print("="*80)
        
        # 基本信息
        print(f"\n📈 基本信息:")
        print(f"  合约代码: {analysis['contract']}")
        print(f"  数据周期: {analysis['period']}")
        print(f"  当前价格: {analysis['current_price']:.2f}")
        print(f"  当前成交量: {analysis['current_volume']:,}")
        print(f"  当前持仓量: {analysis['current_open_interest']:,}")
        print(f"  数据范围: {analysis['data_period']}")
        print(f"  数据量: {analysis['total_records']} 条记录")
        
        # 趋势分析
        self._print_trend_analysis(analysis['trend'])
        
        # 动量分析
        self._print_momentum_analysis(analysis['momentum'])
        
        # 波动性分析
        self._print_volatility_analysis(analysis['volatility'])
        
        # 成交量分析
        self._print_volume_analysis(analysis['volume'])

        # 持仓量分析
        self._print_open_interest_analysis(analysis.get('open_interest', {}))

        # 支撑压力位
        self._print_support_resistance(analysis['support_resistance'])

        # 反转信号
        self._print_reversal_signals(analysis['reversal_signals'])

        # 综合评估
        self._print_overall_assessment(analysis['overall_assessment'])
        
        print("="*80)
    
    def _print_trend_analysis(self, trend):
        """打印趋势分析"""
        print(f"\n📈 趋势分析:")
        print(f"  整体方向: {trend['overall_direction']}")
        print(f"  短期趋势: {trend['short_term']}")
        print(f"  中期趋势: {trend['medium_term']}")
        print(f"  长期趋势: {trend['long_term']}")
        
        # 移动平均线
        ma = trend['moving_averages']
        print(f"  均线状态: {ma['status']}")
        print(f"  MA20: {ma['ma20']:.2f} (偏离: {ma['price_vs_ma20']:.2f}%)")
        print(f"  MA60: {ma['ma60']:.2f} (偏离: {ma['price_vs_ma60']:.2f}%)")
        
        # 趋势强度
        strength = trend['strength']
        print(f"  趋势强度: {strength['level']} ({strength['strength']:.3f})")
        
        # 趋势一致性
        consistency = trend['consistency']
        print(f"  一致性: {consistency['status']}")
    
    def _print_momentum_analysis(self, momentum):
        """打印动量分析"""
        print(f"\n⚡ 动量分析:")
        print(f"  整体动量: {momentum['overall_momentum']}")
        
        # RSI
        rsi = momentum['rsi']
        print(f"  RSI: {rsi['value']:.1f} ({rsi['status']})")
        
        # KDJ
        kdj = momentum['kdj']
        print(f"  KDJ: K={kdj['k']:.1f}, D={kdj['d']:.1f}, J={kdj['j']:.1f}")
        print(f"       {kdj['description']}")
        
        # MACD
        macd = momentum['macd']
        print(f"  MACD: {macd['macd']:.4f}, Signal: {macd['signal']:.4f}")
        print(f"        {macd['description']}")
    
    def _print_volatility_analysis(self, volatility):
        """打印波动性分析"""
        print(f"\n📊 波动性分析:")
        print(f"  波动性等级: {volatility['level']}")
        
        # 详细分析
        analysis = volatility['analysis']
        print(f"  历史波动率: {analysis['historical_vol']:.4f}")
        print(f"  ATR波动率: {analysis['atr_vol']:.4f}")
        print(f"  波动率百分位: {analysis['percentile']:.1f}%")
        
        # 布林带
        bollinger = volatility['bollinger']
        if 'position' in bollinger:
            print(f"  布林带位置: {bollinger['position']:.3f} ({bollinger['position_desc']})")
            print(f"  布林带宽度: {bollinger['width']:.3f} ({bollinger['width_desc']})")
        
        # 交易建议
        print(f"  交易建议: {volatility['trading_suggestion']}")
    
    def _print_volume_analysis(self, volume):
        """打印成交量分析"""
        print(f"\n📦 成交量分析:")
        
        # 成交量趋势
        trend = volume['trend']
        print(f"  成交量状态: {trend['status']} (比率: {trend['ratio']:.2f})")
        print(f"  描述: {trend['description']}")
        
        # 价量关系
        pv_relation = volume['price_volume_relation']
        print(f"  价量关系: {pv_relation['status']}")
        print(f"  描述: {pv_relation['description']}")
        
        # OBV
        obv = volume['obv']
        if 'status' in obv:
            print(f"  OBV状态: {obv['status']}")
            print(f"  OBV描述: {obv['description']}")
        
        # 特殊情况
        if volume['volume_surge']:
            print(f"  ⚠️ 成交量异常放大")

        # 检查价量背离
        if volume['divergence']:
            print(f"  ⚠️ 价量背离")

    def _print_open_interest_analysis(self, oi_analysis):
        """打印持仓量分析"""
        print(f"\n📊 持仓量分析:")

        if not oi_analysis or oi_analysis.get('status') == '无持仓量数据':
            print("  暂无持仓量数据")
            return

        print(f"  当前持仓量: {oi_analysis['current_oi']:,}")

        # 持仓量趋势
        oi_trend = oi_analysis['oi_trend']
        print(f"  持仓量趋势: {oi_trend['direction']} ({oi_trend['change_rate']:.2f}%)")
        print(f"  趋势描述: {oi_trend['description']}")

        # 价格持仓量关系
        price_oi = oi_analysis['price_oi_relation']
        if 'relation' in price_oi:
            print(f"  价量关系: {price_oi['relation']}")
            print(f"  市场信号: {price_oi['signal']}")
            print(f"  价格变化: {price_oi['price_change']:.2f}%")
            print(f"  持仓变化: {price_oi['oi_change']:.2f}%")

        # 持仓量变化率
        oi_change = oi_analysis['oi_change_rate']
        print(f"  日变化率: {oi_change:.2f}%")

        # 分析结论
        if 'oi_analysis' in oi_analysis:
            print(f"  分析结论:")
            for conclusion in oi_analysis['oi_analysis'][:3]:  # 只显示前3条
                print(f"    • {conclusion}")

    def _print_support_resistance(self, sr):
        """打印支撑压力位"""
        print(f"\n📍 支撑压力位:")
        print(f"  当前价格: {sr['current_price']:.2f}")
        
        # 压力位
        if sr['nearest_resistance']:
            print(f"  关键压力位:")
            for i, r in enumerate(sr['nearest_resistance'][:3], 1):
                print(f"    {i}. {r['price']:.2f} (上方 {r['distance_pct']:.1f}%, 强度: {r['strength']})")
        
        # 支撑位
        if sr['nearest_support']:
            print(f"  关键支撑位:")
            for i, s in enumerate(sr['nearest_support'][:3], 1):
                print(f"    {i}. {s['price']:.2f} (下方 {s['distance_pct']:.1f}%, 强度: {s['strength']})")
        
        # 关键位置
        if sr['key_levels']:
            print(f"  重点关注:")
            for level in sr['key_levels']:
                print(f"    • {level['type']}: {level['price']:.2f} (距离 {level['distance']:.1f}%)")
    
    def _print_reversal_signals(self, signals):
        """打印反转信号"""
        if not signals:
            return
        
        print(f"\n🔄 反转信号:")
        for signal in signals:
            probability_icon = "🔴" if signal['probability'] == 'high' else "🟡" if signal['probability'] == 'medium' else "🟢"
            print(f"  {probability_icon} {signal['type']}: {signal['description']}")
            print(f"     建议: {signal['action']}")
    
    def _print_overall_assessment(self, assessment):
        """打印综合评估"""
        print(f"\n🎯 综合评估:")
        print(f"  综合评分: {assessment['score']}/100")
        print(f"  投资建议: {assessment['recommendation']}")
        print(f"  风险等级: {assessment['risk_level']}")
    
    def save_detailed_report(self, analysis, filename=None):
        """保存详细报告到文件"""
        if filename is None:
            timestamp = analysis['timestamp'].strftime('%Y%m%d_%H%M%S')
            filename = f"analysis_result_{analysis['contract']}_{timestamp}.txt"
        
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            # 写入报告头部
            f.write("期货技术分析详细报告\n")
            f.write("="*100 + "\n")
            f.write(f"合约代码: {analysis['contract']}\n")
            f.write(f"数据周期: {analysis['period']}\n")
            f.write(f"当前价格: {analysis['current_price']:.2f}\n")
            f.write(f"当前成交量: {analysis['current_volume']:,}\n")
            f.write(f"当前持仓量: {analysis['current_open_interest']:,}\n")
            f.write(f"数据范围: {analysis['data_period']}\n")
            f.write(f"数据量: {analysis['total_records']} 条记录\n")
            f.write(f"报告生成时间: {analysis['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("="*100 + "\n")
            
            # 写入详细分析
            self._write_detailed_analysis(f, analysis)
        
        print(f"\n📄 详细报告已保存至: {filepath}")
        return filepath
    
    def _write_detailed_analysis(self, f, analysis):
        """写入详细分析内容"""
        # 趋势分析详情
        f.write("\n趋势分析详情\n")
        f.write("-"*50 + "\n")
        trend = analysis['trend']
        f.write(f"整体方向: {trend['overall_direction']}\n")
        f.write(f"短期趋势: {trend['short_term']}\n")
        f.write(f"中期趋势: {trend['medium_term']}\n")
        f.write(f"长期趋势: {trend['long_term']}\n")
        f.write(f"趋势强度: {trend['strength']['level']} ({trend['strength']['strength']:.3f})\n")
        f.write(f"趋势一致性: {trend['consistency']['status']}\n")
        f.write(f"一致性描述: {trend['consistency']['description']}\n")
        
        # 移动平均线详情
        ma = trend['moving_averages']
        f.write(f"\n移动平均线分析:\n")
        f.write(f"  状态: {ma['status']}\n")
        f.write(f"  描述: {ma['description']}\n")
        f.write(f"  MA20: {ma['ma20']:.2f} (价格偏离: {ma['price_vs_ma20']:.2f}%)\n")
        f.write(f"  MA60: {ma['ma60']:.2f} (价格偏离: {ma['price_vs_ma60']:.2f}%)\n")
        
        # 动量分析详情
        f.write("\n动量分析详情\n")
        f.write("-"*50 + "\n")
        momentum = analysis['momentum']
        f.write(f"整体动量: {momentum['overall_momentum']}\n")
        
        # RSI详情
        rsi = momentum['rsi']
        f.write(f"\nRSI分析:\n")
        f.write(f"  数值: {rsi['value']:.2f}\n")
        f.write(f"  状态: {rsi['status']}\n")
        f.write(f"  信号: {rsi['signal']}\n")
        
        # KDJ详情
        kdj = momentum['kdj']
        f.write(f"\nKDJ分析:\n")
        f.write(f"  K值: {kdj['k']:.2f}\n")
        f.write(f"  D值: {kdj['d']:.2f}\n")
        f.write(f"  J值: {kdj['j']:.2f}\n")
        f.write(f"  信号: {kdj['signal']}\n")
        f.write(f"  描述: {kdj['description']}\n")
        
        # MACD详情
        macd = momentum['macd']
        f.write(f"\nMACD分析:\n")
        f.write(f"  MACD: {macd['macd']:.4f}\n")
        f.write(f"  Signal: {macd['signal']:.4f}\n")
        f.write(f"  Histogram: {macd['histogram']:.4f}\n")
        f.write(f"  交叉信号: {macd['cross_signal']}\n")
        f.write(f"  描述: {macd['description']}\n")
        
        # 波动性分析详情
        f.write("\n波动性分析详情\n")
        f.write("-"*50 + "\n")
        volatility = analysis['volatility']
        f.write(f"波动性等级: {volatility['level']}\n")
        
        vol_analysis = volatility['analysis']
        f.write(f"历史波动率: {vol_analysis['historical_vol']:.4f}\n")
        f.write(f"ATR波动率: {vol_analysis['atr_vol']:.4f}\n")
        f.write(f"波动率百分位: {vol_analysis['percentile']:.1f}%\n")
        
        # 布林带详情
        bollinger = volatility['bollinger']
        if 'position' in bollinger:
            f.write(f"\n布林带分析:\n")
            f.write(f"  上轨: {bollinger['upper']:.2f}\n")
            f.write(f"  中轨: {bollinger['middle']:.2f}\n")
            f.write(f"  下轨: {bollinger['lower']:.2f}\n")
            f.write(f"  位置: {bollinger['position']:.3f}\n")
            f.write(f"  宽度: {bollinger['width']:.3f}\n")
            f.write(f"  位置描述: {bollinger['position_desc']}\n")
            f.write(f"  宽度描述: {bollinger['width_desc']}\n")
        
        f.write(f"\n交易建议: {volatility['trading_suggestion']}\n")

        # 成交量分析详情
        f.write("\n成交量分析详情\n")
        f.write("-"*50 + "\n")
        volume = analysis['volume']
        
        trend = volume['trend']
        f.write(f"成交量趋势:\n")
        f.write(f"  状态: {trend['status']}\n")
        f.write(f"  比率: {trend['ratio']:.2f}\n")
        f.write(f"  描述: {trend['description']}\n")
        
        pv_relation = volume['price_volume_relation']
        f.write(f"\n价量关系:\n")
        f.write(f"  状态: {pv_relation['status']}\n")
        f.write(f"  描述: {pv_relation['description']}\n")
        f.write(f"  信号: {pv_relation['signal']}\n")
        
        obv = volume['obv']
        if 'status' in obv:
            f.write(f"\nOBV分析:\n")
            f.write(f"  状态: {obv['status']}\n")
            f.write(f"  描述: {obv['description']}\n")
            f.write(f"  趋势: {obv['trend']}\n")
            f.write(f"  数值: {obv['value']:.0f}\n")

        # 持仓量分析详情
        f.write("\n持仓量分析详情\n")
        f.write("-"*50 + "\n")
        oi_analysis = analysis.get('open_interest', {})
        if oi_analysis and oi_analysis.get('status') != '无持仓量数据':
            f.write(f"当前持仓量: {oi_analysis['current_oi']:,}\n")

            oi_trend = oi_analysis['oi_trend']
            f.write(f"\n持仓量趋势:\n")
            f.write(f"  方向: {oi_trend['direction']}\n")
            f.write(f"  变化率: {oi_trend['change_rate']:.2f}%\n")
            f.write(f"  描述: {oi_trend['description']}\n")

            price_oi = oi_analysis['price_oi_relation']
            if 'relation' in price_oi:
                f.write(f"\n价格持仓量关系:\n")
                f.write(f"  关系类型: {price_oi['relation']}\n")
                f.write(f"  市场信号: {price_oi['signal']}\n")
                f.write(f"  强度: {price_oi['strength']}\n")
                f.write(f"  价格变化: {price_oi['price_change']:.2f}%\n")
                f.write(f"  持仓变化: {price_oi['oi_change']:.2f}%\n")

            f.write(f"\n持仓量日变化率: {oi_analysis['oi_change_rate']:.2f}%\n")

            if 'oi_analysis' in oi_analysis:
                f.write(f"\n分析结论:\n")
                for conclusion in oi_analysis['oi_analysis']:
                    f.write(f"  • {conclusion}\n")
        else:
            f.write("无持仓量数据\n")

        # 支撑压力位详情
        f.write("\n支撑压力位详情\n")
        f.write("-"*50 + "\n")
        sr = analysis['support_resistance']
        f.write(f"当前价格: {sr['current_price']:.2f}\n")
        
        if sr['nearest_resistance']:
            f.write(f"\n关键压力位:\n")
            for i, r in enumerate(sr['nearest_resistance'], 1):
                f.write(f"  {i}. 价格: {r['price']:.2f}\n")
                f.write(f"     距离: 上方 {r['distance_pct']:.2f}%\n")
                f.write(f"     强度: {r['strength']}\n\n")
        
        if sr['nearest_support']:
            f.write(f"关键支撑位:\n")
            for i, s in enumerate(sr['nearest_support'], 1):
                f.write(f"  {i}. 价格: {s['price']:.2f}\n")
                f.write(f"     距离: 下方 {s['distance_pct']:.2f}%\n")
                f.write(f"     强度: {s['strength']}\n\n")
        
        if sr['key_levels']:
            f.write(f"重点关注位置:\n")
            for level in sr['key_levels']:
                f.write(f"  {level['type']}: {level['price']:.2f}\n")
                f.write(f"  距离: {level['distance']:.2f}%\n")
                f.write(f"  强度: {level['strength']}\n\n")
        
        # 反转信号详情
        if analysis['reversal_signals']:
            f.write("反转信号详情\n")
            f.write("-"*50 + "\n")
            for signal in analysis['reversal_signals']:
                f.write(f"信号类型: {signal['type']}\n")
                f.write(f"描述: {signal['description']}\n")
                f.write(f"概率: {signal['probability']}\n")
                f.write(f"建议行动: {signal['action']}\n\n")
        
        # 综合评估详情
        f.write("综合评估详情\n")
        f.write("-"*50 + "\n")
        assessment = analysis['overall_assessment']
        f.write(f"综合评分: {assessment['score']}/100\n")
        f.write(f"投资建议: {assessment['recommendation']}\n")
        f.write(f"风险等级: {assessment['risk_level']}\n")
        
        f.write(f"\n关键要点:\n")
        for point in assessment['key_points']:
            f.write(f"  • {point}\n")
