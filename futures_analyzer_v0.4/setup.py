#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货技术分析库 v0.2 安装脚本 - 最终版本
"""

from setuptools import setup
import os

# 读取README文件
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "期货技术分析库 v0.2 - 最终版本"

# 读取requirements
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return ['pandas>=1.3.0', 'numpy>=1.20.0']

setup(
    name="futures-analyzer-final",
    version="0.2.0",
    author="Futures Analysis Team",
    author_email="<EMAIL>",
    description="专业的期货技术分析工具库 - 最终版本",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/futures-analysis/futures-analyzer-final",
    py_modules=[
        'futures_analyzer',
        'local_data_loader',
        'local_technical_indicators',
        'local_analysis_engine',
        'local_report_generator',
        'main'
    ],
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Financial and Insurance Industry",
        "Topic :: Office/Business :: Financial :: Investment",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
    ],
    python_requires=">=3.7",
    install_requires=read_requirements(),
    entry_points={
        'console_scripts': [
            'futures-analyzer=main:main',
        ],
    },
    include_package_data=True,
    package_data={
        '': ['*.txt', '*.md', '*.yml', '*.yaml', '*.py'],
    },
    keywords="futures trading technical-analysis finance quantitative",
    project_urls={
        "Bug Reports": "https://github.com/futures-analysis/futures-analyzer-final/issues",
        "Source": "https://github.com/futures-analysis/futures-analyzer-final",
        "Documentation": "https://futures-analyzer-final.readthedocs.io/",
    },
)
