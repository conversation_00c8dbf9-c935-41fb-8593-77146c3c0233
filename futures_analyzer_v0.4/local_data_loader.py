"""
本地数据加载器
从本地数据文件中加载期货数据
支持日线和15分钟线数据
统一的数据清洗和字段提取逻辑
"""

import os
import pandas as pd
from datetime import datetime, timedelta
import logging
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LocalDataLoader:
    def __init__(self, data_dir="/Users/<USER>/Downloads/data_index_0704"):
        """
        初始化本地数据加载器

        Args:
            data_dir: 数据目录路径
        """
        self.data_dir = data_dir
        self.exchanges = ['SHFE', 'DCE', 'ZCE', 'CFFEX', 'GFEX']  # 支持的交易所列表

        # 数据清洗统计
        self.cleaning_stats = {
            'total_processed': 0,
            'dirty_records_cleaned': 0,
            'invalid_records_removed': 0
        }

    def list_available_exchanges(self):
        """列出所有可用的交易所"""
        available_exchanges = []
        for exchange in self.exchanges:
            exchange_dir = os.path.join(self.data_dir, exchange)
            if os.path.exists(exchange_dir):
                available_exchanges.append(exchange)
        return available_exchanges

    def list_contracts_by_exchange(self, exchange):
        """列出指定交易所的所有合约"""
        contracts = []
        exchange_dir = os.path.join(self.data_dir, exchange)
        if os.path.exists(exchange_dir):
            files = os.listdir(exchange_dir)
            contract_set = set()
            for file in files:
                if file.endswith('_Day.txt') or file.endswith('_Minute_15.txt'):
                    if '_Day.txt' in file:
                        contract = file.replace('_Day.txt', '')
                    else:
                        contract = file.replace('_Minute_15.txt', '')
                    contract_set.add(contract)
            contracts = sorted(list(contract_set))
        return contracts

    def get_available_periods(self, exchange, contract):
        """获取指定合约的可用周期"""
        periods = []
        exchange_dir = os.path.join(self.data_dir, exchange)
        if os.path.exists(exchange_dir):
            day_file = os.path.join(exchange_dir, f"{contract}_Day.txt")
            minute_file = os.path.join(exchange_dir, f"{contract}_Minute_15.txt")

            if os.path.exists(day_file):
                periods.append('日线')
            if os.path.exists(minute_file):
                periods.append('15分钟线')
        return periods

    def clean_data_record(self, record):
        """
        清理单条数据记录，删除不符合格式的数据
        参考data_process.py的清洗逻辑

        Args:
            record: 原始数据记录字符串

        Returns:
            tuple: (cleaned_record, is_dirty)
        """
        if not record.strip():
            return None, False

        original_record = record.strip()
        is_dirty = False

        # 检查记录是否以有效交易所开头
        for exchange in self.exchanges:
            if original_record.startswith(exchange):
                # 按逗号分割字段验证
                fields = original_record.split(',')
                if len(fields) >= 13 and fields[0] in self.exchanges:
                    return original_record, is_dirty
                break

        # 如果没有直接匹配，尝试清理异常字符
        for exchange in self.exchanges:
            # 查找交易所名称在记录中的位置
            exchange_pos = original_record.find(exchange)
            if exchange_pos > 0:  # 找到了交易所，但不在开头
                # 提取从交易所开始的部分
                cleaned_record = original_record[exchange_pos:]
                # 验证清理后的记录是否有效
                fields = cleaned_record.split(',')
                if len(fields) >= 13 and fields[0] in self.exchanges:
                    is_dirty = True
                    logger.debug(f"清理异常字符: {original_record[:50]}... -> {cleaned_record[:50]}...")
                    return cleaned_record, is_dirty

        # 如果都无法清理，返回None表示无效记录
        return None, True

    def parse_record_fields(self, record):
        """
        解析记录字段，统一字段提取逻辑
        参考data_process.py的字段映射

        Args:
            record: 清理后的数据记录

        Returns:
            dict: 解析后的字段字典，失败返回None
        """
        try:
            fields = record.split(',')
            if len(fields) < 13:
                return None

            # 统一的字段映射
            parsed_data = {
                'exchange': fields[0].strip(),
                'contract': fields[1].strip(),
                'trading_date': fields[2].strip(),      # 交易日
                'update_time': fields[3].strip(),       # 更新时间
                'update_ms': fields[4].strip(),         # 更新毫秒（通常不用）
                'open': float(fields[5]),               # 开盘价
                'high': float(fields[6]),               # 最高价
                'low': float(fields[7]),                # 最低价
                'close': float(fields[8]),              # 收盘价
                'volume': int(fields[9]),               # 成交量
                'open_interest': int(fields[10]),       # 持仓量
                'prev_open_interest': int(fields[11]),  # 前一根K线的持仓量
                'cumulative_volume': int(fields[12])    # 今日累计成交量
                # fields[13] 是今日累计成交额，通常不可用，跳过
            }

            # 数据合理性验证
            if not self._validate_parsed_data(parsed_data):
                return None

            return parsed_data

        except (ValueError, IndexError) as e:
            logger.debug(f"字段解析失败: {record[:100]}... 错误: {e}")
            return None

    def _validate_parsed_data(self, data):
        """
        验证解析后数据的合理性

        Args:
            data: 解析后的数据字典

        Returns:
            bool: 数据是否有效
        """
        try:
            # 验证交易所
            if data['exchange'] not in self.exchanges:
                return False

            # 验证价格数据
            prices = [data['open'], data['high'], data['low'], data['close']]
            if any(price <= 0 for price in prices):
                return False

            # 验证成交量
            if data['volume'] < 0:
                return False

            # 验证持仓量
            if data['open_interest'] < 0:
                return False

            # 价格逻辑检查
            if data['high'] < data['low']:
                return False

            if data['high'] < max(data['open'], data['close']):
                return False

            if data['low'] > min(data['open'], data['close']):
                return False

            # 异常波动检查（单日涨跌幅超过50%认为异常）
            price_range = data['high'] - data['low']
            avg_price = (data['high'] + data['low']) / 2
            if price_range / avg_price > 0.5:  # 50%的日内波动
                return False

            return True

        except (ValueError, TypeError, ZeroDivisionError):
            return False

    def load_contract_data(self, exchange, contract, period='日线', start_date=None, end_date=None):
        """
        加载指定合约的数据

        Args:
            exchange: 交易所代码
            contract: 合约代码
            period: 数据周期，'日线' 或 '15分钟线'
            start_date: 开始日期，格式 'YYYYMMDD'
            end_date: 结束日期，格式 'YYYYMMDD'

        Returns:
            DataFrame: 包含OHLCV数据的DataFrame
        """
        # 确定文件路径
        if period == '日线':
            filename = f"{contract}_Day.txt"
        elif period == '15分钟线':
            filename = f"{contract}_Minute_15.txt"
        else:
            logger.error(f"不支持的数据周期: {period}")
            return None

        file_path = os.path.join(self.data_dir, exchange, filename)

        if not os.path.exists(file_path):
            logger.error(f"数据文件不存在: {file_path}")
            return None

        logger.info(f"加载数据文件: {file_path}")

        # 读取数据
        data_list = []
        # 尝试不同的编码
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin1']
        content = None

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read().strip()
                    break
            except UnicodeDecodeError:
                continue

        if content is None:
            logger.error(f"无法解码文件: {file_path}")
            return None

        records = content.split(';')

        # 统计数据清洗信息
        original_count = len([r for r in records if r.strip()])
        dirty_count = 0
        invalid_count = 0

        for record in records:
            if not record.strip():
                continue

            # 使用统一的数据清洗方法
            cleaned_record, is_dirty = self.clean_data_record(record)

            if cleaned_record is None:
                invalid_count += 1
                continue

            if is_dirty:
                dirty_count += 1

            # 使用统一的字段解析方法
            parsed_data = self.parse_record_fields(cleaned_record)

            if parsed_data is None:
                invalid_count += 1
                continue

            # 构建datetime
            try:
                if period == '日线':
                    # 日线数据时间通常是 00:00:00
                    dt = datetime.strptime(parsed_data['trading_date'], "%Y%m%d")
                else:
                    # 15分钟线数据
                    datetime_str = f"{parsed_data['trading_date']} {parsed_data['update_time']}"
                    dt = datetime.strptime(datetime_str, "%Y%m%d %H:%M:%S")

                # 构建最终数据记录
                data_list.append({
                    'datetime': dt,
                    'date': parsed_data['trading_date'],
                    'time': parsed_data['update_time'],
                    'open': parsed_data['open'],
                    'high': parsed_data['high'],
                    'low': parsed_data['low'],
                    'close': parsed_data['close'],
                    'volume': parsed_data['volume'],
                    'open_interest': parsed_data['open_interest'],
                    'prev_open_interest': parsed_data['prev_open_interest'],
                    'total_volume': parsed_data['cumulative_volume'],
                    'exchange': parsed_data['exchange'],
                    'contract': parsed_data['contract']
                })

            except (ValueError, KeyError) as e:
                logger.debug(f"日期时间解析失败: {parsed_data.get('trading_date', 'N/A')} {parsed_data.get('update_time', 'N/A')} 错误: {e}")
                invalid_count += 1
                continue

        if not data_list:
            logger.error(f"未能从文件中解析出有效数据: {file_path}")
            return None

        # 更新全局清洗统计
        self.cleaning_stats['total_processed'] += original_count
        self.cleaning_stats['dirty_records_cleaned'] += dirty_count
        self.cleaning_stats['invalid_records_removed'] += invalid_count

        # 数据清洗统计报告
        if dirty_count > 0 or invalid_count > 0:
            clean_rate = (len(data_list) / original_count) * 100 if original_count > 0 else 0
            logger.info(f"数据清洗完成: 原始记录 {original_count}, 有效记录 {len(data_list)}, "
                       f"清理脏数据 {dirty_count}, 移除无效记录 {invalid_count}, 清洗率 {clean_rate:.2f}%")

        df = pd.DataFrame(data_list)
        df = df.sort_values('datetime').reset_index(drop=True)

        # 日期过滤
        if start_date:
            start_dt = datetime.strptime(start_date, "%Y%m%d")
            df = df[df['datetime'] >= start_dt]

        if end_date:
            end_dt = datetime.strptime(end_date, "%Y%m%d") + timedelta(days=1)
            df = df[df['datetime'] < end_dt]

        logger.info(f"成功加载 {len(df)} 条数据记录")
        return df

    def get_cleaning_stats(self):
        """获取数据清洗统计信息"""
        return self.cleaning_stats.copy()


    def get_latest_data(self, exchange, contract, period='日线', periods=100):
        """
        获取最新的N个周期数据

        Args:
            exchange: 交易所代码
            contract: 合约代码
            period: 数据周期
            periods: 获取的周期数

        Returns:
            DataFrame: 最新的数据
        """
        df = self.load_contract_data(exchange, contract, period)
        if df is not None and len(df) > 0:
            return df.tail(periods)
        return None

    def get_contract_info(self, exchange, contract, period='日线'):
        """
        获取合约基本信息

        Args:
            exchange: 交易所代码
            contract: 合约代码
            period: 数据周期

        Returns:
            dict: 合约信息
        """
        df = self.load_contract_data(exchange, contract, period)
        if df is None or len(df) == 0:
            return None

        info = {
            'exchange': exchange,
            'contract': contract,
            'period': period,
            'start_date': df['datetime'].min().strftime('%Y-%m-%d'),
            'end_date': df['datetime'].max().strftime('%Y-%m-%d'),
            'total_records': len(df),
            'latest_price': df['close'].iloc[-1],
            'latest_volume': df['volume'].iloc[-1],
            'latest_open_interest': df['open_interest'].iloc[-1],
            'price_range': {
                'min': df['low'].min(),
                'max': df['high'].max()
            }
        }

        return info

def main():
    """测试本地数据加载器"""
    loader = LocalDataLoader()

    # 列出可用交易所
    print("可用交易所:")
    exchanges = loader.list_available_exchanges()
    for i, exchange in enumerate(exchanges, 1):
        print(f"{i}. {exchange}")

    if exchanges:
        # 测试第一个交易所
        test_exchange = exchanges[0]
        print(f"\n测试交易所: {test_exchange}")

        # 列出该交易所的合约
        contracts = loader.list_contracts_by_exchange(test_exchange)
        print(f"可用合约 (前5个):")
        for i, contract in enumerate(contracts[:5], 1):
            print(f"  {i}. {contract}")

        if contracts:
            # 测试第一个合约
            test_contract = contracts[0]
            print(f"\n测试合约: {test_contract}")

            # 获取可用周期
            periods = loader.get_available_periods(test_exchange, test_contract)
            print(f"可用周期: {periods}")

            if periods:
                # 测试加载数据
                test_period = periods[0]
                print(f"\n测试加载 {test_period} 数据:")

                # 获取合约信息
                info = loader.get_contract_info(test_exchange, test_contract, test_period)
                if info:
                    print(f"合约信息: {info}")

                # 获取最新100条数据
                df = loader.get_latest_data(test_exchange, test_contract, test_period, 100)
                if df is not None:
                    print(f"\n最新数据样本:")
                    print(df[['datetime', 'open', 'high', 'low', 'close', 'volume', 'open_interest']].tail())

if __name__ == "__main__":
    main()
