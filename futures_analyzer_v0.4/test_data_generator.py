"""
测试数据生成器
生成模拟的期货K线数据用于测试
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random

class TestDataGenerator:
    """测试数据生成器"""
    
    def __init__(self, seed=42):
        """初始化生成器"""
        np.random.seed(seed)
        random.seed(seed)
    
    def generate_kline_data(self, periods=1000, start_price=4000, volatility=0.02):
        """生成K线数据"""
        
        # 生成时间序列
        start_time = datetime.now() - timedelta(days=periods)
        timestamps = [start_time + timedelta(minutes=15*i) for i in range(periods)]
        
        # 生成价格数据（使用几何布朗运动）
        returns = np.random.normal(0, volatility, periods)
        prices = [start_price]
        
        for i in range(1, periods):
            # 添加一些趋势性
            trend = 0.0001 * np.sin(i / 100)  # 周期性趋势
            price = prices[-1] * (1 + returns[i] + trend)
            prices.append(max(price, 100))  # 确保价格不会太低
        
        # 生成OHLC数据
        data = []
        for i, (timestamp, close_price) in enumerate(zip(timestamps, prices)):
            # 生成开盘价（基于前一个收盘价）
            if i == 0:
                open_price = close_price
            else:
                open_price = prices[i-1] * (1 + np.random.normal(0, volatility/4))
            
            # 生成最高价和最低价
            high_low_range = abs(close_price - open_price) + close_price * volatility * np.random.uniform(0.5, 2.0)
            high_price = max(open_price, close_price) + high_low_range * np.random.uniform(0, 0.5)
            low_price = min(open_price, close_price) - high_low_range * np.random.uniform(0, 0.5)
            
            # 确保价格逻辑正确
            high_price = max(high_price, open_price, close_price)
            low_price = min(low_price, open_price, close_price)
            
            # 生成成交量（对数正态分布）
            base_volume = 10000
            volume = int(np.random.lognormal(np.log(base_volume), 0.5))
            
            # 生成持仓量
            if i == 0:
                open_interest = 50000
            else:
                change = np.random.normal(0, 0.01)
                open_interest = max(int(data[-1]['open_interest'] * (1 + change)), 1000)
            
            # 构造数据记录
            record = {
                'contract_id': 'IF2509',
                'trade_date': timestamp.strftime('%Y%m%d'),
                'update_time': timestamp.strftime('%H:%M:%S'),
                'update_ms': '000',
                'open': round(open_price, 2),
                'high': round(high_price, 2),
                'low': round(low_price, 2),
                'close': round(close_price, 2),
                'volume': volume,
                'open_interest': open_interest,
                'prev_open_interest': open_interest - np.random.randint(-1000, 1000),
                'daily_volume': volume,
                'daily_amount': volume * close_price,
                'datetime': timestamp
            }
            
            data.append(record)
        
        return pd.DataFrame(data)
    
    def generate_daily_data(self, periods=200):
        """生成日线数据"""
        return self.generate_kline_data(periods=periods, volatility=0.015)
    
    def generate_minute_data(self, periods=1000):
        """生成15分钟线数据"""
        return self.generate_kline_data(periods=periods, volatility=0.008)

class MockDataLoader:
    """模拟数据加载器"""
    
    def __init__(self):
        """初始化模拟加载器"""
        self.generator = TestDataGenerator()
        self.data_cache = {}
    
    def test_connection(self):
        """测试连接（总是返回True）"""
        return True
    
    def list_available_exchanges(self):
        """列出可用交易所"""
        return ['CFFEX', 'SHFE', 'DCE', 'ZCE', 'GFEX']
    
    def get_available_periods(self, exchange, contract):
        """获取可用周期"""
        return ['日线', '15分钟线']
    
    def load_contract_data(self, exchange, contract, period='日线'):
        """加载合约数据"""
        cache_key = f"{exchange}_{contract}_{period}"
        
        if cache_key not in self.data_cache:
            if period == '日线':
                data = self.generator.generate_daily_data(200)
            else:  # 15分钟线
                data = self.generator.generate_minute_data(1000)
            
            self.data_cache[cache_key] = data
        
        return self.data_cache[cache_key].copy()

def main():
    """测试数据生成"""
    print("🧪 测试数据生成器")
    print("="*50)
    
    generator = TestDataGenerator()
    
    # 生成日线数据
    daily_data = generator.generate_daily_data(200)
    print(f"📅 生成日线数据: {len(daily_data)} 条")
    print(f"   价格范围: {daily_data['close'].min():.2f} - {daily_data['close'].max():.2f}")
    print(f"   成交量范围: {daily_data['volume'].min()} - {daily_data['volume'].max()}")
    
    # 生成分钟线数据
    minute_data = generator.generate_minute_data(1000)
    print(f"⏰ 生成15分钟线数据: {len(minute_data)} 条")
    print(f"   价格范围: {minute_data['close'].min():.2f} - {minute_data['close'].max():.2f}")
    print(f"   成交量范围: {minute_data['volume'].min()} - {minute_data['volume'].max()}")
    
    # 保存样本数据
    daily_data.to_csv('test_daily_data.csv', index=False)
    minute_data.to_csv('test_minute_data.csv', index=False)
    print(f"\n💾 样本数据已保存")

if __name__ == "__main__":
    main()
