Metadata-Version: 2.4
Name: futures-analyzer-final
Version: 0.2.0
Summary: 专业的期货技术分析工具库 - 最终版本
Home-page: https://github.com/futures-analysis/futures-analyzer-final
Author: Futures Analysis Team
Author-email: Futures Analysis Team <<EMAIL>>
License: MIT
Project-URL: Homepage, https://github.com/futures-analysis/futures-analyzer-final
Project-URL: Repository, https://github.com/futures-analysis/futures-analyzer-final
Project-URL: Documentation, https://futures-analyzer-final.readthedocs.io/
Project-URL: Bug Reports, https://github.com/futures-analysis/futures-analyzer-final/issues
Keywords: futures,trading,technical-analysis,finance,quantitative
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Financial and Insurance Industry
Classifier: Topic :: Office/Business :: Financial :: Investment
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Requires-Python: >=3.7
Description-Content-Type: text/markdown
Requires-Dist: pandas>=1.3.0
Requires-Dist: numpy>=1.20.0
Requires-Dist: python-dateutil>=2.8.0
Dynamic: author
Dynamic: home-page
Dynamic: requires-python

# 期货技术分析库 v0.2 - 最终版本

专业的期货技术分析工具库，提供简化的API接口和强大的分析功能。

## ✨ 主要特性

- 🏢 **多交易所支持**: SHFE, DCE, ZCE, CFFEX, GFEX
- 🧹 **自动数据清洗**: 智能处理脏数据和异常记录
- 📊 **全面技术指标**: RSI, 移动平均线, 趋势分析, 波动性指标等
- 🔍 **智能分析引擎**: 自动选择最佳K线周期
- 📄 **详细报告生成**: 结果保存到文件，不在终端显示
- 📚 **Python库形式**: 方便集成到其他项目
- 🚀 **简化API**: 一行代码完成分析

## 🚀 快速开始

### 安装

```bash
# 克隆项目
git clone <repository-url>
cd futures_analyzer_final

# 安装依赖
pip install -r requirements.txt

# 安装库（可选）
pip install -e .
```

### 基本使用

#### 1. 命令行使用

```bash
python main.py
# 输入: CFFEX/IF2509
```

#### 2. Python库使用

```python
from futures_analyzer_final import analyze_futures

# 快速分析
result = analyze_futures("CFFEX/IF2509")

if result['success']:
    print(f"分析成功！")
    print(f"分析的周期: {result['analyzed_periods']}")
    print(f"保存的文件: {result['saved_files']}")
else:
    print(f"分析失败: {result['error']}")
```

#### 3. 使用类接口

```python
from futures_analyzer_final import FuturesAnalyzer

# 创建分析器
analyzer = FuturesAnalyzer()

# 分析合约
result = analyzer.analyze("SHFE/ag2508")

# 获取可用合约
symbols = analyzer.get_available_symbols()
print(f"可用合约: {symbols[:5]}...")  # 显示前5个
```

## 📋 支持的合约格式

合约代码格式：`交易所/合约代码`

### 支持的交易所

- **SHFE** (上海期货交易所): `SHFE/ag2508`, `SHFE/cu2506`
- **DCE** (大连商品交易所): `DCE/m2509`, `DCE/c2509`
- **ZCE** (郑州商品交易所): `ZCE/CF509`, `ZCE/SR509`
- **CFFEX** (中国金融期货交易所): `CFFEX/IF2509`, `CFFEX/IC2509`
- **GFEX** (广州期货交易所): `GFEX/SI2509`

## 📊 分析功能

### 自动周期选择

系统会自动选择最佳的K线周期进行分析：

1. **优先使用日线数据** - 适合长期趋势分析
2. **补充15分钟线数据** - 适合短期交易信号
3. **智能数据量判断** - 根据可用数据自动调整

### 技术指标

- **趋势指标**: 移动平均线(MA), 趋势强度分析
- **动量指标**: RSI
- **波动性指标**: 历史波动率, ATR
- **成交量指标**: OBV, 成交量比率分析

### 分析内容

- 📈 趋势分析 (短期/中期/长期)
- ⚡ 动量分析 (超买超卖状态)
- 📊 波动性分析 (风险评估)
- 📦 成交量分析 (资金流向)
- 🎯 综合评估和建议

## 📁 输出文件

分析结果保存在 `analysis_results` 文件夹中，文件命名格式：

```
analysis_{交易所}_{合约}_{周期}_{时间戳}.txt
```

例如：`analysis_CFFEX_IF2509_日线_20231201_143022.txt`

## 🔧 API 参考

### FuturesAnalyzer 类

```python
class FuturesAnalyzer:
    def __init__(self, data_dir="data_path", output_dir="analysis_results")
    def analyze(self, symbol)
    def get_available_symbols()
    def validate_symbol(self, symbol)
```

### 便捷函数

```python
def analyze_futures(symbol, data_dir="data_path", output_dir="analysis_results")
```

### 主要方法

#### analyze(symbol)

分析指定合约的技术指标。

**参数:**
- `symbol` (str): 合约代码，格式为 "交易所/合约代码"

**返回:**
```python
{
    'success': True/False,
    'symbol': '合约代码',
    'exchange': '交易所',
    'contract': '合约',
    'analyzed_periods': ['日线', '15分钟线'],
    'saved_files': ['文件路径1', '文件路径2'],
    'error': '错误信息'  # 仅在失败时
}
```

## 📝 使用示例

### 示例1：基本分析

```python
from futures_analyzer_final import analyze_futures

# 分析股指期货
result = analyze_futures("CFFEX/IF2509")
print(f"分析结果: {result['success']}")
```

### 示例2：批量分析

```python
from futures_analyzer_final import FuturesAnalyzer

analyzer = FuturesAnalyzer()

# 要分析的合约列表
symbols = ["CFFEX/IF2509", "SHFE/ag2508", "DCE/m2509"]

for symbol in symbols:
    result = analyzer.analyze(symbol)
    if result['success']:
        print(f"✅ {symbol} 分析成功")
    else:
        print(f"❌ {symbol} 分析失败: {result['error']}")
```

### 示例3：获取可用合约

```python
from futures_analyzer_final import FuturesAnalyzer

analyzer = FuturesAnalyzer()
symbols = analyzer.get_available_symbols()

print("可用合约:")
for symbol in symbols[:10]:  # 显示前10个
    print(f"  - {symbol}")
```

## 📈 分析报告示例

```
期货技术分析报告 v0.2
================================================================================
合约代码: CFFEX/IF2509
数据周期: 日线
分析时间: 2023-12-01 14:30:22
当前价格: 3918.00
当前成交量: 38,088
当前持仓量: 132,375
数据范围: 2023-01-01 至 2023-12-01
数据量: 1000 条记录
================================================================================

📈 趋势分析:
  整体方向: 强势上升
  短期趋势: 上升
  中期趋势: 上升
  长期趋势: 上升
  趋势强度: 0.80

⚡ 动量分析:
  RSI: 66.61 (正常)

📊 波动性分析:
  波动性等级: 较低
  历史波动率: 0.2560
  ATR波动率: 0.0099

📦 成交量分析:
  成交量状态: 正常
  OBV趋势: 上升
```

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License
