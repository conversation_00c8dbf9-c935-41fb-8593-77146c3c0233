#!/bin/bash

# OneAPI Enhanced 离线部署 - 镜像下载脚本
# 在有网络的机器上运行，下载所有需要的Docker镜像

set -e

echo "📦 OneAPI Enhanced 离线部署 - 镜像下载"
echo "====================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 定义所有需要的镜像（移除langfuse相关）
IMAGES=(
    "justsong/one-api:latest"
    "mysql:8.0"
    "redis:7-alpine"
    "nginx:alpine"
)

# 创建镜像存储目录
create_image_dir() {
    log_info "创建镜像存储目录..."
    mkdir -p docker-images
    cd docker-images
}

# 下载单个镜像
download_image() {
    local image=$1
    local filename=$(echo $image | sed 's/[\/:]/_/g')
    
    log_info "下载镜像: $image"
    
    # 拉取镜像
    if docker pull $image; then
        log_success "下载成功: $image"
        
        # 导出镜像
        log_info "导出镜像: $image -> ${filename}.tar"
        if docker save -o "${filename}.tar" $image; then
            log_success "导出成功: ${filename}.tar"
            
            # 压缩镜像文件
            log_info "压缩镜像文件..."
            gzip "${filename}.tar"
            log_success "压缩完成: ${filename}.tar.gz"
            
            return 0
        else
            log_error "导出失败: $image"
            return 1
        fi
    else
        log_error "下载失败: $image"
        return 1
    fi
}

# 下载所有镜像
download_all_images() {
    log_info "开始下载所有Docker镜像..."
    
    local success_count=0
    local total_count=${#IMAGES[@]}
    
    for image in "${IMAGES[@]}"; do
        echo ""
        if download_image "$image"; then
            ((success_count++))
        fi
    done
    
    echo ""
    log_info "下载统计: $success_count/$total_count 成功"
    
    if [ $success_count -eq $total_count ]; then
        log_success "所有镜像下载完成！"
        return 0
    else
        log_warning "部分镜像下载失败"
        return 1
    fi
}

# 创建镜像清单
create_image_manifest() {
    log_info "创建镜像清单..."
    
    cat > image-manifest.txt << EOF
OneAPI Enhanced 离线部署镜像清单
==============================
生成时间: $(date)
生成主机: $(hostname)

镜像列表:
EOF

    for image in "${IMAGES[@]}"; do
        local filename=$(echo $image | sed 's/[\/:]/_/g')
        local filesize=""
        
        if [ -f "${filename}.tar.gz" ]; then
            filesize=$(du -h "${filename}.tar.gz" | cut -f1)
            echo "✅ $image -> ${filename}.tar.gz ($filesize)" >> image-manifest.txt
        else
            echo "❌ $image -> 下载失败" >> image-manifest.txt
        fi
    done
    
    cat >> image-manifest.txt << EOF

总大小: $(du -sh . | cut -f1)
文件数量: $(ls -1 *.tar.gz 2>/dev/null | wc -l)

使用说明:
1. 将所有 .tar.gz 文件上传到服务器
2. 运行 load-images.sh 脚本导入镜像
3. 使用 deploy-offline.sh 进行离线部署
EOF

    log_success "镜像清单已创建: image-manifest.txt"
}

# 创建导入脚本
create_load_script() {
    log_info "创建镜像导入脚本..."
    
    cat > load-images.sh << 'EOF'
#!/bin/bash

# OneAPI Enhanced 离线部署 - 镜像导入脚本
# 在服务器上运行，导入所有Docker镜像

set -e

echo "📥 OneAPI Enhanced 离线部署 - 镜像导入"
echo "====================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 导入单个镜像
load_image() {
    local tar_file=$1
    
    log_info "导入镜像: $tar_file"
    
    # 解压并导入
    if gunzip -c "$tar_file" | docker load; then
        log_success "导入成功: $tar_file"
        return 0
    else
        log_error "导入失败: $tar_file"
        return 1
    fi
}

# 导入所有镜像
load_all_images() {
    log_info "开始导入所有Docker镜像..."
    
    local success_count=0
    local total_count=0
    
    for tar_file in *.tar.gz; do
        if [ -f "$tar_file" ]; then
            ((total_count++))
            if load_image "$tar_file"; then
                ((success_count++))
            fi
        fi
    done
    
    echo ""
    log_info "导入统计: $success_count/$total_count 成功"
    
    if [ $success_count -eq $total_count ]; then
        log_success "所有镜像导入完成！"
        
        # 显示导入的镜像
        echo ""
        log_info "已导入的镜像列表:"
        docker images | grep -E "(justsong|mysql|redis|nginx|langfuse|postgres|clickhouse)"
        
        return 0
    else
        log_error "部分镜像导入失败"
        return 1
    fi
}

# 主函数
main() {
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        exit 1
    fi
    
    # 检查镜像文件
    if ! ls *.tar.gz &> /dev/null; then
        log_error "未找到镜像文件 (*.tar.gz)"
        exit 1
    fi
    
    # 导入镜像
    load_all_images
}

main "$@"
EOF

    chmod +x load-images.sh
    log_success "镜像导入脚本已创建: load-images.sh"
}

# 创建离线部署脚本
create_offline_deploy_script() {
    log_info "创建离线部署脚本..."
    
    cat > deploy-offline.sh << 'EOF'
#!/bin/bash

# OneAPI Enhanced 离线部署脚本
# 使用本地镜像进行部署，无需网络连接

set -e

echo "🚀 OneAPI Enhanced 离线部署"
echo "=========================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查镜像是否存在
check_images() {
    log_info "检查本地镜像..."
    
    local required_images=(
        "justsong/one-api:latest"
        "mysql:8.0"
        "redis:7-alpine"
    )
    
    local missing_images=()
    
    for image in "${required_images[@]}"; do
        if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "^$image$"; then
            log_success "镜像存在: $image"
        else
            log_error "镜像缺失: $image"
            missing_images+=("$image")
        fi
    done
    
    if [ ${#missing_images[@]} -gt 0 ]; then
        log_error "缺少必需的镜像，请先运行 load-images.sh"
        return 1
    fi
    
    return 0
}

# 创建离线compose文件
create_offline_compose() {
    log_info "创建离线Docker Compose配置..."
    
    cat > docker-compose.offline.yml << 'EOFCOMPOSE'
version: '2.1'

services:
  one-api:
    image: justsong/one-api:latest
    container_name: one-api
    restart: always
    ports:
      - "3000:3000"
    environment:
      - SQL_DSN=oneapi:123456@tcp(mysql:3306)/one-api
      - REDIS_CONN_STRING=redis://redis:6379
      - SESSION_SECRET=random_string_here
      - TZ=Asia/Shanghai
    depends_on:
      - redis
      - mysql
    volumes:
      - ./data/oneapi:/data
    pull_policy: never

  mysql:
    image: mysql:8.0
    container_name: mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: one-api
      MYSQL_USER: oneapi
      MYSQL_PASSWORD: 123456
    volumes:
      - ./data/mysql:/var/lib/mysql
    ports:
      - "127.0.0.1:3306:3306"
    pull_policy: never

  redis:
    image: redis:7-alpine
    container_name: redis
    restart: always
    volumes:
      - ./data/redis:/data
    ports:
      - "127.0.0.1:6379:6379"
    pull_policy: never
EOFCOMPOSE

    log_success "离线配置文件已创建: docker-compose.offline.yml"
}

# 部署服务
deploy_services() {
    log_info "启动OneAPI服务..."
    
    # 创建数据目录
    mkdir -p data/{oneapi,mysql,redis}
    
    # 启动服务
    if docker-compose -f docker-compose.offline.yml up -d; then
        log_success "服务启动成功"
        
        # 等待服务启动
        log_info "等待服务启动..."
        sleep 30
        
        # 检查服务状态
        if curl -f http://localhost:3000/api/status &> /dev/null; then
            log_success "OneAPI服务运行正常"
        else
            log_error "OneAPI服务启动失败"
            return 1
        fi
        
        return 0
    else
        log_error "服务启动失败"
        return 1
    fi
}

# 显示部署信息
show_deployment_info() {
    echo ""
    log_success "🎉 离线部署完成！"
    echo ""
    echo "📋 服务地址："
    echo "  OneAPI管理界面: http://localhost:3000"
    echo ""
    echo "🔑 默认管理员信息："
    echo "  访问OneAPI管理界面进行初始化设置"
    echo ""
    echo "📊 查看服务状态："
    echo "  docker-compose -f docker-compose.offline.yml ps"
    echo ""
    echo "📝 查看日志："
    echo "  docker-compose -f docker-compose.offline.yml logs -f"
    echo ""
    echo "🛑 停止服务："
    echo "  docker-compose -f docker-compose.offline.yml down"
}

# 主函数
main() {
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose未安装"
        exit 1
    fi
    
    # 检查镜像
    if ! check_images; then
        exit 1
    fi
    
    # 创建配置文件
    create_offline_compose
    
    # 部署服务
    if deploy_services; then
        show_deployment_info
    else
        exit 1
    fi
}

main "$@"
EOF

    chmod +x deploy-offline.sh
    log_success "离线部署脚本已创建: deploy-offline.sh"
}

# 创建打包脚本
create_package_script() {
    log_info "创建打包脚本..."
    
    cat > package-for-upload.sh << 'EOF'
#!/bin/bash

# 打包所有文件用于上传到服务器

echo "📦 打包离线部署文件..."

# 创建上传包
PACKAGE_NAME="oneapi-offline-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$PACKAGE_NAME"

# 复制镜像文件
cp *.tar.gz "$PACKAGE_NAME/" 2>/dev/null || true

# 复制脚本文件
cp load-images.sh "$PACKAGE_NAME/"
cp deploy-offline.sh "$PACKAGE_NAME/"
cp image-manifest.txt "$PACKAGE_NAME/" 2>/dev/null || true

# 创建说明文件
cat > "$PACKAGE_NAME/README.txt" << 'EOFREADME'
OneAPI Enhanced 离线部署包
========================

文件说明:
- *.tar.gz: Docker镜像文件
- load-images.sh: 镜像导入脚本
- deploy-offline.sh: 离线部署脚本
- image-manifest.txt: 镜像清单

部署步骤:
1. 上传整个文件夹到服务器
2. 运行: chmod +x *.sh
3. 导入镜像: ./load-images.sh
4. 部署服务: ./deploy-offline.sh

注意事项:
- 确保服务器已安装Docker和Docker Compose
- 确保端口3000、3306、6379未被占用
- 首次启动可能需要较长时间
EOFREADME

# 创建压缩包
tar -czf "${PACKAGE_NAME}.tar.gz" "$PACKAGE_NAME"

echo "✅ 打包完成: ${PACKAGE_NAME}.tar.gz"
echo "📊 包大小: $(du -h "${PACKAGE_NAME}.tar.gz" | cut -f1)"
echo ""
echo "📤 上传到服务器后的操作:"
echo "1. tar -xzf ${PACKAGE_NAME}.tar.gz"
echo "2. cd $PACKAGE_NAME"
echo "3. chmod +x *.sh"
echo "4. ./load-images.sh"
echo "5. ./deploy-offline.sh"
EOF

    chmod +x package-for-upload.sh
    log_success "打包脚本已创建: package-for-upload.sh"
}

# 显示统计信息
show_statistics() {
    echo ""
    log_success "📊 下载统计信息:"
    echo ""
    
    # 显示文件列表
    echo "📁 镜像文件:"
    ls -lh *.tar.gz 2>/dev/null || echo "  无镜像文件"
    
    echo ""
    echo "📁 脚本文件:"
    ls -lh *.sh
    
    echo ""
    echo "📊 总大小: $(du -sh . | cut -f1)"
    
    echo ""
    echo "📋 下一步操作:"
    echo "1. 运行 ./package-for-upload.sh 创建上传包"
    echo "2. 将生成的 .tar.gz 文件上传到服务器"
    echo "3. 在服务器上解压并运行部署脚本"
}

# 主函数
main() {
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查网络连接
    if ! docker pull hello-world &> /dev/null; then
        log_error "无法连接Docker Hub，请检查网络连接"
        exit 1
    fi
    
    # 清理测试镜像
    docker rmi hello-world &> /dev/null || true
    
    echo ""
    log_info "开始下载OneAPI Enhanced所需的所有Docker镜像..."
    echo ""
    
    # 创建目录
    create_image_dir
    
    # 下载镜像
    if download_all_images; then
        # 创建相关文件
        create_image_manifest
        create_load_script
        create_offline_deploy_script
        create_package_script
        
        # 显示统计
        show_statistics
        
        echo ""
        log_success "🎉 所有镜像下载完成！"
        log_info "运行 ./package-for-upload.sh 创建上传包"
    else
        log_error "镜像下载失败"
        exit 1
    fi
}

main "$@"
