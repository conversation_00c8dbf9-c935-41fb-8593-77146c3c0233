# 🚀 OneAPI Enhanced 离线部署指南

## 📋 概述

针对无法连接外网的服务器环境，提供完整的离线部署解决方案。

### 🎯 解决的问题

- ✅ **网络隔离** - 服务器无法访问Docker Hub
- ✅ **版本兼容** - 绕过Docker Compose版本限制
- ✅ **完整功能** - 保持所有OneAPI Enhanced功能
- ✅ **简化部署** - 一键式离线部署

## 🔄 部署流程

### 阶段1: 本地准备（有网络的机器）

```bash
# 1. 下载所有Docker镜像
./download-images.sh

# 2. 打包上传文件
cd docker-images
./package-for-upload.sh

# 3. 上传到服务器
scp oneapi-offline-YYYYMMDD-HHMMSS.tar.gz user@server:/opt/
```

### 阶段2: 服务器部署（无网络环境）

```bash
# 1. 解压部署包
cd /opt
tar -xzf oneapi-offline-YYYYMMDD-HHMMSS.tar.gz
cd oneapi-offline-YYYYMMDD-HHMMSS

# 2. 设置执行权限
chmod +x *.sh

# 3. 导入Docker镜像
./load-images.sh

# 4. 离线部署服务
./deploy-offline.sh
```

## 📦 包含的镜像

| 镜像 | 用途 | 大小(约) |
|------|------|----------|
| justsong/one-api:latest | OneAPI主服务 | 50MB |
| mysql:8.0 | 数据库 | 200MB |
| redis:7-alpine | 缓存 | 15MB |
| nginx:alpine | 反向代理 | 25MB |
| langfuse/langfuse:latest | 分析服务(可选) | 100MB |
| postgres:15 | Langfuse数据库(可选) | 150MB |

**总大小**: 约540MB

## 🔧 核心特性

### 1. 版本兼容性

- **绕过Compose版本限制** - 使用2.1版本格式
- **支持老版本Docker** - 兼容Docker Compose 1.14+
- **无网络依赖** - 完全离线运行

### 2. 镜像管理

```bash
# 自动下载和压缩
download-images.sh

# 自动导入和验证
load-images.sh

# 使用本地镜像部署
deploy-offline.sh
```

### 3. 配置优化

```yaml
# docker-compose.offline.yml
version: '2.1'  # 兼容老版本

services:
  one-api:
    image: justsong/one-api:latest
    pull_policy: never  # 强制使用本地镜像
    # ... 其他配置
```

## 📊 部署验证

### 检查镜像导入

```bash
# 查看已导入的镜像
docker images | grep -E "(justsong|mysql|redis)"

# 预期输出:
# justsong/one-api    latest    abc123    2 days ago    50MB
# mysql               8.0       def456    3 days ago    200MB
# redis               7-alpine  ghi789    1 week ago    15MB
```

### 检查服务状态

```bash
# 查看容器状态
docker-compose -f docker-compose.offline.yml ps

# 检查OneAPI服务
curl http://localhost:3000/api/status

# 预期响应:
# {"success":true,"message":"","data":null}
```

## 🛠️ 故障排除

### 1. 镜像导入失败

```bash
# 检查镜像文件
ls -la *.tar.gz

# 手动导入单个镜像
gunzip -c justsong_one-api_latest.tar.gz | docker load

# 验证导入结果
docker images justsong/one-api
```

### 2. 服务启动失败

```bash
# 查看详细日志
docker-compose -f docker-compose.offline.yml logs

# 检查端口占用
netstat -tlnp | grep -E ":(3000|3306|6379)"

# 重新启动服务
docker-compose -f docker-compose.offline.yml down
docker-compose -f docker-compose.offline.yml up -d
```

### 3. 数据库连接问题

```bash
# 检查MySQL容器
docker exec -it mysql mysql -u oneapi -p123456

# 检查数据库状态
docker exec mysql mysqladmin -u root -p123456 status
```

## 📋 文件清单

### 本地生成的文件

```
docker-images/
├── justsong_one-api_latest.tar.gz     # OneAPI镜像
├── mysql_8.0.tar.gz                   # MySQL镜像
├── redis_7-alpine.tar.gz              # Redis镜像
├── nginx_alpine.tar.gz                # Nginx镜像
├── load-images.sh                     # 镜像导入脚本
├── deploy-offline.sh                  # 离线部署脚本
├── package-for-upload.sh              # 打包脚本
└── image-manifest.txt                 # 镜像清单
```

### 服务器部署文件

```
oneapi-offline-YYYYMMDD-HHMMSS/
├── *.tar.gz                          # 所有镜像文件
├── load-images.sh                    # 导入脚本
├── deploy-offline.sh                 # 部署脚本
├── docker-compose.offline.yml       # 离线配置
└── README.txt                        # 使用说明
```

## 🎯 优势对比

| 特性 | 在线部署 | 离线部署 |
|------|----------|----------|
| 网络要求 | ✅ 需要外网 | ❌ 无需网络 |
| 部署速度 | 慢(下载镜像) | 快(本地镜像) |
| 版本控制 | 依赖网络 | 完全可控 |
| 安全性 | 一般 | 高(隔离环境) |
| 维护性 | 简单 | 需要手动更新 |

## 🔄 更新流程

### 更新镜像

```bash
# 1. 在有网络的机器上
./download-images.sh

# 2. 重新打包
cd docker-images
./package-for-upload.sh

# 3. 上传新版本到服务器
scp oneapi-offline-NEW.tar.gz user@server:/opt/

# 4. 在服务器上更新
./load-images.sh  # 导入新镜像
./deploy-offline.sh  # 重新部署
```

## 📞 技术支持

### 常见问题

1. **镜像下载慢** - 使用国内Docker镜像源
2. **文件传输慢** - 使用rsync或分片传输
3. **磁盘空间不足** - 清理旧镜像和容器

### 诊断命令

```bash
# 系统信息
docker version
docker-compose --version
df -h

# 网络测试
ping 8.8.8.8
curl -I https://docker.io

# 服务状态
docker ps -a
docker images
```

## ✅ 部署检查清单

- [ ] 本地Docker环境正常
- [ ] 网络连接可用(下载阶段)
- [ ] 所有镜像下载成功
- [ ] 打包文件完整
- [ ] 服务器Docker已安装
- [ ] 镜像导入成功
- [ ] 服务启动正常
- [ ] OneAPI界面可访问
- [ ] 数据库连接正常

🎉 **完成离线部署！现在你可以在完全隔离的环境中运行OneAPI Enhanced了！**
