#!/bin/bash

# OneAPI Enhanced Complete 清理脚本
# 用于停止所有服务和清理数据

set -e

echo "🛑 OneAPI Enhanced Complete 清理脚本"
echo "================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止代理服务
stop_proxy_service() {
    log_info "停止代理服务..."
    
    cd logging-system 2>/dev/null || return
    
    # 通过PID文件停止
    if [ -f proxy.pid ]; then
        PID=$(cat proxy.pid)
        if ps -p $PID > /dev/null 2>&1; then
            log_info "停止代理服务 (PID: $PID)..."
            kill $PID
            rm proxy.pid
            log_success "代理服务已停止"
        else
            log_warning "代理服务进程不存在"
            rm proxy.pid
        fi
    else
        # 通过端口查找进程
        PID=$(lsof -ti:3001 2>/dev/null || true)
        if [ ! -z "$PID" ]; then
            log_info "通过端口查找到代理服务 (PID: $PID)，正在停止..."
            kill $PID
            log_success "代理服务已停止"
        else
            log_info "代理服务未运行"
        fi
    fi
    
    cd ..
}

# 停止OneAPI原版服务
stop_oneapi_original() {
    log_info "停止OneAPI原版服务..."
    
    cd oneapi-original 2>/dev/null || return
    
    if [ -f docker-compose.yml ]; then
        docker-compose down
        log_success "OneAPI原版服务已停止"
    else
        log_warning "OneAPI原版配置文件不存在"
    fi
    
    cd ..
}

# 停止集成服务
stop_integrated_services() {
    log_info "停止集成服务..."
    
    cd deployment 2>/dev/null || return
    
    # 停止集成版服务
    if [ -f docker-compose.integrated.yml ]; then
        docker-compose -f docker-compose.integrated.yml down
        log_success "集成服务已停止"
    fi
    
    # 停止生产版服务
    if [ -f docker-compose.production.yml ]; then
        docker-compose -f docker-compose.production.yml down
        log_success "生产服务已停止"
    fi
    
    # 停止Langfuse服务
    if [ -f docker-compose.langfuse.yml ]; then
        docker-compose -f docker-compose.langfuse.yml down
        log_success "Langfuse服务已停止"
    fi
    
    cd ..
}

# 清理Docker资源
cleanup_docker() {
    log_info "清理Docker资源..."
    
    # 停止所有相关容器
    CONTAINERS=$(docker ps -a --filter "name=oneapi" --filter "name=mysql" --filter "name=redis" --filter "name=langfuse" -q)
    if [ ! -z "$CONTAINERS" ]; then
        log_info "停止相关容器..."
        docker stop $CONTAINERS 2>/dev/null || true
        docker rm $CONTAINERS 2>/dev/null || true
        log_success "容器已清理"
    fi
    
    # 清理未使用的网络
    docker network prune -f 2>/dev/null || true
    log_success "网络已清理"
}

# 清理日志文件
cleanup_logs() {
    log_info "清理日志文件..."
    
    # 清理代理服务日志
    if [ -f logging-system/proxy.log ]; then
        rm logging-system/proxy.log
        log_success "代理服务日志已清理"
    fi
    
    # 询问是否清理对话记录
    if [ -f logging-system/conversation_logs.jsonl ]; then
        read -p "是否清理对话记录文件？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm logging-system/conversation_logs.jsonl
            log_success "对话记录已清理"
        else
            log_info "保留对话记录文件"
        fi
    fi
}

# 清理数据文件
cleanup_data() {
    log_info "检查数据文件..."
    
    # 询问是否清理OneAPI数据
    if [ -d oneapi-original/data ]; then
        echo ""
        log_warning "发现OneAPI数据目录，包含用户数据和配置"
        read -p "是否清理所有数据？这将删除用户、API Key等所有数据 (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf oneapi-original/data
            log_success "OneAPI数据已清理"
            log_warning "所有用户数据已删除，下次启动将重新初始化"
        else
            log_info "保留OneAPI数据"
        fi
    fi
}

# 显示清理选项
show_cleanup_options() {
    echo ""
    echo "📋 请选择清理模式："
    echo "1) 快速清理 - 仅停止服务"
    echo "2) 标准清理 - 停止服务 + 清理容器"
    echo "3) 完全清理 - 停止服务 + 清理容器 + 清理日志"
    echo "4) 深度清理 - 完全清理 + 清理数据（谨慎使用）"
    echo "5) 自定义清理 - 手动选择清理项目"
    echo ""
    read -p "请输入选择 (1-5): " choice
    
    case $choice in
        1) quick_cleanup ;;
        2) standard_cleanup ;;
        3) full_cleanup ;;
        4) deep_cleanup ;;
        5) custom_cleanup ;;
        *) log_error "无效选择"; exit 1 ;;
    esac
}

# 快速清理
quick_cleanup() {
    log_info "执行快速清理..."
    stop_proxy_service
    stop_oneapi_original
    stop_integrated_services
    log_success "快速清理完成"
}

# 标准清理
standard_cleanup() {
    log_info "执行标准清理..."
    stop_proxy_service
    stop_oneapi_original
    stop_integrated_services
    cleanup_docker
    log_success "标准清理完成"
}

# 完全清理
full_cleanup() {
    log_info "执行完全清理..."
    stop_proxy_service
    stop_oneapi_original
    stop_integrated_services
    cleanup_docker
    cleanup_logs
    log_success "完全清理完成"
}

# 深度清理
deep_cleanup() {
    log_info "执行深度清理..."
    log_warning "深度清理将删除所有数据，包括用户信息和API Key"
    read -p "确定要继续吗？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        stop_proxy_service
        stop_oneapi_original
        stop_integrated_services
        cleanup_docker
        cleanup_logs
        cleanup_data
        
        # 清理Docker卷
        docker volume prune -f 2>/dev/null || true
        
        log_success "深度清理完成"
        log_warning "所有数据已删除，下次部署将重新开始"
    else
        log_info "取消深度清理"
    fi
}

# 自定义清理
custom_cleanup() {
    log_info "自定义清理模式..."
    
    echo "请选择要清理的项目："
    echo "1) 代理服务"
    echo "2) OneAPI原版服务"
    echo "3) 集成服务"
    echo "4) Docker容器和网络"
    echo "5) 日志文件"
    echo "6) 数据文件"
    echo ""
    read -p "请输入选择 (可多选，用空格分隔): " -a selections
    
    for selection in "${selections[@]}"; do
        case $selection in
            1) stop_proxy_service ;;
            2) stop_oneapi_original ;;
            3) stop_integrated_services ;;
            4) cleanup_docker ;;
            5) cleanup_logs ;;
            6) cleanup_data ;;
        esac
    done
    
    log_success "自定义清理完成"
}

# 显示清理后状态
show_cleanup_status() {
    echo ""
    echo "📊 清理后状态："
    
    # 检查端口占用
    for port in 3000 3001 1919; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            log_warning "端口 $port 仍被占用"
        else
            log_success "端口 $port 已释放"
        fi
    done
    
    # 检查Docker容器
    CONTAINERS=$(docker ps --filter "name=oneapi" --filter "name=mysql" --filter "name=redis" --filter "name=langfuse" -q)
    if [ -z "$CONTAINERS" ]; then
        log_success "没有相关容器在运行"
    else
        log_warning "仍有相关容器在运行"
    fi
    
    echo ""
    echo "🔄 重新部署："
    echo "  ./deploy.sh"
}

# 主函数
main() {
    # 检查是否在正确的目录
    if [ ! -f "README.md" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 显示清理选项
    show_cleanup_options
    
    # 显示清理后状态
    show_cleanup_status
}

# 运行主函数
main "$@"
