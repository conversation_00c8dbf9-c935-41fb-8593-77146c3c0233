#!/bin/bash

# OneAPI Enhanced Complete 修复版打包脚本

set -e

echo "📦 OneAPI Enhanced Complete 修复版打包"
echo "===================================="

# 定义变量
PACKAGE_NAME="oneapi-enhanced-complete-fixed-$(date +%Y%m%d-%H%M%S)"
CURRENT_DIR=$(pwd)

echo "📁 创建打包目录: $PACKAGE_NAME"
mkdir -p "/tmp/$PACKAGE_NAME"

# 复制所有文件
echo "📋 复制项目文件..."
cp -r ./* "/tmp/$PACKAGE_NAME/"

# 清理不需要的文件
echo "🧹 清理临时文件..."
cd "/tmp/$PACKAGE_NAME"

# 清理备份文件
find . -name "*.backup" -delete 2>/dev/null || true
find . -name "*.bak" -delete 2>/dev/null || true
find . -name "*.tmp" -delete 2>/dev/null || true

# 清理日志文件
find . -name "*.log" -delete 2>/dev/null || true
find . -name "*.pid" -delete 2>/dev/null || true

# 清理系统文件
find . -name ".DS_Store" -delete 2>/dev/null || true
find . -name "Thumbs.db" -delete 2>/dev/null || true

# 清理Git文件
rm -rf .git 2>/dev/null || true
rm -f .gitignore 2>/dev/null || true

# 清理IDE文件
rm -rf .vscode 2>/dev/null || true
rm -rf .idea 2>/dev/null || true

# 清理Python缓存
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "*.pyc" -delete 2>/dev/null || true

# 清理对话记录（保护隐私）
rm -f logging-system/conversation_logs.jsonl 2>/dev/null || true

# 清理数据目录（避免包含敏感数据）
rm -rf oneapi-original/data 2>/dev/null || true

echo "📝 创建版本信息文件..."
cat > VERSION << EOFVERSION
OneAPI Enhanced Complete - Fixed Version
========================================
Package: $PACKAGE_NAME
Build Date: $(date)
Build Host: $(hostname)
Build User: $(whoami)

Fixed Issues:
- Docker Compose healthcheck configuration
- YAML special character escaping
- Deployment script compatibility

Components:
- OneAPI Original: Official OneAPI service
- Logging System: User conversation tracking
- Testing Scripts: Comprehensive test suite
- Deployment Configs: Multiple deployment modes (FIXED)
- Documentation: Complete guides and examples
- Fix Scripts: Automatic problem resolution

Features:
- Automatic user identification via database
- Complete conversation tracking and logging
- Real-time web monitoring interface
- Multi-user and multi-API-key support
- Docker-based deployment options
- Comprehensive testing framework
- Production-ready configurations
- Automatic configuration fixing

Directory Structure:
- oneapi-original/: OneAPI service files
- logging-system/: Conversation tracking system
- testing-scripts/: Test scripts collection
- deployment/: Deployment configurations (FIXED)
- docs/: Documentation files
- examples/: Configuration templates
- fix-healthcheck.sh: Configuration fix script

Usage:
1. Extract the package
2. Run ./deploy.sh for one-click deployment
3. If you encounter healthcheck errors, run ./fix-healthcheck.sh
4. Access services at http://localhost:3000 and http://localhost:3001
5. Run tests with scripts in testing-scripts/
6. Clean up with ./cleanup.sh

For detailed instructions, see README.md and docs/
EOFVERSION

echo "🔧 设置文件权限..."
chmod +x *.sh 2>/dev/null || true
chmod +x testing-scripts/*.sh 2>/dev/null || true
chmod +x deployment/*.sh 2>/dev/null || true

echo "📊 生成统计信息..."
echo ""
echo "📋 打包统计："
echo "  总文件数: $(find . -type f | wc -l)"
echo "  总目录数: $(find . -type d | wc -l)"
echo "  包大小: $(du -sh . | cut -f1)"

# 返回临时目录的上级目录
cd /tmp

# 创建压缩包
echo ""
echo "🗜️ 创建压缩包..."
tar -czf "$PACKAGE_NAME.tar.gz" "$PACKAGE_NAME"

# 移动到原目录
mv "$PACKAGE_NAME.tar.gz" "$CURRENT_DIR/"

# 清理临时目录
rm -rf "$PACKAGE_NAME"

echo ""
echo "✅ 修复版打包完成！"
echo ""
echo "📦 压缩包信息："
echo "  文件名: $PACKAGE_NAME.tar.gz"
echo "  位置: $CURRENT_DIR/$PACKAGE_NAME.tar.gz"
echo "  大小: $(du -h "$CURRENT_DIR/$PACKAGE_NAME.tar.gz" | cut -f1)"

echo ""
echo "🔧 修复说明："
echo "此版本已修复Docker Compose健康检查配置问题"
echo "如果仍遇到问题，解压后运行 ./fix-healthcheck.sh"

echo ""
echo "🚀 部署步骤："
echo "1. 将压缩包传输到目标服务器"
echo "2. 解压: tar -xzf $PACKAGE_NAME.tar.gz"
echo "3. 进入目录: cd $PACKAGE_NAME"
echo "4. 运行部署: ./deploy.sh"
echo "5. 如遇问题: ./fix-healthcheck.sh"
