# OneAPI代理服务环境变量配置
# 复制此文件为 .env 并根据实际情况修改配置

# ===== 基础配置 =====
# OneAPI服务地址
ONEAPI_BASE_URL=http://localhost:3000

# 代理服务端口
PROXY_PORT=3001

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# ===== 数据库配置 =====
# OneAPI数据库连接信息
ONEAPI_DB_HOST=localhost
ONEAPI_DB_PORT=3306
ONEAPI_DB_USER=oneapi
ONEAPI_DB_PASSWORD=123456
ONEAPI_DB_NAME=one-api

# 数据库连接池配置
DB_POOL_MIN_SIZE=1
DB_POOL_MAX_SIZE=10

# ===== 日志配置 =====
# 对话日志文件路径
LOG_FILE=conversation_logs.jsonl

# 日志轮转配置（可选）
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=5

# ===== 缓存配置 =====
# 用户信息缓存时间（秒）
USER_CACHE_TTL=3600

# 最大缓存用户数
MAX_CACHED_USERS=1000

# ===== 安全配置 =====
# 允许的来源（CORS）
ALLOWED_ORIGINS=*

# API限流配置（可选）
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# ===== 监控配置 =====
# 健康检查间隔（秒）
HEALTH_CHECK_INTERVAL=30

# 数据库连接超时（秒）
DB_TIMEOUT=10

# ===== 高级配置 =====
# 是否启用详细日志
ENABLE_VERBOSE_LOGGING=false

# 是否启用性能监控
ENABLE_PERFORMANCE_MONITORING=true

# 是否启用用户统计
ENABLE_USER_STATISTICS=true

# ===== Langfuse集成（可选） =====
LANGFUSE_BASE_URL=http://localhost:1919
LANGFUSE_PUBLIC_KEY=pk-lf-ac71521b-6a96-4871-85f8-967c7ee634fd
LANGFUSE_SECRET_KEY=******************************************
LANGFUSE_PROJECT_ID=your-project-id
