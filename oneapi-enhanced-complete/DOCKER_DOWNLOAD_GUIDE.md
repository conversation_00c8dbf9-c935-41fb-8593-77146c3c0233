# 🐳 Docker镜像下载指南

## 📋 概述

由于你的服务器无法连接外网，需要在有网络的机器上下载Docker镜像，然后传输到服务器。

## 🎯 解决方案

### 问题分析
1. **服务器网络隔离** - 无法访问Docker Hub
2. **OneAPI镜像下载失败** - 需要尝试多个镜像源
3. **Langfuse不需要** - 移除所有langfuse相关依赖
4. **Docker Compose版本兼容** - 使用2.1版本格式

### 核心镜像列表

我已经为你准备了核心版本，只需要以下4个镜像：

```bash
# 必需的核心镜像
justsong/one-api:latest     # OneAPI主服务 (~50MB)
mysql:8.0                   # 数据库 (~200MB)  
redis:7-alpine             # 缓存 (~15MB)
nginx:alpine               # 反向代理 (~25MB)

# 总大小约: 290MB (压缩后约150MB)
```

## 🚀 操作步骤

### 步骤1: 在有Docker和网络的机器上

```bash
# 1. 解压项目
tar -xzf oneapi-enhanced-complete-fixed-YYYYMMDD-HHMMSS.tar.gz
cd oneapi-enhanced-complete-fixed-YYYYMMDD-HHMMSS

# 2. 运行修复版下载脚本
./download-images-fixed.sh

# 这个脚本会：
# - 尝试多个OneAPI镜像源
# - 下载核心镜像（mysql, redis, nginx）
# - 自动压缩镜像文件
# - 创建导入和部署脚本
```

### 步骤2: 手动下载（如果脚本失败）

如果自动脚本失败，可以手动下载：

```bash
# 创建目录
mkdir -p docker-images
cd docker-images

# 下载镜像
docker pull mysql:8.0
docker pull redis:7-alpine  
docker pull nginx:alpine

# 尝试OneAPI镜像的多个源
docker pull justsong/one-api:latest || \
docker pull songquanpeng/one-api:latest || \
docker pull ghcr.io/songquanpeng/one-api:latest

# 如果下载了其他名称的镜像，重新标记
docker tag songquanpeng/one-api:latest justsong/one-api:latest

# 导出镜像
docker save -o justsong_one-api_latest.tar justsong/one-api:latest
docker save -o mysql_8.0.tar mysql:8.0
docker save -o redis_7-alpine.tar redis:7-alpine
docker save -o nginx_alpine.tar nginx:alpine

# 压缩镜像
gzip *.tar
```

### 步骤3: 创建导入脚本

```bash
# 创建导入脚本
cat > load-images.sh << 'EOF'
#!/bin/bash
echo "导入Docker镜像..."

for file in *.tar.gz; do
    echo "导入: $file"
    gunzip -c "$file" | docker load
done

echo "镜像导入完成！"
docker images | grep -E "(justsong|mysql|redis|nginx)"
EOF

chmod +x load-images.sh
```

### 步骤4: 创建部署脚本

```bash
# 创建部署脚本
cat > deploy-core.sh << 'EOF'
#!/bin/bash
echo "部署OneAPI核心版..."

# 创建compose文件
cat > docker-compose.yml << 'EOFCOMPOSE'
version: '2.1'

services:
  one-api:
    image: justsong/one-api:latest
    container_name: one-api
    restart: always
    ports:
      - "3000:3000"
    environment:
      - SQL_DSN=oneapi:123456@tcp(mysql:3306)/one-api
      - REDIS_CONN_STRING=redis://redis:6379
      - SESSION_SECRET=random_string_here
      - TZ=Asia/Shanghai
    depends_on:
      - redis
      - mysql
    volumes:
      - ./data/oneapi:/data

  mysql:
    image: mysql:8.0
    container_name: mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: one-api
      MYSQL_USER: oneapi
      MYSQL_PASSWORD: 123456
    volumes:
      - ./data/mysql:/var/lib/mysql
    ports:
      - "127.0.0.1:3306:3306"

  redis:
    image: redis:7-alpine
    container_name: redis
    restart: always
    volumes:
      - ./data/redis:/data
    ports:
      - "127.0.0.1:6379:6379"
EOFCOMPOSE

# 创建数据目录
mkdir -p data/{oneapi,mysql,redis}

# 启动服务
docker-compose up -d

echo "部署完成！访问 http://localhost:3000"
EOF

chmod +x deploy-core.sh
```

### 步骤5: 打包上传

```bash
# 打包所有文件
tar -czf oneapi-offline-core.tar.gz *.tar.gz *.sh

# 上传到服务器
scp oneapi-offline-core.tar.gz user@server:/opt/
```

## 🖥️ 在服务器上部署

### 步骤1: 解压和导入

```bash
# 在服务器上
cd /opt
tar -xzf oneapi-offline-core.tar.gz

# 导入镜像
./load-images.sh

# 验证镜像
docker images | grep -E "(justsong|mysql|redis|nginx)"
```

### 步骤2: 部署服务

```bash
# 部署服务
./deploy-core.sh

# 检查状态
docker-compose ps

# 检查OneAPI服务
curl http://localhost:3000/api/status
```

## 🔧 故障排除

### OneAPI镜像下载失败

如果所有OneAPI镜像源都失败，可以：

1. **使用国内镜像源**：
```bash
# 配置Docker镜像加速
sudo mkdir -p /etc/docker
sudo tee /etc/docker/daemon.json <<-'EOF'
{
  "registry-mirrors": [
    "https://mirror.ccs.tencentyun.com",
    "https://docker.mirrors.ustc.edu.cn"
  ]
}
EOF
sudo systemctl restart docker
```

2. **手动构建镜像**：
```bash
# 如果有OneAPI源码
git clone https://github.com/songquanpeng/one-api.git
cd one-api
docker build -t justsong/one-api:latest .
```

### 服务启动失败

```bash
# 查看日志
docker-compose logs

# 检查端口占用
netstat -tlnp | grep -E ":(3000|3306|6379)"

# 重启服务
docker-compose down
docker-compose up -d
```

## ✅ 验证清单

部署完成后，确认：

- [ ] 所有镜像导入成功
- [ ] OneAPI服务启动正常
- [ ] 数据库连接正常
- [ ] Redis缓存正常
- [ ] Web界面可访问
- [ ] 无langfuse相关错误

## 📊 核心版本特点

### ✅ 包含功能
- OneAPI完整功能
- 用户管理和API Key管理
- 多模型支持
- 请求转发和负载均衡
- 基础监控和日志

### ❌ 移除功能
- Langfuse分析和追踪
- 高级数据分析
- 对话质量评估
- 复杂的可视化图表

### 🎯 适用场景
- 基础的API代理需求
- 简单的用户管理
- 网络隔离环境
- 快速部署需求

这个核心版本满足OneAPI的主要功能需求，同时避免了复杂的依赖和网络问题。
