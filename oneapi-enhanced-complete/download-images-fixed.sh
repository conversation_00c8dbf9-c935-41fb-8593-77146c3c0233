#!/bin/bash

# OneAPI Enhanced 离线部署 - 修复版镜像下载脚本
# 移除langfuse依赖，处理oneapi镜像下载问题

set -e

echo "📦 OneAPI Enhanced 离线部署 - 修复版镜像下载"
echo "==========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 定义核心镜像（移除langfuse相关）
CORE_IMAGES=(
    "mysql:8.0"
    "redis:7-alpine"
    "nginx:alpine"
)

# OneAPI镜像的多个备选方案
ONEAPI_IMAGES=(
    "justsong/one-api:latest"
    "songquanpeng/one-api:latest"
    "ghcr.io/songquanpeng/one-api:latest"
)

# 创建镜像存储目录
create_image_dir() {
    log_info "创建镜像存储目录..."
    mkdir -p docker-images
    cd docker-images
}

# 下载单个镜像
download_image() {
    local image=$1
    local filename=$(echo $image | sed 's/[\/:]/_/g' | sed 's/ghcr.io_//g')
    
    log_info "下载镜像: $image"
    
    # 拉取镜像
    if docker pull $image; then
        log_success "下载成功: $image"
        
        # 导出镜像
        log_info "导出镜像: $image -> ${filename}.tar"
        if docker save -o "${filename}.tar" $image; then
            log_success "导出成功: ${filename}.tar"
            
            # 压缩镜像文件
            log_info "压缩镜像文件..."
            gzip "${filename}.tar"
            log_success "压缩完成: ${filename}.tar.gz"
            
            return 0
        else
            log_error "导出失败: $image"
            return 1
        fi
    else
        log_error "下载失败: $image"
        return 1
    fi
}

# 尝试下载OneAPI镜像
download_oneapi_image() {
    log_info "尝试下载OneAPI镜像..."
    
    for image in "${ONEAPI_IMAGES[@]}"; do
        log_info "尝试镜像源: $image"
        
        if download_image "$image"; then
            # 如果不是标准名称，重新标记为标准名称
            if [ "$image" != "justsong/one-api:latest" ]; then
                log_info "重新标记镜像为标准名称..."
                docker tag "$image" "justsong/one-api:latest"
                
                # 重新导出标准名称的镜像
                log_info "导出标准名称镜像..."
                docker save -o "justsong_one-api_latest.tar" "justsong/one-api:latest"
                gzip "justsong_one-api_latest.tar"
                log_success "标准名称镜像导出完成"
            fi
            
            log_success "OneAPI镜像下载成功: $image"
            return 0
        else
            log_warning "镜像源失败: $image"
        fi
    done
    
    log_error "所有OneAPI镜像源都失败"
    return 1
}

# 下载核心镜像
download_core_images() {
    log_info "下载核心镜像..."
    
    local success_count=0
    local total_count=${#CORE_IMAGES[@]}
    
    for image in "${CORE_IMAGES[@]}"; do
        echo ""
        if download_image "$image"; then
            ((success_count++))
        fi
    done
    
    echo ""
    log_info "核心镜像下载统计: $success_count/$total_count 成功"
    
    return $success_count
}

# 创建镜像清单
create_image_manifest() {
    log_info "创建镜像清单..."
    
    cat > image-manifest.txt << EOF
OneAPI Enhanced 离线部署镜像清单 (无Langfuse版本)
===============================================
生成时间: $(date)
生成主机: $(hostname)

核心镜像列表:
EOF

    # 检查OneAPI镜像
    if [ -f "justsong_one-api_latest.tar.gz" ]; then
        local filesize=$(du -h "justsong_one-api_latest.tar.gz" | cut -f1)
        echo "✅ justsong/one-api:latest -> justsong_one-api_latest.tar.gz ($filesize)" >> image-manifest.txt
    else
        echo "❌ justsong/one-api:latest -> 下载失败" >> image-manifest.txt
    fi
    
    # 检查核心镜像
    for image in "${CORE_IMAGES[@]}"; do
        local filename=$(echo $image | sed 's/[\/:]/_/g')
        local filesize=""
        
        if [ -f "${filename}.tar.gz" ]; then
            filesize=$(du -h "${filename}.tar.gz" | cut -f1)
            echo "✅ $image -> ${filename}.tar.gz ($filesize)" >> image-manifest.txt
        else
            echo "❌ $image -> 下载失败" >> image-manifest.txt
        fi
    done
    
    cat >> image-manifest.txt << EOF

总大小: $(du -sh . | cut -f1)
文件数量: $(ls -1 *.tar.gz 2>/dev/null | wc -l)

注意事项:
- 此版本不包含Langfuse相关功能
- 仅包含OneAPI核心功能所需的镜像
- 如需Langfuse功能，请单独下载相关镜像

使用说明:
1. 将所有 .tar.gz 文件上传到服务器
2. 运行 load-images-core.sh 脚本导入镜像
3. 使用 deploy-offline-core.sh 进行离线部署
EOF

    log_success "镜像清单已创建: image-manifest.txt"
}

# 创建核心版导入脚本
create_core_load_script() {
    log_info "创建核心版镜像导入脚本..."
    
    cat > load-images-core.sh << 'EOF'
#!/bin/bash

# OneAPI Enhanced 离线部署 - 核心版镜像导入脚本
# 仅导入核心功能所需的镜像

set -e

echo "📥 OneAPI Enhanced 离线部署 - 核心版镜像导入"
echo "==========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 导入单个镜像
load_image() {
    local tar_file=$1
    
    log_info "导入镜像: $tar_file"
    
    # 解压并导入
    if gunzip -c "$tar_file" | docker load; then
        log_success "导入成功: $tar_file"
        return 0
    else
        log_error "导入失败: $tar_file"
        return 1
    fi
}

# 导入所有镜像
load_all_images() {
    log_info "开始导入所有Docker镜像..."
    
    local success_count=0
    local total_count=0
    
    for tar_file in *.tar.gz; do
        if [ -f "$tar_file" ]; then
            ((total_count++))
            if load_image "$tar_file"; then
                ((success_count++))
            fi
        fi
    done
    
    echo ""
    log_info "导入统计: $success_count/$total_count 成功"
    
    if [ $success_count -eq $total_count ]; then
        log_success "所有镜像导入完成！"
        
        # 显示导入的镜像
        echo ""
        log_info "已导入的镜像列表:"
        docker images | grep -E "(justsong|one-api|mysql|redis|nginx)" || echo "未找到相关镜像"
        
        return 0
    else
        log_error "部分镜像导入失败"
        return 1
    fi
}

# 主函数
main() {
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        exit 1
    fi
    
    # 检查镜像文件
    if ! ls *.tar.gz &> /dev/null; then
        log_error "未找到镜像文件 (*.tar.gz)"
        exit 1
    fi
    
    # 导入镜像
    load_all_images
}

main "$@"
EOF

    chmod +x load-images-core.sh
    log_success "核心版镜像导入脚本已创建: load-images-core.sh"
}

# 创建核心版离线部署脚本
create_core_deploy_script() {
    log_info "创建核心版离线部署脚本..."
    
    cat > deploy-offline-core.sh << 'EOF'
#!/bin/bash

# OneAPI Enhanced 核心版离线部署脚本
# 使用本地镜像进行部署，无Langfuse功能

set -e

echo "🚀 OneAPI Enhanced 核心版离线部署"
echo "==============================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查核心镜像是否存在
check_core_images() {
    log_info "检查本地核心镜像..."
    
    local required_images=(
        "justsong/one-api:latest"
        "mysql:8.0"
        "redis:7-alpine"
    )
    
    local missing_images=()
    
    for image in "${required_images[@]}"; do
        if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "^$image$"; then
            log_success "镜像存在: $image"
        else
            log_error "镜像缺失: $image"
            missing_images+=("$image")
        fi
    done
    
    if [ ${#missing_images[@]} -gt 0 ]; then
        log_error "缺少必需的镜像，请先运行 load-images-core.sh"
        return 1
    fi
    
    return 0
}

# 创建核心版compose文件
create_core_compose() {
    log_info "创建核心版Docker Compose配置..."
    
    cat > docker-compose.core.yml << 'EOFCOMPOSE'
version: '2.1'

services:
  one-api:
    image: justsong/one-api:latest
    container_name: one-api
    restart: always
    ports:
      - "3000:3000"
    environment:
      - SQL_DSN=oneapi:123456@tcp(mysql:3306)/one-api
      - REDIS_CONN_STRING=redis://redis:6379
      - SESSION_SECRET=random_string_here
      - TZ=Asia/Shanghai
    depends_on:
      - redis
      - mysql
    volumes:
      - ./data/oneapi:/data
    pull_policy: never

  mysql:
    image: mysql:8.0
    container_name: mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: one-api
      MYSQL_USER: oneapi
      MYSQL_PASSWORD: 123456
    volumes:
      - ./data/mysql:/var/lib/mysql
    ports:
      - "127.0.0.1:3306:3306"
    pull_policy: never

  redis:
    image: redis:7-alpine
    container_name: redis
    restart: always
    volumes:
      - ./data/redis:/data
    ports:
      - "127.0.0.1:6379:6379"
    pull_policy: never
EOFCOMPOSE

    log_success "核心版配置文件已创建: docker-compose.core.yml"
}

# 部署核心服务
deploy_core_services() {
    log_info "启动OneAPI核心服务..."
    
    # 创建数据目录
    mkdir -p data/{oneapi,mysql,redis}
    
    # 启动服务
    if docker-compose -f docker-compose.core.yml up -d; then
        log_success "服务启动成功"
        
        # 等待服务启动
        log_info "等待服务启动..."
        sleep 30
        
        # 检查服务状态
        if curl -f http://localhost:3000/api/status &> /dev/null; then
            log_success "OneAPI服务运行正常"
        else
            log_error "OneAPI服务启动失败"
            return 1
        fi
        
        return 0
    else
        log_error "服务启动失败"
        return 1
    fi
}

# 显示部署信息
show_deployment_info() {
    echo ""
    log_success "🎉 OneAPI Enhanced 核心版离线部署完成！"
    echo ""
    echo "📋 服务地址："
    echo "  OneAPI管理界面: http://localhost:3000"
    echo ""
    echo "🔑 默认管理员信息："
    echo "  访问OneAPI管理界面进行初始化设置"
    echo ""
    echo "📊 查看服务状态："
    echo "  docker-compose -f docker-compose.core.yml ps"
    echo ""
    echo "📝 查看日志："
    echo "  docker-compose -f docker-compose.core.yml logs -f"
    echo ""
    echo "🛑 停止服务："
    echo "  docker-compose -f docker-compose.core.yml down"
    echo ""
    echo "⚠️ 注意事项："
    echo "  - 此版本不包含Langfuse分析功能"
    echo "  - 仅包含OneAPI核心功能"
    echo "  - 如需高级分析功能，请使用完整版"
}

# 主函数
main() {
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose未安装"
        exit 1
    fi
    
    # 检查镜像
    if ! check_core_images; then
        exit 1
    fi
    
    # 创建配置文件
    create_core_compose
    
    # 部署服务
    if deploy_core_services; then
        show_deployment_info
    else
        exit 1
    fi
}

main "$@"
EOF

    chmod +x deploy-offline-core.sh
    log_success "核心版离线部署脚本已创建: deploy-offline-core.sh"
}

# 显示统计信息
show_statistics() {
    echo ""
    log_success "📊 下载统计信息:"
    echo ""
    
    # 显示文件列表
    echo "📁 镜像文件:"
    ls -lh *.tar.gz 2>/dev/null || echo "  无镜像文件"
    
    echo ""
    echo "📁 脚本文件:"
    ls -lh *.sh
    
    echo ""
    echo "📊 总大小: $(du -sh . | cut -f1)"
    
    echo ""
    echo "📋 下一步操作:"
    echo "1. 将所有文件上传到服务器"
    echo "2. 在服务器上运行 load-images-core.sh"
    echo "3. 运行 deploy-offline-core.sh 进行部署"
}

# 主函数
main() {
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查网络连接
    if ! docker pull hello-world &> /dev/null; then
        log_error "无法连接Docker Hub，请检查网络连接"
        exit 1
    fi
    
    # 清理测试镜像
    docker rmi hello-world &> /dev/null || true
    
    echo ""
    log_info "开始下载OneAPI Enhanced核心版所需的Docker镜像..."
    log_warning "注意：此版本不包含Langfuse功能"
    echo ""
    
    # 创建目录
    create_image_dir
    
    # 下载OneAPI镜像
    oneapi_success=false
    if download_oneapi_image; then
        oneapi_success=true
    fi
    
    # 下载核心镜像
    core_success_count=$(download_core_images)
    
    # 创建相关文件
    create_image_manifest
    create_core_load_script
    create_core_deploy_script
    
    # 显示统计
    show_statistics
    
    echo ""
    if [ "$oneapi_success" = true ] && [ $core_success_count -eq ${#CORE_IMAGES[@]} ]; then
        log_success "🎉 所有核心镜像下载完成！"
        log_info "现在可以将文件上传到服务器进行离线部署"
    else
        log_warning "部分镜像下载失败，请检查网络连接或镜像源"
        if [ "$oneapi_success" = false ]; then
            log_error "OneAPI镜像下载失败，这是必需的镜像"
        fi
    fi
}

main "$@"
