#!/bin/bash

# OneAPI Enhanced Complete 一键部署脚本
# 支持多种部署模式和环境检查

set -e

echo "🚀 OneAPI Enhanced Complete 一键部署"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统环境
check_environment() {
    log_info "检查系统环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    log_success "Docker已安装: $(docker --version)"
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    log_success "Docker Compose已安装"
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_warning "Python3未安装，某些功能可能无法使用"
    else
        log_success "Python3已安装: $(python3 --version)"
    fi
    
    # 检查端口占用
    log_info "检查端口占用..."
    for port in 3000 3001 3306; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            log_warning "端口 $port 已被占用"
        else
            log_success "端口 $port 可用"
        fi
    done
}

# 显示部署选项
show_deployment_options() {
    echo ""
    echo "📋 请选择部署模式："
    echo "1) 基础模式 - OneAPI + 日志记录系统"
    echo "2) 集成模式 - 包含Langfuse的完整系统"
    echo "3) 生产模式 - 生产环境优化配置"
    echo "4) 测试模式 - 仅启动测试环境"
    echo "5) 自定义模式 - 手动选择组件"
    echo ""
    read -p "请输入选择 (1-5): " choice
    
    case $choice in
        1) deploy_basic ;;
        2) deploy_integrated ;;
        3) deploy_production ;;
        4) deploy_testing ;;
        5) deploy_custom ;;
        *) log_error "无效选择"; exit 1 ;;
    esac
}

# 基础模式部署
deploy_basic() {
    log_info "开始基础模式部署..."
    
    # 启动OneAPI
    log_info "启动OneAPI服务..."
    cd oneapi-original
    docker-compose up -d
    cd ..
    
    # 等待OneAPI启动
    log_info "等待OneAPI服务启动..."
    sleep 15
    
    # 检查OneAPI状态
    if curl -f http://localhost:3000/api/status &> /dev/null; then
        log_success "OneAPI服务启动成功"
    else
        log_error "OneAPI服务启动失败"
        return 1
    fi
    
    # 启动日志记录系统
    log_info "启动日志记录系统..."
    cd logging-system
    
    # 安装Python依赖
    if command -v python3 &> /dev/null; then
        pip3 install -r requirements.txt
        
        # 启动代理服务
        nohup python3 proxy-server.py > proxy.log 2>&1 &
        echo $! > proxy.pid
        
        sleep 5
        
        # 检查代理服务状态
        if curl -f http://localhost:3001/health &> /dev/null; then
            log_success "代理服务启动成功"
        else
            log_error "代理服务启动失败"
            return 1
        fi
    else
        log_warning "Python3未安装，跳过代理服务启动"
    fi
    
    cd ..
    
    show_service_info
}

# 集成模式部署
deploy_integrated() {
    log_info "开始集成模式部署..."
    
    cd deployment
    if [ -f "start-integrated.sh" ]; then
        chmod +x start-integrated.sh
        ./start-integrated.sh
    else
        log_error "集成部署脚本不存在"
        return 1
    fi
    cd ..
    
    show_service_info_integrated
}

# 生产模式部署
deploy_production() {
    log_info "开始生产模式部署..."
    
    cd deployment
    if [ -f "docker-compose.production.yml" ]; then
        docker-compose -f docker-compose.production.yml up -d
    else
        log_error "生产环境配置文件不存在"
        return 1
    fi
    cd ..
    
    show_service_info
}

# 测试模式部署
deploy_testing() {
    log_info "开始测试模式部署..."
    
    # 先启动基础服务
    deploy_basic
    
    # 运行测试
    log_info "运行系统测试..."
    cd testing-scripts
    
    if [ -f "test-multi-users.sh" ]; then
        chmod +x *.sh
        ./test-multi-users.sh
    else
        log_warning "测试脚本不存在"
    fi
    
    cd ..
}

# 自定义模式部署
deploy_custom() {
    log_info "自定义模式部署..."
    
    echo "请选择要启动的组件："
    echo "1) OneAPI原版服务"
    echo "2) 日志记录系统"
    echo "3) Langfuse集成"
    echo "4) 测试脚本"
    echo ""
    read -p "请输入选择 (可多选，用空格分隔): " -a selections
    
    for selection in "${selections[@]}"; do
        case $selection in
            1) 
                log_info "启动OneAPI原版服务..."
                cd oneapi-original && docker-compose up -d && cd ..
                ;;
            2)
                log_info "启动日志记录系统..."
                cd logging-system
                pip3 install -r requirements.txt
                nohup python3 proxy-server.py > proxy.log 2>&1 &
                echo $! > proxy.pid
                cd ..
                ;;
            3)
                log_info "启动Langfuse集成..."
                cd deployment
                docker-compose -f docker-compose.langfuse.yml up -d
                cd ..
                ;;
            4)
                log_info "运行测试脚本..."
                cd testing-scripts
                chmod +x *.sh
                ./test-multi-users.sh
                cd ..
                ;;
        esac
    done
    
    show_service_info
}

# 显示服务信息
show_service_info() {
    echo ""
    log_success "部署完成！"
    echo ""
    echo "📋 服务地址："
    echo "  OneAPI管理界面: http://localhost:3000"
    echo "  代理服务监控:   http://localhost:3001"
    echo "  代理API地址:    http://localhost:3001/v1/chat/completions"
    echo ""
    echo "🔑 默认管理员信息："
    echo "  Root Token: sk-34a6c378e51fb8f6626f799361a7be81cade3be3"
    echo ""
    echo "📊 查看日志："
    echo "  OneAPI日志:     docker-compose logs -f"
    echo "  代理服务日志:   tail -f logging-system/proxy.log"
    echo "  对话记录:       tail -f logging-system/conversation_logs.jsonl"
    echo ""
    echo "🧪 运行测试："
    echo "  cd testing-scripts && ./test-multi-users.sh"
    echo ""
    echo "🛑 停止服务："
    echo "  ./cleanup.sh"
}

# 显示集成模式服务信息
show_service_info_integrated() {
    echo ""
    log_success "集成模式部署完成！"
    echo ""
    echo "📋 服务地址："
    echo "  OneAPI管理界面: http://localhost:3000"
    echo "  代理服务监控:   http://localhost:3001"
    echo "  Langfuse界面:   http://localhost:1919"
    echo ""
    echo "📊 查看日志："
    echo "  docker-compose logs -f"
    echo ""
    echo "🛑 停止服务："
    echo "  cd deployment && docker-compose down"
}

# 主函数
main() {
    # 检查是否在正确的目录
    if [ ! -f "README.md" ] || [ ! -d "oneapi-original" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 检查环境
    check_environment
    
    # 显示部署选项
    show_deployment_options
}

# 运行主函数
main "$@"
