#!/bin/bash

echo "🧪 测试新API Key的用户信息获取"
echo "================================"

NEW_API_KEY="sk-sPVKUn4GVZaQG2go1e74A84520E7461e86201859C51e89Ae"
PROXY_URL="http://localhost:3001"
ONEAPI_URL="http://localhost:3000"

echo "新API Key: $NEW_API_KEY"
echo ""

echo "=== 步骤1: 直接从OneAPI获取用户信息 ==="
echo "🔍 测试API Key在OneAPI中的用户信息..."

USER_INFO=$(curl -s -H "Authorization: Bearer $NEW_API_KEY" "$ONEAPI_URL/api/user/self")
echo "OneAPI用户信息响应:"
echo "$USER_INFO" | jq '.'

echo ""
echo "=== 步骤2: 通过代理服务获取用户信息 ==="
echo "🔍 测试代理服务获取用户信息..."

PROXY_USER_INFO=$(curl -s -H "Authorization: Bearer $NEW_API_KEY" "$PROXY_URL/api/user/self")
echo "代理服务用户信息响应:"
echo "$PROXY_USER_INFO" | jq '.'

echo ""
echo "=== 步骤3: 发送聊天请求测试 ==="
echo "💬 使用新API Key发送聊天请求..."

# 创建测试请求
cat > new_test_request.json << 'EOF'
{
  "model": "qwen-plus",
  "messages": [
    {
      "role": "user", 
      "content": "你好，我是新用户，请简单介绍一下你自己。"
    }
  ],
  "max_tokens": 100
}
EOF

echo "📝 请求内容:"
cat new_test_request.json | jq '.'

echo ""
echo "🚀 发送请求..."

CHAT_RESPONSE=$(curl -s -X POST "$PROXY_URL/v1/chat/completions" \
  -H "Authorization: Bearer $NEW_API_KEY" \
  -H "Content-Type: application/json" \
  -d @new_test_request.json)

echo "聊天响应:"
echo "$CHAT_RESPONSE" | jq '.'

echo ""
echo "=== 步骤4: 查看对话记录 ==="
echo "📝 查看最新的对话记录..."

sleep 2  # 等待日志写入

LOGS_RESPONSE=$(curl -s "$PROXY_URL/logs?limit=2")
echo "最新对话记录:"
echo "$LOGS_RESPONSE" | jq '.logs[0] | {timestamp, user_info, api_key_hash: .metadata.api_key_hash}'

echo ""
echo "=== 步骤5: 检查本地日志文件 ==="
if [ -f "conversation_logs.jsonl" ]; then
    echo "📄 本地日志文件最新记录:"
    tail -1 conversation_logs.jsonl | jq '{timestamp, user_info, api_key_hash: .metadata.api_key_hash}'
else
    echo "📄 本地日志文件不存在"
fi

# 清理临时文件
rm -f new_test_request.json

echo ""
echo "🎯 测试总结:"
echo "1. 检查OneAPI是否返回了用户信息"
echo "2. 检查代理服务是否成功获取用户信息"
echo "3. 检查对话记录中是否包含用户信息"
echo "4. 检查API Key是否被正确记录"
