#!/bin/bash

echo "🎯 测试用户映射功能"
echo "=================="

NEW_API_KEY="sk-sPVKUn4GVZaQG2go1e74A84520E7461e86201859C51e89Ae"
PROXY_URL="http://localhost:3001"

echo "测试API Key: $NEW_API_KEY"
echo "期望用户名: testuser"
echo ""

# 创建测试请求
cat > mapping_test_request.json << 'EOF'
{
  "model": "qwen-plus",
  "messages": [
    {
      "role": "user", 
      "content": "你好，我是testuser，这是测试用户映射功能的消息。"
    }
  ],
  "max_tokens": 30
}
EOF

echo "📝 发送聊天请求..."
CHAT_RESPONSE=$(curl -s -X POST "$PROXY_URL/v1/chat/completions" \
  -H "Authorization: Bearer $NEW_API_KEY" \
  -H "Content-Type: application/json" \
  -d @mapping_test_request.json)

echo "✅ 聊天响应:"
echo "$CHAT_RESPONSE" | jq '.choices[0].message.content'

echo ""
echo "📊 等待日志写入..."
sleep 2

echo ""
echo "📝 查看最新对话记录 (应该显示testuser):"
LOGS_RESPONSE=$(curl -s "$PROXY_URL/logs?limit=1")
echo "$LOGS_RESPONSE" | jq '.logs[0] | {
  timestamp, 
  user_info: .user_info,
  api_key: .metadata.api_key,
  model: .request.model,
  user_message: .request.messages[0].content
}'

echo ""
echo "📄 本地日志文件最新记录:"
if [ -f "conversation_logs.jsonl" ]; then
    tail -1 conversation_logs.jsonl | jq '{
      timestamp, 
      user_info: .user_info,
      api_key: .metadata.api_key
    }'
else
    echo "本地日志文件不存在"
fi

# 清理临时文件
rm -f mapping_test_request.json

echo ""
echo "🎉 测试完成！"
echo ""
echo "📋 验证结果:"
echo "✅ 应该显示用户名: testuser"
echo "✅ 应该显示用户ID: 2"
echo "✅ 应该显示邮箱: <EMAIL>"
echo "✅ 应该显示完整API Key"
echo ""
echo "🌐 查看Web界面: $PROXY_URL"
