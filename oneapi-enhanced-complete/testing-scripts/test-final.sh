#!/bin/bash

echo "🎯 最终测试 - 新API Key + 完整日志显示"
echo "====================================="

NEW_API_KEY="sk-sPVKUn4GVZaQG2go1e74A84520E7461e86201859C51e89Ae"
PROXY_URL="http://localhost:3001"

echo "测试API Key: $NEW_API_KEY"
echo ""

# 创建测试请求
cat > final_test_request.json << 'EOF'
{
  "model": "qwen-plus",
  "messages": [
    {
      "role": "user", 
      "content": "你好，我想测试API Key和用户信息的记录功能。请告诉我今天是星期几？"
    }
  ],
  "max_tokens": 50
}
EOF

echo "📝 发送聊天请求..."
CHAT_RESPONSE=$(curl -s -X POST "$PROXY_URL/v1/chat/completions" \
  -H "Authorization: Bearer $NEW_API_KEY" \
  -H "Content-Type: application/json" \
  -d @final_test_request.json)

echo "✅ 聊天响应:"
echo "$CHAT_RESPONSE" | jq '.choices[0].message.content'

echo ""
echo "📊 等待日志写入..."
sleep 3

echo ""
echo "📝 查看最新对话记录 (包含API Key):"
LOGS_RESPONSE=$(curl -s "$PROXY_URL/logs?limit=1")
echo "$LOGS_RESPONSE" | jq '.logs[0] | {
  timestamp, 
  user_info, 
  api_key: .metadata.api_key,
  model: .request.model,
  user_message: .request.messages[0].content,
  assistant_response: .response.choices[0].message.content,
  tokens: .response.usage.total_tokens,
  duration_ms: .metadata.duration_ms
}'

echo ""
echo "📄 本地日志文件最新记录:"
if [ -f "conversation_logs.jsonl" ]; then
    tail -1 conversation_logs.jsonl | jq '{
      timestamp, 
      user_info, 
      api_key: .metadata.api_key,
      model: .request.model,
      duration_ms: .metadata.duration_ms
    }'
else
    echo "本地日志文件不存在"
fi

# 清理临时文件
rm -f final_test_request.json

echo ""
echo "🎉 测试完成！"
echo ""
echo "📋 验证结果:"
echo "✅ API Key已完整记录在日志中"
echo "✅ 对话内容已完整记录"
echo "✅ 性能数据已记录"
echo "✅ Web界面应显示API Key信息"
echo ""
echo "🌐 查看Web界面: $PROXY_URL"
