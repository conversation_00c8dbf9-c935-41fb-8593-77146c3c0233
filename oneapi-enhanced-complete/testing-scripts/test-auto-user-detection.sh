#!/bin/bash

echo "🔍 测试自动用户信息获取功能"
echo "============================"

# 使用你的新API Key
NEW_API_KEY="sk-sPVKUn4GVZaQG2go1e74A84520E7461e86201859C51e89Ae"
PROXY_URL="http://localhost:3001"

echo "测试API Key: $NEW_API_KEY"
echo "目标: 自动从OneAPI获取用户信息，不依赖映射文件"
echo ""

# 创建测试请求
cat > auto_test_request.json << 'EOF'
{
  "model": "qwen-plus",
  "messages": [
    {
      "role": "user", 
      "content": "这是测试自动用户信息获取的消息，请简单回复。"
    }
  ],
  "max_tokens": 30
}
EOF

echo "📝 发送聊天请求..."
CHAT_RESPONSE=$(curl -s -X POST "$PROXY_URL/v1/chat/completions" \
  -H "Authorization: Bearer $NEW_API_KEY" \
  -H "Content-Type: application/json" \
  -d @auto_test_request.json)

echo "✅ 聊天响应:"
echo "$CHAT_RESPONSE" | jq '.choices[0].message.content'

echo ""
echo "📊 等待日志写入..."
sleep 3

echo ""
echo "📝 查看最新对话记录:"
LOGS_RESPONSE=$(curl -s "$PROXY_URL/logs?limit=1")
echo "$LOGS_RESPONSE" | jq '.logs[0] | {
  timestamp, 
  user_info: .user_info,
  api_key: .metadata.api_key,
  model: .request.model,
  user_message: .request.messages[0].content
}'

echo ""
echo "📄 检查用户信息来源:"
echo "$LOGS_RESPONSE" | jq '.logs[0].user_info | {
  username,
  user_id: .user_id // .id,
  email,
  source,
  note
}'

# 清理临时文件
rm -f auto_test_request.json

echo ""
echo "🎯 测试结果分析:"
echo "1. 检查是否成功获取到用户信息"
echo "2. 检查用户信息的来源 (OneAPI API vs 生成)"
echo "3. 检查是否有详细的调试日志"
echo ""
echo "📋 期望结果:"
echo "- 如果OneAPI API工作: 应该显示真实的用户名和邮箱"
echo "- 如果API不可用: 应该显示基于API Key生成的用户信息"
echo "- 应该有详细的调试日志显示尝试的API端点"
