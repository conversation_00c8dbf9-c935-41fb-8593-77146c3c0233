#!/bin/bash

echo "🧪 测试聊天完成请求"
echo "==================="

API_KEY="sk-ZncNB9PXFIf5JiDO68Bf0dEc2aA744E49551D4A491Cf9897"
PROXY_URL="http://localhost:3001"

echo "使用API Key: $API_KEY"
echo "代理地址: $PROXY_URL"
echo ""

# 创建JSON文件避免命令行转义问题
cat > test_request.json << 'EOF'
{
  "model": "qwen-plus",
  "messages": [
    {
      "role": "user", 
      "content": "你好，请介绍一下地球"
    }
  ]
}
EOF

echo "📝 请求内容:"
cat test_request.json | jq '.'

echo ""
echo "🚀 发送请求..."

# 使用文件方式发送请求
curl -X POST "$PROXY_URL/v1/chat/completions" \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d @test_request.json \
  --verbose

echo ""
echo "✅ 请求完成"

# 清理临时文件
rm -f test_request.json
