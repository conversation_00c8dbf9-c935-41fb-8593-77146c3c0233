#!/usr/bin/env python3
"""
OneAPI数据库用户信息解析器
直接连接OneAPI数据库获取准确的用户信息
"""

import os
import logging
from typing import Optional, Dict
import asyncio
import aiomysql
from datetime import datetime

logger = logging.getLogger(__name__)

class OneAPIUserResolver:
    """OneAPI用户信息解析器 - 数据库直连版本"""
    
    def __init__(self):
        self.db_config = {
            "host": os.getenv("ONEAPI_DB_HOST", "localhost"),
            "port": int(os.getenv("ONEAPI_DB_PORT", "5001")),  # 注意这里是映射后的端口
            "user": os.getenv("ONEAPI_DB_USER", "oneapi"),
            "password": os.getenv("ONEAPI_DB_PASSWORD", "123456"),
            "db": os.getenv("ONEAPI_DB_NAME", "one-api"),
            "charset": "utf8mb4",
            "autocommit": True
        }
        self.pool = None
        logger.info(f"数据库配置: {self.db_config['host']}:{self.db_config['port']}/{self.db_config['db']}")
    
    async def init_pool(self):
        """初始化数据库连接池"""
        try:
            self.pool = await aiomysql.create_pool(
                minsize=1,
                maxsize=5,
                **self.db_config
            )
            logger.info("✅ 数据库连接池初始化成功")
            return True
        except Exception as e:
            logger.error(f"❌ 数据库连接池初始化失败: {e}")
            return False
    
    async def close_pool(self):
        """关闭数据库连接池"""
        if self.pool:
            self.pool.close()
            await self.pool.wait_closed()
            logger.info("数据库连接池已关闭")
    
    async def get_user_by_token(self, api_key: str) -> Optional[Dict]:
        """通过API Key获取用户信息"""
        if not self.pool:
            if not await self.init_pool():
                return None

        # 处理API Key格式：OneAPI数据库中存储的key没有sk-前缀
        db_key = api_key
        if api_key.startswith('sk-'):
            db_key = api_key[3:]  # 移除sk-前缀

        try:
            async with self.pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 查询token信息
                    token_query = """
                        SELECT user_id, name, status, created_time, expired_time, remain_quota, unlimited_quota, used_quota
                        FROM tokens
                        WHERE `key` = %s AND status = 1
                    """

                    await cursor.execute(token_query, (db_key,))
                    token_info = await cursor.fetchone()
                    
                    if not token_info:
                        logger.info(f"❌ 未找到API Key: {api_key[:8]}...")
                        return None
                    
                    logger.info(f"✅ 找到token信息: user_id={token_info['user_id']}, name={token_info['name']}")
                    
                    # 查询用户信息
                    user_query = """
                        SELECT id, username, email, display_name, role, status, quota, used_quota, request_count, `group`
                        FROM users
                        WHERE id = %s
                    """
                    
                    await cursor.execute(user_query, (token_info['user_id'],))
                    user_info = await cursor.fetchone()
                    
                    if not user_info:
                        logger.warning(f"⚠️ 未找到用户信息: user_id={token_info['user_id']}")
                        return None
                    
                    logger.info(f"✅ 获取到用户信息: {user_info['username']} ({user_info['email']})")
                    
                    # 构造返回数据
                    result = {
                        "id": user_info['id'],
                        "username": user_info['username'],
                        "email": user_info['email'],
                        "display_name": user_info['display_name'],
                        "role": user_info['role'],
                        "status": user_info['status'],
                        "group": user_info['group'],
                        "quota": user_info['quota'],
                        "used_quota": user_info['used_quota'],
                        "request_count": user_info['request_count'],
                        "token_name": token_info['name'],
                        "token_status": token_info['status'],
                        "token_remain_quota": token_info['remain_quota'],
                        "token_used_quota": token_info['used_quota'],
                        "unlimited_quota": bool(token_info['unlimited_quota']),
                        "token_created_time": datetime.fromtimestamp(token_info['created_time']).isoformat() if token_info['created_time'] else None,
                        "token_expired_time": datetime.fromtimestamp(token_info['expired_time']).isoformat() if token_info['expired_time'] > 0 else "永不过期",
                        "source": "database",
                        "retrieved_at": datetime.now().isoformat()
                    }
                    
                    return result
                    
        except Exception as e:
            logger.error(f"❌ 数据库查询失败: {e}")
            return None
    
    async def get_user_stats(self, user_id: int) -> Optional[Dict]:
        """获取用户统计信息"""
        if not self.pool:
            if not await self.init_pool():
                return None
        
        try:
            async with self.pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 查询用户的token数量
                    token_count_query = """
                        SELECT COUNT(*) as token_count
                        FROM tokens 
                        WHERE user_id = %s AND status = 1
                    """
                    
                    await cursor.execute(token_count_query, (user_id,))
                    token_count = await cursor.fetchone()
                    
                    # 查询用户的使用统计（如果有logs表）
                    usage_query = """
                        SELECT COUNT(*) as request_count, SUM(quota) as total_quota_used
                        FROM logs 
                        WHERE user_id = %s
                    """
                    
                    try:
                        await cursor.execute(usage_query, (user_id,))
                        usage_stats = await cursor.fetchone()
                    except:
                        # 如果logs表不存在或结构不同，使用默认值
                        usage_stats = {"request_count": 0, "total_quota_used": 0}
                    
                    return {
                        "user_id": user_id,
                        "token_count": token_count['token_count'] if token_count else 0,
                        "request_count": usage_stats['request_count'] if usage_stats else 0,
                        "total_quota_used": usage_stats['total_quota_used'] if usage_stats else 0,
                        "retrieved_at": datetime.now().isoformat()
                    }
                    
        except Exception as e:
            logger.error(f"❌ 获取用户统计失败: {e}")
            return None
    
    async def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            if not self.pool:
                if not await self.init_pool():
                    return False
            
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("SELECT 1")
                    result = await cursor.fetchone()
                    
            logger.info("✅ 数据库连接测试成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库连接测试失败: {e}")
            return False
    
    async def get_database_info(self) -> Optional[Dict]:
        """获取数据库信息"""
        try:
            if not self.pool:
                if not await self.init_pool():
                    return None
            
            async with self.pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 获取表信息
                    await cursor.execute("SHOW TABLES")
                    tables = await cursor.fetchall()
                    
                    # 获取用户数量
                    await cursor.execute("SELECT COUNT(*) as user_count FROM users")
                    user_count = await cursor.fetchone()
                    
                    # 获取token数量
                    await cursor.execute("SELECT COUNT(*) as token_count FROM tokens WHERE status = 1")
                    token_count = await cursor.fetchone()
                    
                    return {
                        "tables": [table[f'Tables_in_{self.db_config["db"]}'] for table in tables],
                        "user_count": user_count['user_count'] if user_count else 0,
                        "active_token_count": token_count['token_count'] if token_count else 0,
                        "database_name": self.db_config["db"],
                        "retrieved_at": datetime.now().isoformat()
                    }
                    
        except Exception as e:
            logger.error(f"❌ 获取数据库信息失败: {e}")
            return None

# 全局实例
user_resolver = OneAPIUserResolver()

async def get_user_info_from_database(api_key: str) -> Optional[Dict]:
    """获取用户信息的便捷函数"""
    return await user_resolver.get_user_by_token(api_key)

async def test_database_connection() -> bool:
    """测试数据库连接的便捷函数"""
    return await user_resolver.test_connection()

if __name__ == "__main__":
    # 测试脚本
    async def main():
        print("🔍 测试OneAPI数据库连接...")
        
        # 测试连接
        if await test_database_connection():
            print("✅ 数据库连接成功")
            
            # 获取数据库信息
            db_info = await user_resolver.get_database_info()
            if db_info:
                print(f"📊 数据库信息: {db_info}")
            
            # 测试用户查询（使用实际的API Key）
            test_api_key = "sk-sPVKUn4GVZaQG2go1e74A84520E7461e86201859C51e89Ae"
            user_info = await get_user_info_from_database(test_api_key)
            if user_info:
                print(f"👤 用户信息: {user_info}")
            else:
                print("❌ 未找到用户信息")
        else:
            print("❌ 数据库连接失败")
        
        await user_resolver.close_pool()
    
    asyncio.run(main())
