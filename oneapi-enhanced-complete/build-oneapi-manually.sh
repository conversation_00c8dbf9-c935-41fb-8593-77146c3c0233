#!/bin/bash

# OneAPI手动构建脚本 - 当镜像下载失败时的备用方案

set -e

echo "🔨 OneAPI手动构建脚本"
echo "==================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查构建依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        return 1
    fi
    
    # 检查Git
    if ! command -v git &> /dev/null; then
        log_error "Git未安装，请先安装Git"
        return 1
    fi
    
    # 检查网络连接
    if ! ping -c 1 github.com &> /dev/null; then
        log_error "无法连接到GitHub"
        return 1
    fi
    
    log_success "所有依赖检查通过"
    return 0
}

# 克隆OneAPI源码
clone_oneapi() {
    log_info "克隆OneAPI源码..."
    
    # 清理可能存在的目录
    rm -rf one-api
    
    # 克隆源码
    if git clone https://github.com/songquanpeng/one-api.git; then
        log_success "源码克隆成功"
        return 0
    else
        log_error "源码克隆失败"
        return 1
    fi
}

# 构建Docker镜像
build_image() {
    log_info "构建OneAPI Docker镜像..."
    
    cd one-api
    
    # 检查Dockerfile是否存在
    if [ ! -f Dockerfile ]; then
        log_error "未找到Dockerfile"
        return 1
    fi
    
    log_info "开始构建镜像，这可能需要几分钟..."
    
    # 构建镜像
    if docker build -t justsong/one-api:latest .; then
        log_success "镜像构建成功"
        cd ..
        return 0
    else
        log_error "镜像构建失败"
        cd ..
        return 1
    fi
}

# 验证构建结果
verify_build() {
    log_info "验证构建结果..."
    
    # 检查镜像是否存在
    if docker images "justsong/one-api:latest" --format "{{.Repository}}" | grep -q "justsong/one-api"; then
        log_success "镜像构建验证成功"
        
        # 显示镜像信息
        echo ""
        log_info "镜像信息:"
        docker images "justsong/one-api:latest" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
        
        return 0
    else
        log_error "镜像构建验证失败"
        return 1
    fi
}

# 导出镜像
export_image() {
    log_info "导出镜像文件..."
    
    mkdir -p docker-images
    cd docker-images
    
    # 导出镜像
    if docker save -o justsong_one-api_latest.tar justsong/one-api:latest; then
        log_success "镜像导出成功"
        
        # 压缩镜像
        log_info "压缩镜像文件..."
        gzip justsong_one-api_latest.tar
        
        log_success "镜像压缩完成: justsong_one-api_latest.tar.gz"
        
        # 显示文件信息
        echo ""
        log_info "导出文件信息:"
        ls -lh justsong_one-api_latest.tar.gz
        
        cd ..
        return 0
    else
        log_error "镜像导出失败"
        cd ..
        return 1
    fi
}

# 创建简化的Dockerfile（备用方案）
create_simple_dockerfile() {
    log_info "创建简化的Dockerfile..."
    
    mkdir -p oneapi-simple
    cd oneapi-simple
    
    cat > Dockerfile << 'EOF'
FROM node:18-alpine AS builder

WORKDIR /app

# 如果有预编译的文件，可以直接复制
# 这里提供一个最小化的示例
RUN echo '{"name":"one-api","version":"0.6.6"}' > package.json

FROM alpine:latest

RUN apk add --no-cache ca-certificates

WORKDIR /app

# 创建一个简单的启动脚本
RUN echo '#!/bin/sh' > start.sh && \
    echo 'echo "OneAPI服务启动中..."' >> start.sh && \
    echo 'echo "请访问 http://localhost:3000"' >> start.sh && \
    echo 'while true; do sleep 3600; done' >> start.sh && \
    chmod +x start.sh

EXPOSE 3000

CMD ["./start.sh"]
EOF

    log_warning "这是一个简化的Dockerfile，仅用于测试"
    log_warning "实际使用请从GitHub获取完整源码"
    
    cd ..
}

# 显示使用说明
show_usage() {
    echo ""
    log_success "🎉 OneAPI镜像构建完成！"
    echo ""
    echo "📋 下一步操作:"
    echo ""
    echo "1. 如果要在当前机器使用:"
    echo "   docker run -d -p 3000:3000 --name oneapi justsong/one-api:latest"
    echo ""
    echo "2. 如果要传输到其他机器:"
    echo "   # 镜像文件位置: docker-images/justsong_one-api_latest.tar.gz"
    echo "   # 在目标机器上导入:"
    echo "   gunzip -c justsong_one-api_latest.tar.gz | docker load"
    echo ""
    echo "3. 使用Docker Compose部署:"
    echo "   # 现在可以正常运行 docker-compose up -d"
    echo ""
    echo "📊 构建统计:"
    echo "   镜像大小: $(docker images justsong/one-api:latest --format "{{.Size}}")"
    echo "   构建时间: $(date)"
}

# 主函数
main() {
    echo ""
    log_info "OneAPI镜像手动构建流程开始..."
    echo ""
    
    # 检查依赖
    if ! check_dependencies; then
        log_error "依赖检查失败，请解决依赖问题后重试"
        exit 1
    fi
    
    echo ""
    
    # 询问用户是否继续
    read -p "是否继续构建OneAPI镜像？这将下载源码并构建镜像 (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "取消构建"
        exit 0
    fi
    
    # 克隆源码
    if ! clone_oneapi; then
        log_error "源码克隆失败"
        exit 1
    fi
    
    echo ""
    
    # 构建镜像
    if ! build_image; then
        log_error "镜像构建失败"
        
        # 提供备用方案
        echo ""
        log_warning "尝试创建简化版本..."
        create_simple_dockerfile
        
        exit 1
    fi
    
    echo ""
    
    # 验证构建
    if ! verify_build; then
        log_error "构建验证失败"
        exit 1
    fi
    
    echo ""
    
    # 导出镜像
    if export_image; then
        show_usage
    else
        log_warning "镜像导出失败，但镜像已构建成功"
        show_usage
    fi
    
    # 清理源码目录
    read -p "是否清理源码目录？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -rf one-api
        log_success "源码目录已清理"
    fi
}

main "$@"
