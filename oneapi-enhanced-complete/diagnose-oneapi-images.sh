#!/bin/bash

# OneAPI镜像下载问题诊断脚本

set -e

echo "🔍 OneAPI镜像下载问题诊断"
echo "========================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker环境
check_docker() {
    log_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        return 1
    fi
    
    log_success "Docker版本: $(docker --version)"
    
    # 检查Docker服务状态
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行"
        return 1
    fi
    
    log_success "Docker服务正常运行"
    return 0
}

# 检查网络连接
check_network() {
    log_info "检查网络连接..."
    
    # 检查基本网络
    if ping -c 1 8.8.8.8 &> /dev/null; then
        log_success "基本网络连接正常"
    else
        log_error "基本网络连接失败"
        return 1
    fi
    
    # 检查Docker Hub连接
    if curl -s --connect-timeout 10 https://registry-1.docker.io/v2/ &> /dev/null; then
        log_success "Docker Hub连接正常"
    else
        log_warning "Docker Hub连接可能有问题"
    fi
    
    # 检查GitHub Container Registry连接
    if curl -s --connect-timeout 10 https://ghcr.io/v2/ &> /dev/null; then
        log_success "GitHub Container Registry连接正常"
    else
        log_warning "GitHub Container Registry连接可能有问题"
    fi
    
    return 0
}

# 测试基础镜像下载
test_basic_image() {
    log_info "测试基础镜像下载..."
    
    if docker pull hello-world &> /dev/null; then
        log_success "基础镜像下载成功"
        docker rmi hello-world &> /dev/null || true
        return 0
    else
        log_error "基础镜像下载失败"
        return 1
    fi
}

# 详细测试OneAPI镜像源
test_oneapi_images() {
    log_info "详细测试OneAPI镜像源..."
    
    local images=(
        "justsong/one-api:latest"
        "songquanpeng/one-api:latest"
        "ghcr.io/songquanpeng/one-api:latest"
    )
    
    for image in "${images[@]}"; do
        echo ""
        log_info "测试镜像: $image"
        
        # 检查镜像是否存在
        log_info "检查镜像是否存在..."
        if docker manifest inspect "$image" &> /dev/null; then
            log_success "镜像存在: $image"
            
            # 尝试下载
            log_info "尝试下载镜像..."
            if timeout 300 docker pull "$image"; then
                log_success "下载成功: $image"
                
                # 检查镜像信息
                log_info "镜像信息:"
                docker images "$image" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
                
                # 清理镜像
                docker rmi "$image" &> /dev/null || true
                
                return 0
            else
                log_error "下载失败: $image"
            fi
        else
            log_error "镜像不存在或无法访问: $image"
        fi
    done
    
    return 1
}

# 检查Docker配置
check_docker_config() {
    log_info "检查Docker配置..."
    
    # 检查Docker daemon配置
    if [ -f /etc/docker/daemon.json ]; then
        log_info "Docker daemon配置:"
        cat /etc/docker/daemon.json
    else
        log_info "未找到Docker daemon配置文件"
    fi
    
    # 检查Docker镜像加速器
    log_info "检查Docker镜像加速器配置..."
    docker info | grep -A 10 "Registry Mirrors" || log_info "未配置镜像加速器"
}

# 搜索可用的OneAPI镜像
search_oneapi_images() {
    log_info "搜索可用的OneAPI镜像..."
    
    # 搜索Docker Hub
    log_info "搜索Docker Hub..."
    if command -v curl &> /dev/null; then
        curl -s "https://registry.hub.docker.com/v2/repositories/justsong/one-api/tags/" | \
        python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if 'results' in data:
        print('可用标签:')
        for tag in data['results'][:5]:
            print(f\"  - {tag['name']}\")
except:
    print('无法解析搜索结果')
" 2>/dev/null || log_warning "无法搜索Docker Hub"
    fi
}

# 提供解决方案
provide_solutions() {
    echo ""
    log_info "🔧 可能的解决方案:"
    echo ""
    
    echo "1. 配置Docker镜像加速器:"
    echo "   sudo mkdir -p /etc/docker"
    echo "   sudo tee /etc/docker/daemon.json <<-'EOF'"
    echo "   {"
    echo "     \"registry-mirrors\": ["
    echo "       \"https://mirror.ccs.tencentyun.com\","
    echo "       \"https://docker.mirrors.ustc.edu.cn\","
    echo "       \"https://hub-mirror.c.163.com\""
    echo "     ]"
    echo "   }"
    echo "   EOF"
    echo "   sudo systemctl restart docker"
    echo ""
    
    echo "2. 使用代理下载:"
    echo "   export HTTP_PROXY=http://proxy:port"
    echo "   export HTTPS_PROXY=http://proxy:port"
    echo "   docker pull justsong/one-api:latest"
    echo ""
    
    echo "3. 手动构建镜像:"
    echo "   git clone https://github.com/songquanpeng/one-api.git"
    echo "   cd one-api"
    echo "   docker build -t justsong/one-api:latest ."
    echo ""
    
    echo "4. 使用预构建镜像文件:"
    echo "   # 从其他机器获取镜像文件"
    echo "   docker load -i oneapi-image.tar"
    echo ""
    
    echo "5. 尝试不同的镜像标签:"
    echo "   docker pull justsong/one-api:v0.6.6"
    echo "   docker tag justsong/one-api:v0.6.6 justsong/one-api:latest"
}

# 创建修复脚本
create_fix_script() {
    log_info "创建修复脚本..."
    
    cat > fix-oneapi-download.sh << 'EOF'
#!/bin/bash

# OneAPI镜像下载修复脚本

echo "🔧 OneAPI镜像下载修复"
echo "==================="

# 配置镜像加速器
configure_mirror() {
    echo "配置Docker镜像加速器..."
    
    sudo mkdir -p /etc/docker
    sudo tee /etc/docker/daemon.json <<-'EOFMIRROR'
{
  "registry-mirrors": [
    "https://mirror.ccs.tencentyun.com",
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com"
  ]
}
EOFMIRROR
    
    sudo systemctl restart docker
    echo "镜像加速器配置完成"
}

# 尝试多种方法下载OneAPI镜像
download_oneapi() {
    echo "尝试下载OneAPI镜像..."
    
    # 方法1: 直接下载latest
    if docker pull justsong/one-api:latest; then
        echo "✅ 下载成功: justsong/one-api:latest"
        return 0
    fi
    
    # 方法2: 尝试其他用户名
    if docker pull songquanpeng/one-api:latest; then
        echo "✅ 下载成功: songquanpeng/one-api:latest"
        docker tag songquanpeng/one-api:latest justsong/one-api:latest
        return 0
    fi
    
    # 方法3: 尝试GitHub Container Registry
    if docker pull ghcr.io/songquanpeng/one-api:latest; then
        echo "✅ 下载成功: ghcr.io/songquanpeng/one-api:latest"
        docker tag ghcr.io/songquanpeng/one-api:latest justsong/one-api:latest
        return 0
    fi
    
    # 方法4: 尝试特定版本
    for version in v0.6.6 v0.6.5 v0.6.4; do
        if docker pull justsong/one-api:$version; then
            echo "✅ 下载成功: justsong/one-api:$version"
            docker tag justsong/one-api:$version justsong/one-api:latest
            return 0
        fi
    done
    
    echo "❌ 所有方法都失败了"
    return 1
}

# 主函数
main() {
    read -p "是否配置Docker镜像加速器? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        configure_mirror
    fi
    
    download_oneapi
}

main "$@"
EOF

    chmod +x fix-oneapi-download.sh
    log_success "修复脚本已创建: fix-oneapi-download.sh"
}

# 主函数
main() {
    echo ""
    
    # 检查Docker环境
    if ! check_docker; then
        log_error "Docker环境有问题，请先解决Docker问题"
        exit 1
    fi
    
    echo ""
    
    # 检查网络连接
    if ! check_network; then
        log_error "网络连接有问题"
    fi
    
    echo ""
    
    # 测试基础镜像
    if ! test_basic_image; then
        log_error "基础镜像下载失败，可能是网络或Docker配置问题"
    fi
    
    echo ""
    
    # 检查Docker配置
    check_docker_config
    
    echo ""
    
    # 搜索可用镜像
    search_oneapi_images
    
    echo ""
    
    # 测试OneAPI镜像
    if ! test_oneapi_images; then
        log_error "所有OneAPI镜像源都失败"
        provide_solutions
        create_fix_script
    else
        log_success "找到可用的OneAPI镜像源"
    fi
}

main "$@"
