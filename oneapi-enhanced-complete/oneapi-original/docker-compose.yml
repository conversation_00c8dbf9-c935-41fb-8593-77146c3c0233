version: '3.2'

services:
  one-api:
    image: justsong/one-api:latest
    container_name: one-api
    platform: linux/amd64
    restart: always
    ports:
      - "3000:3000"
    volumes:
      - ./data/oneapi:/data
      - ./logs:/app/logs
    environment:
      - SQL_DSN=oneapi:123456@tcp(db:3306)/one-api
      - REDIS_CONN_STRING=redis://redis
      - TZ=Asia/Shanghai
      - SESSION_SECRET=ae5e609a2057988e420b83e8ff743575dc17cea291d0410d87ef0c06f05add02
      - LANGFUSE_ENABLED=true
      - LANGFUSE_BASE_URL=http://localhost:1919
      - LANGFUSE_PUBLIC_KEY=pk-lf-ac71521b-6a96-4871-85f8-967c7ee634fd
      - LANGFUSE_SECRET_KEY=******************************************
      - LANGFUSE_PROJECT_ID=your-project-id
    depends_on:
      - redis
      - db
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  redis:
    image: redis:latest
    container_name: redis
    restart: always

  db:
    image: mysql:8.2.0
    restart: always
    container_name: mysql
    volumes:
      - ./data/mysql:/var/lib/mysql
    ports:
      - '5001:3306'
    environment:
      TZ: Asia/Shanghai
      MYSQL_ROOT_PASSWORD: 'OneAPI@justsong'
      MYSQL_USER: oneapi
      MYSQL_PASSWORD: '123456'
      MYSQL_DATABASE: one-api
