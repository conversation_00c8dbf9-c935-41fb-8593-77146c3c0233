version: '3.2'

services:
  one-api:
    image: justsong/one-api:latest
    container_name: one-api
    restart: always
    ports:
      - "3000:3000"
    environment:
      - SQL_DSN=oneapi:123456@tcp(mysql:3306)/one-api
      - REDIS_CONN_STRING=redis://redis:6379
      - SESSION_SECRET=random_string_here
      - TZ=Asia/Shanghai
    depends_on:
      - redis
      - mysql
    volumes:
      - ./data/oneapi:/data
    networks:
      - oneapi-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # OneAPI代理服务（用于日志记录）
  oneapi-proxy:
    build:
      context: ../logging-system
      dockerfile: Dockerfile
    container_name: oneapi-proxy
    restart: always
    ports:
      - "3001:3001"
    environment:
      - ONEAPI_BASE_URL=http://one-api:3000
      - ONEAPI_DB_HOST=mysql
      - ONEAPI_DB_PORT=3306
      - ONEAPI_DB_USER=oneapi
      - ONEAPI_DB_PASSWORD=123456
      - ONEAPI_DB_NAME=one-api
      - LOG_LEVEL=INFO
    depends_on:
      - one-api
      - mysql
    volumes:
      - ./data/logs:/app/logs
    networks:
      - oneapi-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  mysql:
    image: mysql:8.0
    container_name: mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: one-api
      MYSQL_USER: oneapi
      MYSQL_PASSWORD: 123456
    volumes:
      - ./data/mysql:/var/lib/mysql
    ports:
      - "127.0.0.1:3306:3306"
    networks:
      - oneapi-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: redis
    restart: always
    volumes:
      - ./data/redis:/data
    ports:
      - "127.0.0.1:6379:6379"
    networks:
      - oneapi-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ../nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ../nginx/ssl:/etc/nginx/ssl:ro
      - ./data/nginx/logs:/var/log/nginx
    depends_on:
      - one-api
      - oneapi-proxy
    networks:
      - oneapi-network

networks:
  oneapi-network:
    driver: bridge

volumes:
  mysql_data:
  redis_data:
  oneapi_data:
  logs_data:
