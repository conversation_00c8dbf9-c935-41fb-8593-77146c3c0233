version: '3.8'

services:
  # OneAPI服务
  oneapi:
    build: .
    container_name: oneapi
    ports:
      - "3000:3000"
    environment:
      # 数据库配置
      - SQL_DSN=mysql://oneapi:oneapi_password@mysql:3306/oneapi
      
      # Langfuse集成配置
      - LANGFUSE_ENABLED=true
      - LANGFUSE_BASE_URL=http://langfuse:3000
      - LANGFUSE_PUBLIC_KEY=${LANGFUSE_PUBLIC_KEY}
      - LANGFUSE_SECRET_KEY=${LANGFUSE_SECRET_KEY}
      - LANGFUSE_PROJECT_ID=${LANGFUSE_PROJECT_ID}
      
      # 其他配置
      - SESSION_SECRET=${SESSION_SECRET:-your-session-secret}
      - INITIAL_ROOT_TOKEN=${INITIAL_ROOT_TOKEN:-sk-initial-root-token}
    depends_on:
      - mysql
      - langfuse
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - oneapi-network

  # Langfuse服务
  langfuse:
    image: langfuse/langfuse:latest
    container_name: langfuse
    ports:
      - "1919:3000"
    environment:
      # 数据库配置
      - DATABASE_URL=*****************************************************/langfuse
      
      # Langfuse配置
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET:-your-nextauth-secret}
      - NEXTAUTH_URL=http://localhost:1919
      - SALT=${SALT:-your-salt}
      
      # 可选：启用身份验证
      - AUTH_DISABLE_USERNAME_PASSWORD=false
      
      # 可选：SMTP配置（用于邮件通知）
      # - SMTP_CONNECTION_URL=smtps://username:<EMAIL>:465
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - oneapi-network

  # OneAPI数据库 (MySQL)
  mysql:
    image: mysql:8.0
    container_name: oneapi-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=oneapi
      - MYSQL_USER=oneapi
      - MYSQL_PASSWORD=oneapi_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    restart: unless-stopped
    networks:
      - oneapi-network

  # Langfuse数据库 (PostgreSQL)
  postgres:
    image: postgres:15
    container_name: langfuse-postgres
    environment:
      - POSTGRES_DB=langfuse
      - POSTGRES_USER=langfuse
      - POSTGRES_PASSWORD=langfuse_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - oneapi-network

  # Redis (可选，用于缓存)
  redis:
    image: redis:7-alpine
    container_name: oneapi-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - oneapi-network

volumes:
  mysql_data:
  postgres_data:
  redis_data:

networks:
  oneapi-network:
    driver: bridge

# 环境变量文件示例 (.env)
# 创建 .env 文件并填入以下内容：
#
# # Langfuse API密钥（从Langfuse控制台获取）
# LANGFUSE_PUBLIC_KEY=pk-lf-your-public-key
# LANGFUSE_SECRET_KEY=sk-lf-your-secret-key
# LANGFUSE_PROJECT_ID=your-project-id
#
# # 安全密钥
# SESSION_SECRET=your-session-secret-here
# NEXTAUTH_SECRET=your-nextauth-secret-here
# SALT=your-salt-here
#
# # 初始管理员Token
# INITIAL_ROOT_TOKEN=sk-your-initial-root-token
