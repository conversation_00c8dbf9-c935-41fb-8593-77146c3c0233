version: '3.8'

services:
  # OneAPI服务
  one-api:
    image: justsong/one-api:latest
    container_name: one-api
    platform: linux/amd64
    restart: always
    ports:
      - "3000:3000"
    volumes:
      - ./data/oneapi:/data
      - ./logs:/app/logs
    environment:
      - SQL_DSN=oneapi:123456@tcp(mysql:3306)/one-api
      - REDIS_CONN_STRING=redis://redis-oneapi
      - TZ=Asia/Shanghai
      - SESSION_SECRET=ae5e609a2057988e420b83e8ff743575dc17cea291d0410d87ef0c06f05add02
    depends_on:
      - redis-oneapi
      - mysql
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # OneAPI-Langfuse代理服务
  langfuse-proxy:
    build:
      context: ./langfuse-proxy
      dockerfile: Dockerfile
    container_name: langfuse-proxy
    restart: always
    ports:
      - "3001:3001"
    environment:
      - ONEAPI_BASE_URL=http://one-api:3000
      - LANGFUSE_SECRET_KEY=******************************************
      - LANGFUSE_PUBLIC_KEY=pk-lf-ac71521b-6a96-4871-85f8-967c7ee634fd
      - LANGFUSE_HOST=http://langfuse-web:3000
      - PROXY_PORT=3001
    depends_on:
      - one-api
      - langfuse-web
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Langfuse Web服务
  langfuse-web:
    image: langfuse/langfuse:3
    container_name: langfuse-web
    restart: always
    ports:
      - "1919:3000"
    environment:
      DATABASE_URL: *****************************************************/postgres
      SALT: "mysalt"
      ENCRYPTION_KEY: "0000000000000000000000000000000000000000000000000000000000000000"
      NEXTAUTH_URL: http://localhost:1919
      NEXTAUTH_SECRET: mysecret
      TELEMETRY_ENABLED: true
      LANGFUSE_ENABLE_EXPERIMENTAL_FEATURES: true
      CLICKHOUSE_MIGRATION_URL: clickhouse://clickhouse:9000
      CLICKHOUSE_URL: http://clickhouse:8123
      CLICKHOUSE_USER: clickhouse
      CLICKHOUSE_PASSWORD: clickhouse
      CLICKHOUSE_CLUSTER_ENABLED: false
      LANGFUSE_S3_EVENT_UPLOAD_BUCKET: langfuse
      LANGFUSE_S3_EVENT_UPLOAD_REGION: auto
      LANGFUSE_S3_EVENT_UPLOAD_ACCESS_KEY_ID: minio
      LANGFUSE_S3_EVENT_UPLOAD_SECRET_ACCESS_KEY: miniosecret
      LANGFUSE_S3_EVENT_UPLOAD_ENDPOINT: http://minio:9000
      LANGFUSE_S3_EVENT_UPLOAD_FORCE_PATH_STYLE: true
      LANGFUSE_S3_EVENT_UPLOAD_PREFIX: events/
      LANGFUSE_S3_MEDIA_UPLOAD_BUCKET: langfuse
      LANGFUSE_S3_MEDIA_UPLOAD_REGION: auto
      LANGFUSE_S3_MEDIA_UPLOAD_ACCESS_KEY_ID: minio
      LANGFUSE_S3_MEDIA_UPLOAD_SECRET_ACCESS_KEY: miniosecret
      LANGFUSE_S3_MEDIA_UPLOAD_ENDPOINT: http://localhost:9090
      LANGFUSE_S3_MEDIA_UPLOAD_FORCE_PATH_STYLE: true
      LANGFUSE_S3_MEDIA_UPLOAD_PREFIX: media/
      REDIS_HOST: redis-langfuse
      REDIS_PORT: 6379
      REDIS_AUTH: myredissecret
      REDIS_TLS_ENABLED: false
    depends_on:
      - postgres-langfuse
      - redis-langfuse
      - clickhouse
      - minio

  # Langfuse Worker服务
  langfuse-worker:
    image: langfuse/langfuse-worker:3
    container_name: langfuse-worker
    restart: always
    ports:
      - "127.0.0.1:3030:3030"
    environment:
      DATABASE_URL: *****************************************************/postgres
      SALT: "mysalt"
      ENCRYPTION_KEY: "0000000000000000000000000000000000000000000000000000000000000000"
      TELEMETRY_ENABLED: true
      LANGFUSE_ENABLE_EXPERIMENTAL_FEATURES: true
      CLICKHOUSE_MIGRATION_URL: clickhouse://clickhouse:9000
      CLICKHOUSE_URL: http://clickhouse:8123
      CLICKHOUSE_USER: clickhouse
      CLICKHOUSE_PASSWORD: clickhouse
      CLICKHOUSE_CLUSTER_ENABLED: false
      LANGFUSE_S3_EVENT_UPLOAD_BUCKET: langfuse
      LANGFUSE_S3_EVENT_UPLOAD_REGION: auto
      LANGFUSE_S3_EVENT_UPLOAD_ACCESS_KEY_ID: minio
      LANGFUSE_S3_EVENT_UPLOAD_SECRET_ACCESS_KEY: miniosecret
      LANGFUSE_S3_EVENT_UPLOAD_ENDPOINT: http://minio:9000
      LANGFUSE_S3_EVENT_UPLOAD_FORCE_PATH_STYLE: true
      LANGFUSE_S3_EVENT_UPLOAD_PREFIX: events/
      LANGFUSE_S3_MEDIA_UPLOAD_BUCKET: langfuse
      LANGFUSE_S3_MEDIA_UPLOAD_REGION: auto
      LANGFUSE_S3_MEDIA_UPLOAD_ACCESS_KEY_ID: minio
      LANGFUSE_S3_MEDIA_UPLOAD_SECRET_ACCESS_KEY: miniosecret
      LANGFUSE_S3_MEDIA_UPLOAD_ENDPOINT: http://localhost:9090
      LANGFUSE_S3_MEDIA_UPLOAD_FORCE_PATH_STYLE: true
      LANGFUSE_S3_MEDIA_UPLOAD_PREFIX: media/
      REDIS_HOST: redis-langfuse
      REDIS_PORT: 6379
      REDIS_AUTH: myredissecret
      REDIS_TLS_ENABLED: false
    depends_on:
      - postgres-langfuse
      - redis-langfuse
      - clickhouse
      - minio

  # OneAPI数据库和缓存
  redis-oneapi:
    image: redis:latest
    container_name: redis-oneapi
    restart: always

  mysql:
    image: mysql:8.2.0
    restart: always
    container_name: mysql
    volumes:
      - ./data/mysql:/var/lib/mysql
    ports:
      - '5001:3306'
    environment:
      TZ: Asia/Shanghai
      MYSQL_ROOT_PASSWORD: 'OneAPI@justsong'
      MYSQL_USER: oneapi
      MYSQL_PASSWORD: '123456'
      MYSQL_DATABASE: one-api

  # Langfuse数据库和依赖服务
  postgres-langfuse:
    image: postgres:latest
    container_name: postgres-langfuse
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres
    ports:
      - "127.0.0.1:5432:5432"
    volumes:
      - langfuse_postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 3s
      timeout: 3s
      retries: 10

  redis-langfuse:
    image: redis:7
    container_name: redis-langfuse
    restart: always
    command: >
      --requirepass myredissecret
    ports:
      - "127.0.0.1:6380:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 3s
      timeout: 10s
      retries: 10

  clickhouse:
    image: clickhouse/clickhouse-server
    container_name: clickhouse
    restart: always
    user: "101:101"
    environment:
      CLICKHOUSE_DB: default
      CLICKHOUSE_USER: clickhouse
      CLICKHOUSE_PASSWORD: clickhouse
    volumes:
      - langfuse_clickhouse_data:/var/lib/clickhouse
      - langfuse_clickhouse_logs:/var/log/clickhouse-server
    ports:
      - "127.0.0.1:8123:8123"
      - "127.0.0.1:1818:9000"
    healthcheck:
      test: wget --no-verbose --tries=1 --spider http://localhost:8123/ping || exit 1
      interval: 5s
      timeout: 5s
      retries: 10
      start_period: 1s

  minio:
    image: minio/minio
    container_name: minio
    restart: always
    entrypoint: sh
    command: -c 'mkdir -p /data/langfuse && minio server --address ":9000" --console-address ":9001" /data'
    environment:
      MINIO_ROOT_USER: minio
      MINIO_ROOT_PASSWORD: miniosecret
    ports:
      - "9090:9000"
      - "127.0.0.1:9091:9001"
    volumes:
      - langfuse_minio_data:/data
    healthcheck:
      test: ["CMD", "mc", "ready", "local"]
      interval: 1s
      timeout: 5s
      retries: 5
      start_period: 1s

volumes:
  langfuse_postgres_data:
    driver: local
  langfuse_clickhouse_data:
    driver: local
  langfuse_clickhouse_logs:
    driver: local
  langfuse_minio_data:
    driver: local
