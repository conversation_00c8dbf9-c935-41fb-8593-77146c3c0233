version: '3.2'

services:
  # OneAPI主服务
  oneapi:
    image: justsong/one-api:latest
    container_name: oneapi
    restart: always
    ports:
      - '3000:3000'
    volumes:
      - ./data/oneapi:/data
    environment:
      - SQL_DSN=oneapi:123456@tcp(mysql:3306)/one-api
      - REDIS_CONN_STRING=redis://redis:6379
      - SESSION_SECRET=random_string_here
      - TZ=Asia/Shanghai
    depends_on:
      - redis
      - mysql
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # OneAPI代理服务（用户跟踪）
  oneapi-proxy:
    build:
      context: .
      dockerfile: Dockerfile.proxy
    container_name: oneapi-proxy
    restart: always
    ports:
      - '3001:3001'
    volumes:
      - ./data/logs:/app/logs
      - ./conversation_logs.jsonl:/app/conversation_logs.jsonl
    environment:
      - ONEAPI_BASE_URL=http://oneapi:3000
      - ONEAPI_DB_HOST=mysql
      - ONEAPI_DB_PORT=3306
      - ONEAPI_DB_USER=oneapi
      - ONEAPI_DB_PASSWORD=123456
      - ONEAPI_DB_NAME=one-api
      - PROXY_PORT=3001
      - LOG_LEVEL=INFO
    depends_on:
      - oneapi
      - mysql
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3001/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: redis
    restart: always
    volumes:
      - ./data/redis:/data
    command: redis-server --appendonly yes

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: mysql
    restart: always
    ports:
      - '3306:3306'
    volumes:
      - ./data/mysql:/var/lib/mysql
    environment:
      - TZ=Asia/Shanghai
      - MYSQL_ROOT_PASSWORD=OneAPI@justsong
      - MYSQL_USER=oneapi
      - MYSQL_PASSWORD=123456
      - MYSQL_DATABASE=one-api
    command: --default-authentication-plugin=mysql_native_password

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: nginx
    restart: always
    ports:
      - '80:80'
      - '443:443'
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./data/ssl:/etc/nginx/ssl
    depends_on:
      - oneapi
      - oneapi-proxy

volumes:
  mysql_data:
  redis_data:
  oneapi_data:
  logs_data:
