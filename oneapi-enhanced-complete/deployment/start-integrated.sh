#!/bin/bash

echo "🚀 启动OneAPI + Langfuse集成服务..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 停止现有服务
echo "🛑 停止现有服务..."
docker-compose -f docker-compose.yml down 2>/dev/null || true
docker-compose -f docker-compose.integrated.yml down 2>/dev/null || true

# 构建代理服务
echo "🔨 构建Langfuse代理服务..."
docker-compose -f docker-compose.integrated.yml build langfuse-proxy

# 启动所有服务
echo "🚀 启动集成服务..."
docker-compose -f docker-compose.integrated.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "📊 检查服务状态..."
echo ""
echo "=== 服务状态 ==="
docker-compose -f docker-compose.integrated.yml ps

echo ""
echo "=== 服务地址 ==="
echo "🌐 OneAPI管理界面:     http://localhost:3000"
echo "🔗 Langfuse代理服务:   http://localhost:3001"
echo "📊 Langfuse控制台:     http://localhost:1919"
echo "📦 MinIO控制台:        http://localhost:9090"

echo ""
echo "=== 使用说明 ==="
echo "1. 访问OneAPI管理界面: http://localhost:3000"
echo "2. 使用root token登录: sk-34a6c378e51fb8f6626f799361a7be81cade3be3"
echo "3. 创建API Key和配置模型"
echo "4. 使用代理地址发送请求: http://localhost:3001/v1/chat/completions"
echo "5. 在Langfuse控制台查看对话记录: http://localhost:1919"

echo ""
echo "=== 测试命令 ==="
echo "# 测试OneAPI状态"
echo "curl http://localhost:3000/api/status"
echo ""
echo "# 测试代理服务状态"
echo "curl http://localhost:3001/api/status"
echo ""
echo "# 测试聊天完成（需要先配置API Key和模型）"
echo "curl -X POST http://localhost:3001/v1/chat/completions \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer YOUR_API_KEY\" \\"
echo "  -d '{"
echo "    \"model\": \"gpt-3.5-turbo\","
echo "    \"messages\": ["
echo "      {\"role\": \"user\", \"content\": \"你好，请介绍一下自己。\"}"
echo "    ]"
echo "  }'"

echo ""
echo "🎉 集成服务启动完成！"
