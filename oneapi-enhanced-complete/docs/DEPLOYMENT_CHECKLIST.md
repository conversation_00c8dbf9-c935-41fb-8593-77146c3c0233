# 🚀 OneAPI增强版 - 服务器部署检查清单

## 📦 部署包信息

- **包名**: oneapi-enhanced-20250623-141506.tar.gz
- **大小**: 20KB
- **包含文件**: 17个文件和目录

## ✅ 部署前检查清单

### 服务器环境
- [ ] Docker 20.10+ 已安装
- [ ] Docker Compose 2.0+ 已安装
- [ ] 服务器内存 ≥ 2GB
- [ ] 可用磁盘空间 ≥ 10GB
- [ ] 端口 3000, 3001, 3306 未被占用

### 网络配置
- [ ] 服务器可以访问外网（下载Docker镜像）
- [ ] 防火墙已开放必要端口
- [ ] 如使用域名，DNS已正确配置

### 安全配置
- [ ] 已准备修改默认密码
- [ ] 已准备SSL证书（如需HTTPS）
- [ ] 已规划备份策略

## 🛠️ 部署步骤

### 1. 上传和解压
```bash
# 上传文件到服务器
scp oneapi-enhanced-20250623-141506.tar.gz user@server:/opt/

# 登录服务器
ssh user@server

# 解压文件
cd /opt
tar -xzf oneapi-enhanced-20250623-141506.tar.gz
cd oneapi-enhanced-20250623-141506
```

### 2. 配置环境变量
```bash
# 复制配置文件
cp .env.production .env

# 编辑配置（重要！）
nano .env

# 必须修改的配置项：
# - ONEAPI_DB_PASSWORD=your_secure_password_here
# - MYSQL_ROOT_PASSWORD=your_root_password_here
# - SESSION_SECRET=your_session_secret_here
```

### 3. 启动系统
```bash
# 设置执行权限
chmod +x start.sh stop.sh

# 启动系统
./start.sh
```

### 4. 验证部署
```bash
# 检查服务状态
docker-compose -f docker-compose.production.yml ps

# 检查OneAPI主服务
curl http://localhost:3000/api/status

# 检查代理服务
curl http://localhost:3001/health

# 检查代理服务状态
curl http://localhost:3001/api/status
```

## 🔧 配置OneAPI

### 1. 访问管理界面
- 地址: http://your-server:3000
- 默认账号: root
- 默认密码: 123456 (请立即修改)

### 2. 基础配置
1. 修改管理员密码
2. 配置系统设置
3. 添加LLM提供商渠道
4. 设置模型映射

### 3. 用户管理
1. 创建用户账号
2. 为用户生成API Key
3. 设置用户配额

## 📊 测试验证

### 1. 功能测试
```bash
# 进入测试目录
cd tests

# 测试多用户识别
./test-multi-users.sh

# 测试数据库集成
./test-database-integration.sh
```

### 2. API测试
```bash
# 使用代理地址发送请求
curl -X POST http://your-server:3001/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Hello, this is a test."}
    ]
  }'
```

### 3. 监控验证
- 访问 http://your-server:3001 查看监控界面
- 检查对话记录是否正常显示
- 验证用户信息是否正确识别

## 🔒 安全加固

### 1. 修改默认密码
```bash
# 修改MySQL root密码
docker exec -it mysql mysql -u root -p
ALTER USER 'root'@'%' IDENTIFIED BY 'new_secure_password';

# 修改OneAPI管理员密码
# 在Web界面中修改
```

### 2. 配置防火墙
```bash
# Ubuntu/Debian
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw allow 3000  # OneAPI
ufw allow 3001  # Proxy
ufw enable

# CentOS/RHEL
firewall-cmd --permanent --add-port=22/tcp
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp
firewall-cmd --permanent --add-port=3000/tcp
firewall-cmd --permanent --add-port=3001/tcp
firewall-cmd --reload
```

### 3. 配置HTTPS（可选）
```bash
# 编辑nginx.conf，启用SSL配置
# 将SSL证书放在 data/ssl/ 目录
# 重启nginx服务
```

## 📈 监控和维护

### 1. 日志监控
```bash
# 查看系统日志
docker-compose -f docker-compose.production.yml logs -f

# 查看对话日志
tail -f conversation_logs.jsonl

# 查看特定服务日志
docker-compose -f docker-compose.production.yml logs -f oneapi-proxy
```

### 2. 性能监控
```bash
# 检查资源使用
docker stats

# 检查磁盘使用
df -h

# 检查数据库状态
docker exec -it mysql mysql -u oneapi -p -e "SHOW PROCESSLIST;"
```

### 3. 备份策略
```bash
# 备份数据库
docker exec mysql mysqldump -u oneapi -p one-api > backup_$(date +%Y%m%d).sql

# 备份对话日志
cp conversation_logs.jsonl backup/conversation_logs_$(date +%Y%m%d).jsonl

# 备份配置文件
tar -czf config_backup_$(date +%Y%m%d).tar.gz .env docker-compose.production.yml
```

## 🆘 故障排除

### 常见问题

1. **端口被占用**
```bash
# 检查端口占用
netstat -tlnp | grep :3000
netstat -tlnp | grep :3001

# 停止占用进程或修改端口配置
```

2. **数据库连接失败**
```bash
# 检查数据库容器状态
docker-compose -f docker-compose.production.yml logs mysql

# 检查数据库连接
docker exec -it mysql mysql -u oneapi -p
```

3. **代理服务无法获取用户信息**
```bash
# 检查代理服务日志
docker-compose -f docker-compose.production.yml logs oneapi-proxy

# 检查数据库表
docker exec -it mysql mysql -u oneapi -p -e "USE one-api; SHOW TABLES;"
```

### 紧急恢复
```bash
# 停止所有服务
./stop.sh

# 清理容器和网络
docker system prune -f

# 重新启动
./start.sh
```

## 📞 技术支持

### 检查系统状态
```bash
# 系统状态检查脚本
curl -s http://localhost:3001/api/status | jq '.'
```

### 收集诊断信息
```bash
# 创建诊断报告
echo "=== System Info ===" > diagnostic.txt
uname -a >> diagnostic.txt
docker --version >> diagnostic.txt
docker-compose --version >> diagnostic.txt

echo "=== Service Status ===" >> diagnostic.txt
docker-compose -f docker-compose.production.yml ps >> diagnostic.txt

echo "=== Proxy Status ===" >> diagnostic.txt
curl -s http://localhost:3001/api/status >> diagnostic.txt

echo "=== Recent Logs ===" >> diagnostic.txt
docker-compose -f docker-compose.production.yml logs --tail=50 >> diagnostic.txt
```

## ✅ 部署完成检查

部署完成后，确认以下项目：

- [ ] OneAPI主服务正常运行 (http://localhost:3000)
- [ ] 代理服务正常运行 (http://localhost:3001)
- [ ] 数据库连接正常
- [ ] 用户信息能够正确识别
- [ ] 对话记录正常保存
- [ ] Web监控界面正常显示
- [ ] API请求能够正常处理
- [ ] 日志文件正常写入

🎉 **恭喜！OneAPI增强版部署完成！**

现在你拥有了一个功能完整、生产就绪的OneAPI用户对话跟踪系统！
