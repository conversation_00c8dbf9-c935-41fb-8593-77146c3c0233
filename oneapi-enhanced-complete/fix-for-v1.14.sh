#!/bin/bash

# Docker Compose 1.14.0 专用修复脚本
# 将所有配置文件降级到2.1版本格式

set -e

echo "🔧 Docker Compose 1.14.0 兼容性修复"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 修复所有compose文件到2.1版本
fix_all_compose_files() {
    log_info "将所有compose文件降级到2.1版本..."
    
    # 文件列表
    FILES=(
        "oneapi-original/docker-compose.yml"
        "deployment/docker-compose.production.yml"
        "deployment/docker-compose.integrated.yml"
        "deployment/docker-compose.langfuse.yml"
    )
    
    for file in "${FILES[@]}"; do
        if [ -f "$file" ]; then
            log_info "修复文件: $file"
            
            # 创建备份
            cp "$file" "${file}.v114.backup"
            
            # 替换版本号
            sed -i.tmp "s/version: '3\.[0-9]'/version: '2.1'/g" "$file"
            rm -f "${file}.tmp"
            
            log_success "已修复: $file -> 2.1版本"
        else
            log_warning "文件不存在: $file"
        fi
    done
}

# 创建简化的基础配置
create_basic_config() {
    log_info "创建Docker Compose 1.14兼容的基础配置..."
    
    cat > oneapi-basic-v1.14.yml << 'EOFCONFIG'
version: '2.1'

services:
  one-api:
    image: justsong/one-api:latest
    container_name: one-api
    restart: always
    ports:
      - "3000:3000"
    environment:
      - SQL_DSN=oneapi:123456@tcp(mysql:3306)/one-api
      - REDIS_CONN_STRING=redis://redis:6379
      - SESSION_SECRET=random_string_here
      - TZ=Asia/Shanghai
    depends_on:
      - redis
      - mysql
    volumes:
      - ./data/oneapi:/data

  mysql:
    image: mysql:8.0
    container_name: mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: one-api
      MYSQL_USER: oneapi
      MYSQL_PASSWORD: 123456
    volumes:
      - ./data/mysql:/var/lib/mysql
    ports:
      - "127.0.0.1:3306:3306"

  redis:
    image: redis:7-alpine
    container_name: redis
    restart: always
    volumes:
      - ./data/redis:/data
    ports:
      - "127.0.0.1:6379:6379"
EOFCONFIG

    log_success "已创建: oneapi-basic-v1.14.yml"
}

# 验证配置文件
verify_configs() {
    log_info "验证配置文件兼容性..."
    
    # 测试基础配置
    if docker-compose -f oneapi-basic-v1.14.yml config &> /dev/null; then
        log_success "基础配置验证通过"
    else
        log_error "基础配置验证失败"
        return 1
    fi
    
    return 0
}

# 显示使用说明
show_usage() {
    echo ""
    log_success "🎉 Docker Compose 1.14兼容性修复完成！"
    echo ""
    echo "📋 使用方法："
    echo ""
    echo "方法1: 使用兼容配置文件（推荐）"
    echo "  docker-compose -f oneapi-basic-v1.14.yml up -d"
    echo ""
    echo "方法2: 使用修复后的原配置"
    echo "  cd oneapi-original && docker-compose up -d"
    echo ""
    echo "📊 服务地址："
    echo "  OneAPI管理界面: http://localhost:3000"
    echo ""
    echo "🔧 如需恢复原配置："
    echo "  ./fix-for-v1.14.sh --restore"
}

# 恢复备份
restore_backups() {
    log_info "恢复备份文件..."
    
    for file in oneapi-original/docker-compose.yml deployment/docker-compose.*.yml; do
        backup_file="${file}.v114.backup"
        if [ -f "$backup_file" ]; then
            cp "$backup_file" "$file"
            log_success "已恢复: $file"
        fi
    done
}

# 主函数
main() {
    echo ""
    echo "�� Docker Compose 1.14.0-rc2 兼容性分析："
    echo "  - 最高支持版本: 2.4"
    echo "  - 当前配置使用: 3.2 (不兼容)"
    echo "  - 需要降级到: 2.1"
    echo ""
    
    read -p "是否继续修复？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "取消修复"
        exit 0
    fi
    
    # 执行修复
    fix_all_compose_files
    create_basic_config
    
    # 验证结果
    if verify_configs; then
        show_usage
    else
        log_error "修复验证失败"
        exit 1
    fi
}

# 处理命令行参数
case "${1:-}" in
    --restore)
        restore_backups
        ;;
    *)
        main "$@"
        ;;
esac
