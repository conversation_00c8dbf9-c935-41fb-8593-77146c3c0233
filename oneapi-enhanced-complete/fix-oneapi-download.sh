#!/bin/bash

# OneAPI镜像下载修复脚本

echo "🔧 OneAPI镜像下载修复"
echo "==================="

# 配置镜像加速器
configure_mirror() {
    echo "配置Docker镜像加速器..."
    
    sudo mkdir -p /etc/docker
    sudo tee /etc/docker/daemon.json <<-'EOFMIRROR'
{
  "registry-mirrors": [
    "https://mirror.ccs.tencentyun.com",
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com"
  ]
}
EOFMIRROR
    
    sudo systemctl restart docker
    echo "镜像加速器配置完成"
}

# 尝试多种方法下载OneAPI镜像
download_oneapi() {
    echo "尝试下载OneAPI镜像..."
    
    # 方法1: 直接下载latest
    if docker pull justsong/one-api:latest; then
        echo "✅ 下载成功: justsong/one-api:latest"
        return 0
    fi
    
    # 方法2: 尝试其他用户名
    if docker pull songquanpeng/one-api:latest; then
        echo "✅ 下载成功: songquanpeng/one-api:latest"
        docker tag songquanpeng/one-api:latest justsong/one-api:latest
        return 0
    fi
    
    # 方法3: 尝试GitHub Container Registry
    if docker pull ghcr.io/songquanpeng/one-api:latest; then
        echo "✅ 下载成功: ghcr.io/songquanpeng/one-api:latest"
        docker tag ghcr.io/songquanpeng/one-api:latest justsong/one-api:latest
        return 0
    fi
    
    # 方法4: 尝试特定版本
    for version in v0.6.6 v0.6.5 v0.6.4; do
        if docker pull justsong/one-api:$version; then
            echo "✅ 下载成功: justsong/one-api:$version"
            docker tag justsong/one-api:$version justsong/one-api:latest
            return 0
        fi
    done
    
    echo "❌ 所有方法都失败了"
    return 1
}

# 主函数
main() {
    read -p "是否配置Docker镜像加速器? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        configure_mirror
    fi
    
    download_oneapi
}

main "$@"
