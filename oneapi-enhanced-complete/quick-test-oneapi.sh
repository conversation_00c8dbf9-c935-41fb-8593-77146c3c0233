#!/bin/bash

# OneAPI镜像快速测试脚本

echo "🔍 OneAPI镜像快速诊断测试"
echo "======================="

# 测试Docker基本功能
echo "1. 测试Docker基本功能..."
if docker run --rm hello-world > /dev/null 2>&1; then
    echo "✅ Docker基本功能正常"
else
    echo "❌ Docker基本功能异常"
    exit 1
fi

# 清理测试镜像
docker rmi hello-world > /dev/null 2>&1 || true

echo ""
echo "2. 测试OneAPI镜像源..."

# 定义要测试的镜像
IMAGES=(
    "justsong/one-api:latest"
    "songquanpeng/one-api:latest"
    "ghcr.io/songquanpeng/one-api:latest"
    "justsong/one-api:v0.6.6"
    "justsong/one-api:v0.6.5"
)

for image in "${IMAGES[@]}"; do
    echo ""
    echo "测试: $image"
    
    # 检查镜像是否存在
    if docker manifest inspect "$image" > /dev/null 2>&1; then
        echo "  ✅ 镜像存在"
        
        # 尝试下载
        echo "  🔄 尝试下载..."
        if timeout 60 docker pull "$image" > /dev/null 2>&1; then
            echo "  ✅ 下载成功！"
            
            # 显示镜像信息
            echo "  📊 镜像信息:"
            docker images "$image" --format "    {{.Repository}}:{{.Tag}} - {{.Size}}"
            
            # 如果不是标准名称，重新标记
            if [ "$image" != "justsong/one-api:latest" ]; then
                echo "  🏷️ 重新标记为标准名称..."
                docker tag "$image" "justsong/one-api:latest"
            fi
            
            echo "  🎉 成功！可以使用这个镜像源"
            break
        else
            echo "  ❌ 下载失败"
        fi
    else
        echo "  ❌ 镜像不存在或无法访问"
    fi
done

echo ""
echo "3. 检查最终结果..."
if docker images "justsong/one-api:latest" --format "{{.Repository}}" | grep -q "justsong/one-api"; then
    echo "✅ OneAPI镜像准备就绪"
    echo ""
    echo "📋 可用镜像:"
    docker images "justsong/one-api" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
    
    echo ""
    echo "🚀 现在可以继续部署了！"
    echo "运行: docker-compose up -d"
else
    echo "❌ 没有可用的OneAPI镜像"
    echo ""
    echo "🔧 建议的解决方案:"
    echo ""
    echo "1. 配置Docker镜像加速器:"
    echo "   sudo mkdir -p /etc/docker"
    echo "   sudo tee /etc/docker/daemon.json <<-'EOF'"
    echo "   {"
    echo "     \"registry-mirrors\": ["
    echo "       \"https://mirror.ccs.tencentyun.com\","
    echo "       \"https://docker.mirrors.ustc.edu.cn\""
    echo "     ]"
    echo "   }"
    echo "   EOF"
    echo "   sudo systemctl restart docker"
    echo ""
    echo "2. 或者手动构建镜像:"
    echo "   git clone https://github.com/songquanpeng/one-api.git"
    echo "   cd one-api"
    echo "   docker build -t justsong/one-api:latest ."
    echo ""
    echo "3. 或者使用代理:"
    echo "   export HTTP_PROXY=http://your-proxy:port"
    echo "   export HTTPS_PROXY=http://your-proxy:port"
    echo "   docker pull justsong/one-api:latest"
fi
