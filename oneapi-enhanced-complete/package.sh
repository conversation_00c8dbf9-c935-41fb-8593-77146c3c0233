#!/bin/bash

# OneAPI Enhanced Complete 打包脚本
# 创建可分发的完整部署包

set -e

echo "📦 OneAPI Enhanced Complete 打包脚本"
echo "=================================="

# 定义变量
PACKAGE_NAME="oneapi-enhanced-complete-$(date +%Y%m%d-%H%M%S)"
CURRENT_DIR=$(pwd)

echo "📁 创建打包目录: $PACKAGE_NAME"
mkdir -p "/tmp/$PACKAGE_NAME"

# 复制所有文件
echo "📋 复制项目文件..."
cp -r ./* "/tmp/$PACKAGE_NAME/"

# 清理不需要的文件
echo "🧹 清理临时文件..."
cd "/tmp/$PACKAGE_NAME"

# 清理日志文件
find . -name "*.log" -delete 2>/dev/null || true
find . -name "*.pid" -delete 2>/dev/null || true

# 清理临时文件
find . -name ".DS_Store" -delete 2>/dev/null || true
find . -name "Thumbs.db" -delete 2>/dev/null || true
find . -name "*.tmp" -delete 2>/dev/null || true

# 清理Git文件
rm -rf .git 2>/dev/null || true
rm -f .gitignore 2>/dev/null || true

# 清理IDE文件
rm -rf .vscode 2>/dev/null || true
rm -rf .idea 2>/dev/null || true

# 清理Python缓存
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "*.pyc" -delete 2>/dev/null || true

# 清理Node.js文件
rm -rf node_modules 2>/dev/null || true
rm -f package-lock.json 2>/dev/null || true

# 清理对话记录（保护隐私）
rm -f logging-system/conversation_logs.jsonl 2>/dev/null || true

# 清理数据目录（避免包含敏感数据）
rm -rf oneapi-original/data 2>/dev/null || true

echo "📝 创建版本信息文件..."
cat > VERSION << EOF
OneAPI Enhanced Complete
=======================
Package: $PACKAGE_NAME
Build Date: $(date)
Build Host: $(hostname)
Build User: $(whoami)

Components:
- OneAPI Original: Official OneAPI service
- Logging System: User conversation tracking
- Testing Scripts: Comprehensive test suite
- Deployment Configs: Multiple deployment modes
- Documentation: Complete guides and examples

Features:
- Automatic user identification via database
- Complete conversation tracking and logging
- Real-time web monitoring interface
- Multi-user and multi-API-key support
- Docker-based deployment options
- Comprehensive testing framework
- Production-ready configurations

Directory Structure:
- oneapi-original/: OneAPI service files
- logging-system/: Conversation tracking system
- testing-scripts/: Test scripts collection
- deployment/: Deployment configurations
- docs/: Documentation files
- examples/: Configuration templates

Usage:
1. Extract the package
2. Run ./deploy.sh for one-click deployment
3. Access services at http://localhost:3000 and http://localhost:3001
4. Run tests with scripts in testing-scripts/
5. Clean up with ./cleanup.sh

For detailed instructions, see README.md and docs/
EOF

echo "📋 创建文件清单..."
cat > FILES.txt << EOF
OneAPI Enhanced Complete - File Manifest
========================================

Root Files:
- README.md                    # Main project documentation
- PROJECT_STRUCTURE.md         # Project structure explanation
- DEPLOYMENT.md               # Detailed deployment guide
- deploy.sh                   # One-click deployment script
- cleanup.sh                  # Cleanup script
- package.sh                  # Packaging script
- VERSION                     # Version information
- FILES.txt                   # File manifest (this file)

oneapi-original/:
- docker-compose.yml          # OneAPI Docker configuration
- data/                       # OneAPI data directory (excluded in package)

logging-system/:
- proxy-server.py             # Main proxy service (25KB)
- db_user_resolver.py         # Database user resolver (11KB)
- requirements.txt            # Python dependencies
- conversation_logs.jsonl     # Conversation logs (excluded in package)

testing-scripts/:
- test-multi-users.sh         # Multi-user API key testing
- test-database-integration.sh # Database integration testing
- test-auto-user-detection.sh # Auto user detection testing
- test-final.sh              # Final functionality testing
- test-user-mapping.sh       # User mapping testing
- test-new-apikey.sh         # New API key testing
- test-chat.sh               # Basic chat testing

deployment/:
- docker-compose.integrated.yml  # Integrated deployment config
- docker-compose.production.yml  # Production deployment config
- docker-compose.langfuse.yml    # Langfuse integration config
- start-integrated.sh           # Integrated startup script

docs/:
- FINAL_SOLUTION.md           # Final solution documentation
- production-solution.md      # Production solution guide
- DEPLOYMENT_CHECKLIST.md     # Deployment checklist
- README.md                   # Original project README

examples/:
- .env.example               # Environment variables template
- .env.proxy                 # Proxy service configuration
- user-mapping.json          # User mapping example

Total Files: $(find . -type f | wc -l)
Package Size: $(du -sh . | cut -f1)
EOF

echo "🔧 设置文件权限..."
chmod +x *.sh 2>/dev/null || true
chmod +x testing-scripts/*.sh 2>/dev/null || true
chmod +x deployment/*.sh 2>/dev/null || true

echo "📊 生成统计信息..."
echo ""
echo "📋 打包统计："
echo "  总文件数: $(find . -type f | wc -l)"
echo "  总目录数: $(find . -type d | wc -l)"
echo "  包大小: $(du -sh . | cut -f1)"

echo ""
echo "📂 目录结构："
find . -type d | sort

# 返回临时目录的上级目录
cd /tmp

# 创建压缩包
echo ""
echo "🗜️ 创建压缩包..."
tar -czf "$PACKAGE_NAME.tar.gz" "$PACKAGE_NAME"

# 移动到原目录
mv "$PACKAGE_NAME.tar.gz" "$CURRENT_DIR/"

# 清理临时目录
rm -rf "$PACKAGE_NAME"

echo ""
echo "✅ 打包完成！"
echo ""
echo "📦 压缩包信息："
echo "  文件名: $PACKAGE_NAME.tar.gz"
echo "  位置: $CURRENT_DIR/$PACKAGE_NAME.tar.gz"
echo "  大小: $(du -h "$CURRENT_DIR/$PACKAGE_NAME.tar.gz" | cut -f1)"

echo ""
echo "🚀 部署步骤："
echo "1. 将压缩包传输到目标服务器"
echo "   scp $PACKAGE_NAME.tar.gz user@server:/opt/"
echo ""
echo "2. 在服务器上解压"
echo "   cd /opt"
echo "   tar -xzf $PACKAGE_NAME.tar.gz"
echo "   cd $PACKAGE_NAME"
echo ""
echo "3. 运行一键部署"
echo "   ./deploy.sh"
echo ""
echo "4. 访问服务"
echo "   OneAPI管理: http://server-ip:3000"
echo "   代理监控: http://server-ip:3001"
echo ""
echo "📖 详细说明请查看解压后的 README.md 文件"

echo ""
echo "🎉 打包完成！现在你可以将这个压缩包部署到任何环境了。"
