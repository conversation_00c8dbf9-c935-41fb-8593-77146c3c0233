# 🚀 OneAPI Enhanced Complete - 部署指南

## 📋 项目概述

OneAPI Enhanced Complete 是一个完整的OneAPI增强版系统，提供了用户对话跟踪、实时监控、多用户支持等企业级功能。

### 🌟 核心特性

- ✅ **模块化设计** - 清晰的文件组织结构
- ✅ **自动用户识别** - 通过数据库直连获取用户信息
- ✅ **完整对话记录** - 记录所有聊天请求和响应
- ✅ **实时Web监控** - 友好的监控界面
- ✅ **多种部署模式** - 基础、集成、生产、测试模式
- ✅ **全面测试覆盖** - 完整的测试脚本集合
- ✅ **一键部署** - 自动化部署和清理脚本

## 🛠️ 环境要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上（推荐8GB）
- **磁盘**: 20GB以上可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Linux (Ubuntu 18.04+, CentOS 7+, macOS)
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Python**: 3.8+ (可选，用于代理服务)

## 📦 获取项目

### 方法1: 下载压缩包（推荐）

```bash
# 下载并解压
wget oneapi-enhanced-complete-YYYYMMDD-HHMMSS.tar.gz
tar -xzf oneapi-enhanced-complete-YYYYMMDD-HHMMSS.tar.gz
cd oneapi-enhanced-complete-YYYYMMDD-HHMMSS
```

### 方法2: Git克隆

```bash
git clone https://github.com/your-repo/oneapi-enhanced-complete.git
cd oneapi-enhanced-complete
```

## 🚀 快速部署

### 一键部署（推荐）

```bash
# 运行一键部署脚本
./deploy.sh
```

部署脚本会自动：
1. 检查系统环境
2. 显示部署选项
3. 启动相应服务
4. 验证部署结果

### 部署模式选择

#### 1. 基础模式
- OneAPI原版服务
- 日志记录系统
- 适合：新环境部署

#### 2. 集成模式
- 包含Langfuse的完整系统
- 高级分析功能
- 适合：需要详细分析的环境

#### 3. 生产模式
- 生产环境优化配置
- 性能和安全优化
- 适合：生产环境部署

#### 4. 测试模式
- 启动服务并运行测试
- 验证功能完整性
- 适合：功能验证

#### 5. 自定义模式
- 手动选择组件
- 灵活配置
- 适合：特殊需求

## 🔧 手动部署

### 步骤1: 启动OneAPI原版

```bash
cd oneapi-original
docker-compose up -d
```

### 步骤2: 启动日志记录系统

```bash
cd logging-system
pip3 install -r requirements.txt
python3 proxy-server.py
```

### 步骤3: 运行测试验证

```bash
cd testing-scripts
./test-multi-users.sh
```

## 📊 服务访问

部署完成后，可以通过以下地址访问：

| 服务 | 地址 | 说明 |
|------|------|------|
| OneAPI管理界面 | http://localhost:3000 | OneAPI原生管理界面 |
| 代理服务监控 | http://localhost:3001 | 对话记录监控界面 |
| 代理API地址 | http://localhost:3001/v1/chat/completions | 替代原OneAPI地址 |
| Langfuse界面 | http://localhost:1919 | Langfuse分析界面（集成模式） |

## ⚙️ 配置说明

### 环境变量配置

```bash
# 复制配置模板
cp examples/.env.example .env

# 编辑配置
nano .env
```

主要配置项：
```bash
# OneAPI数据库配置
ONEAPI_DB_HOST=localhost
ONEAPI_DB_PORT=3306
ONEAPI_DB_USER=oneapi
ONEAPI_DB_PASSWORD=123456
ONEAPI_DB_NAME=one-api

# 代理服务配置
PROXY_PORT=3001
LOG_LEVEL=INFO

# 安全配置
SESSION_SECRET=your_random_secret_here
```

### OneAPI初始配置

1. 访问 http://localhost:3000
2. 完成初始化设置
3. 添加LLM提供商渠道
4. 创建用户账号
5. 生成API Key

## 🧪 测试验证

### 自动测试

```bash
cd testing-scripts

# 运行所有测试
./test-multi-users.sh
./test-database-integration.sh
./test-auto-user-detection.sh
```

### 手动测试

```bash
# 发送测试请求
curl -X POST http://localhost:3001/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Hello, this is a test."}
    ]
  }'

# 查看对话记录
curl http://localhost:3001/logs?limit=5

# 查看系统状态
curl http://localhost:3001/api/status
```

## 📈 监控和维护

### 日志查看

```bash
# OneAPI日志
cd oneapi-original
docker-compose logs -f

# 代理服务日志
tail -f logging-system/proxy.log

# 对话记录
tail -f logging-system/conversation_logs.jsonl
```

### 系统状态检查

```bash
# 检查服务状态
curl http://localhost:3001/api/status

# 检查容器状态
docker ps

# 检查端口占用
netstat -tlnp | grep -E ":(3000|3001|1919)"
```

### 数据备份

```bash
# 备份数据库
docker exec mysql mysqldump -u oneapi -p123456 one-api > backup_$(date +%Y%m%d).sql

# 备份对话记录
cp logging-system/conversation_logs.jsonl backup/conversation_logs_$(date +%Y%m%d).jsonl
```

## 🔒 安全配置

### 1. 修改默认密码

```bash
# 修改数据库密码
nano .env  # 修改 ONEAPI_DB_PASSWORD

# 修改OneAPI管理员密码
# 在Web界面中修改
```

### 2. 配置防火墙

```bash
# Ubuntu/Debian
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw enable

# CentOS/RHEL
firewall-cmd --permanent --add-port=22/tcp
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp
firewall-cmd --reload
```

### 3. 配置HTTPS（可选）

1. 获取SSL证书
2. 配置Nginx反向代理
3. 更新服务配置

## 🔄 更新和维护

### 更新系统

```bash
# 停止服务
./cleanup.sh

# 更新代码
git pull  # 或下载新版本

# 重新部署
./deploy.sh
```

### 清理系统

```bash
# 运行清理脚本
./cleanup.sh

# 选择清理模式：
# 1) 快速清理 - 仅停止服务
# 2) 标准清理 - 停止服务 + 清理容器
# 3) 完全清理 - 停止服务 + 清理容器 + 清理日志
# 4) 深度清理 - 完全清理 + 清理数据（谨慎使用）
```

## 🆘 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   lsof -i :3000
   lsof -i :3001
   
   # 停止占用进程
   kill $(lsof -ti:3000)
   ```

2. **Docker服务启动失败**
   ```bash
   # 查看Docker日志
   docker-compose logs
   
   # 重启Docker服务
   sudo systemctl restart docker
   ```

3. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker exec -it mysql mysql -u oneapi -p
   
   # 检查网络连接
   docker network ls
   ```

4. **代理服务无法启动**
   ```bash
   # 检查Python环境
   python3 --version
   pip3 list
   
   # 重新安装依赖
   cd logging-system
   pip3 install -r requirements.txt
   ```

### 诊断工具

```bash
# 系统信息收集
echo "=== System Info ===" > diagnostic.txt
uname -a >> diagnostic.txt
docker --version >> diagnostic.txt
python3 --version >> diagnostic.txt

echo "=== Service Status ===" >> diagnostic.txt
docker ps >> diagnostic.txt
curl -s http://localhost:3001/api/status >> diagnostic.txt

echo "=== Port Status ===" >> diagnostic.txt
netstat -tlnp | grep -E ":(3000|3001)" >> diagnostic.txt
```

## 📞 技术支持

### 获取帮助

1. 查看 `docs/` 目录中的详细文档
2. 运行测试脚本诊断问题
3. 检查日志文件定位错误
4. 参考故障排除指南

### 联系支持

如果遇到无法解决的问题，请提供：
- 系统环境信息
- 错误日志
- 诊断报告
- 具体的错误描述

## ✅ 部署检查清单

部署完成后，请确认以下项目：

- [ ] OneAPI主服务正常运行 (http://localhost:3000)
- [ ] 代理服务正常运行 (http://localhost:3001)
- [ ] 数据库连接正常
- [ ] Web监控界面可访问
- [ ] 用户信息能够正确识别
- [ ] 对话记录正常保存
- [ ] API请求能够正常处理
- [ ] 测试脚本运行成功
- [ ] 防火墙配置正确
- [ ] 备份策略已设置

🎉 **恭喜！OneAPI Enhanced Complete 部署完成！**

现在你拥有了一个功能完整、模块化、易于管理的OneAPI用户对话跟踪系统！
