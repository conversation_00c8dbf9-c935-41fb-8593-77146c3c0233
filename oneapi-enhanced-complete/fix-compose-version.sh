#!/bin/bash

# Docker Compose版本兼容性修复脚本
# 解决老版本Docker Compose的兼容性问题

set -e

echo "🔧 Docker Compose版本兼容性修复"
echo "==============================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker Compose版本
check_compose_version() {
    log_info "检查Docker Compose版本..."
    
    if command -v docker-compose &> /dev/null; then
        COMPOSE_VERSION=$(docker-compose --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
        log_info "Docker Compose版本: $COMPOSE_VERSION"
        
        # 检查版本是否支持3.2格式
        MAJOR=$(echo $COMPOSE_VERSION | cut -d. -f1)
        MINOR=$(echo $COMPOSE_VERSION | cut -d. -f2)
        
        if [ "$MAJOR" -lt 1 ] || ([ "$MAJOR" -eq 1 ] && [ "$MINOR" -lt 18 ]); then
            log_warning "Docker Compose版本较老，建议升级到1.18+版本"
            return 1
        else
            log_success "Docker Compose版本支持3.2格式"
            return 0
        fi
    elif docker compose version &> /dev/null; then
        log_info "使用Docker内置的compose命令"
        return 0
    else
        log_error "未找到Docker Compose"
        return 1
    fi
}

# 修复compose文件版本
fix_compose_files() {
    log_info "修复Docker Compose文件版本..."
    
    # 要修复的文件列表
    FILES=(
        "oneapi-original/docker-compose.yml"
        "deployment/docker-compose.production.yml"
        "deployment/docker-compose.integrated.yml"
        "deployment/docker-compose.langfuse.yml"
    )
    
    for file in "${FILES[@]}"; do
        if [ -f "$file" ]; then
            log_info "修复文件: $file"
            
            # 创建备份
            cp "$file" "${file}.backup"
            
            # 修复版本号
            if grep -q "version: '3\.[4-9]'" "$file"; then
                sed -i.tmp "s/version: '3\.[4-9]'/version: '3.2'/g" "$file"
                rm -f "${file}.tmp"
                log_success "已修复: $file (3.x -> 3.2)"
            elif grep -q "version: '3\.[0-3]'" "$file"; then
                log_success "版本已兼容: $file"
            else
                log_warning "未找到版本号: $file"
            fi
        else
            log_warning "文件不存在: $file"
        fi
    done
}

# 创建兼容版本的compose文件
create_compatible_compose() {
    log_info "创建兼容版本的compose文件..."
    
    # 为老版本Docker Compose创建简化版本
    cat > docker-compose.compatible.yml << 'EOF'
version: '2.1'

services:
  one-api:
    image: justsong/one-api:latest
    container_name: one-api
    restart: always
    ports:
      - "3000:3000"
    environment:
      - SQL_DSN=oneapi:123456@tcp(mysql:3306)/one-api
      - REDIS_CONN_STRING=redis://redis:6379
      - SESSION_SECRET=random_string_here
      - TZ=Asia/Shanghai
    depends_on:
      - redis
      - mysql
    volumes:
      - ./data/oneapi:/data

  mysql:
    image: mysql:8.0
    container_name: mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: one-api
      MYSQL_USER: oneapi
      MYSQL_PASSWORD: 123456
    volumes:
      - ./data/mysql:/var/lib/mysql
    ports:
      - "127.0.0.1:3306:3306"

  redis:
    image: redis:7-alpine
    container_name: redis
    restart: always
    volumes:
      - ./data/redis:/data
    ports:
      - "127.0.0.1:6379:6379"
EOF

    log_success "已创建兼容版本: docker-compose.compatible.yml"
}

# 验证修复结果
verify_fix() {
    log_info "验证修复结果..."
    
    local error_found=false
    
    # 检查所有compose文件
    for file in oneapi-original/docker-compose.yml deployment/docker-compose.*.yml; do
        if [ -f "$file" ]; then
            log_info "验证文件: $file"
            
            # 检查语法
            if command -v docker-compose &> /dev/null; then
                if docker-compose -f "$file" config &> /dev/null; then
                    log_success "语法正确: $file"
                else
                    log_error "语法错误: $file"
                    error_found=true
                fi
            fi
        fi
    done
    
    if [ "$error_found" = false ]; then
        log_success "所有文件验证通过"
        return 0
    else
        log_error "验证发现问题"
        return 1
    fi
}

# 显示解决方案
show_solutions() {
    echo ""
    echo "📋 解决方案选项："
    echo ""
    echo "方案1: 升级Docker Compose（推荐）"
    echo "  # Ubuntu/Debian"
    echo "  sudo curl -L \"https://github.com/docker/compose/releases/latest/download/docker-compose-\$(uname -s)-\$(uname -m)\" -o /usr/local/bin/docker-compose"
    echo "  sudo chmod +x /usr/local/bin/docker-compose"
    echo ""
    echo "  # CentOS/RHEL"
    echo "  sudo curl -L \"https://github.com/docker/compose/releases/latest/download/docker-compose-\$(uname -s)-\$(uname -m)\" -o /usr/local/bin/docker-compose"
    echo "  sudo chmod +x /usr/local/bin/docker-compose"
    echo ""
    echo "方案2: 使用兼容版本文件"
    echo "  docker-compose -f docker-compose.compatible.yml up -d"
    echo ""
    echo "方案3: 使用Docker内置compose"
    echo "  docker compose up -d"
    echo ""
}

# 恢复备份
restore_backup() {
    log_info "恢复备份文件..."
    
    for file in oneapi-original/docker-compose.yml deployment/docker-compose.*.yml; do
        backup_file="${file}.backup"
        if [ -f "$backup_file" ]; then
            cp "$backup_file" "$file"
            log_success "已恢复: $file"
        fi
    done
}

# 清理备份
cleanup_backups() {
    log_info "清理备份文件..."
    
    for file in oneapi-original/docker-compose.yml.backup deployment/docker-compose.*.yml.backup; do
        if [ -f "$file" ]; then
            rm "$file"
            log_success "已删除备份: $file"
        fi
    done
}

# 主函数
main() {
    # 检查是否在正确的目录
    if [ ! -f "README.md" ] || [ ! -d "oneapi-original" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    echo ""
    echo "🔍 问题诊断："
    echo "Docker Compose版本不兼容，需要修复compose文件版本号"
    echo ""
    
    # 检查版本
    if ! check_compose_version; then
        show_solutions
        echo ""
        read -p "是否继续修复compose文件版本？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "取消修复"
            exit 0
        fi
    fi
    
    # 执行修复
    fix_compose_files
    create_compatible_compose
    
    # 验证修复结果
    if verify_fix; then
        echo ""
        log_success "🎉 修复完成！现在可以重新运行 ./deploy.sh"
        
        echo ""
        echo "📋 使用说明："
        echo "1. 标准部署: ./deploy.sh"
        echo "2. 兼容模式: docker-compose -f docker-compose.compatible.yml up -d"
        echo "3. Docker内置: docker compose up -d"
        
        echo ""
        read -p "是否清理备份文件？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            cleanup_backups
        else
            log_info "备份文件已保留，如需恢复可运行: $0 --restore"
        fi
    else
        log_error "修复验证失败"
        echo ""
        read -p "是否恢复备份文件？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            restore_backup
        fi
        exit 1
    fi
}

# 处理命令行参数
case "${1:-}" in
    --restore)
        restore_backup
        ;;
    --cleanup)
        cleanup_backups
        ;;
    --verify)
        verify_fix
        ;;
    *)
        main "$@"
        ;;
esac
