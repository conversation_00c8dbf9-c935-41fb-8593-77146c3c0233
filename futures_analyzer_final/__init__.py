#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货技术分析库 v0.2 - 最终版本

这是一个专门用于期货技术分析的Python库，提供以下功能：

1. 支持多种交易所数据 (SHFE, DCE, ZCE, CFFEX, GFEX)
2. 自动数据清洗和异常处理
3. 全面的技术指标计算
4. 智能分析引擎
5. 详细的分析报告生成

主要类：
- FuturesAnalyzer: 主要分析器，提供简化的API接口

使用示例：
```python
from futures_analyzer_final import FuturesAnalyzer, analyze_futures

# 方法1：使用类
analyzer = FuturesAnalyzer()
result = analyzer.analyze("CFFEX/IF2509")

# 方法2：使用便捷函数
result = analyze_futures("CFFEX/IF2509")

# 检查结果
if result['success']:
    print(f"分析成功，保存的文件: {result['saved_files']}")
else:
    print(f"分析失败: {result['error']}")
```
"""

from .futures_analyzer import FuturesAnalyzer, analyze_futures

__version__ = "0.2.0"
__author__ = "Futures Analysis Team"

__all__ = [
    'FuturesAnalyzer',
    'analyze_futures'
]

# 版本信息
VERSION_INFO = {
    'major': 0,
    'minor': 2,
    'patch': 0,
    'release': 'final'
}

def get_version():
    """获取版本信息"""
    return __version__

def get_supported_exchanges():
    """获取支持的交易所列表"""
    return ['SHFE', 'DCE', 'ZCE', 'CFFEX', 'GFEX']

# 库信息
LIBRARY_INFO = {
    'name': '期货技术分析库',
    'version': __version__,
    'description': '专业的期货技术分析工具库 - 最终版本',
    'features': [
        '多交易所数据支持',
        '自动数据清洗',
        '全面技术指标',
        '智能分析引擎',
        '详细报告生成',
        '简化API接口'
    ],
    'supported_exchanges': get_supported_exchanges(),
    'supported_periods': ['日线', '15分钟线'],
    'output_formats': ['TXT报告文件']
}

def print_library_info():
    """打印库信息"""
    info = LIBRARY_INFO
    print(f"📊 {info['name']} v{info['version']}")
    print(f"📝 {info['description']}")
    print(f"\n✨ 主要功能:")
    for feature in info['features']:
        print(f"  • {feature}")
    print(f"\n🏢 支持的交易所: {', '.join(info['supported_exchanges'])}")
    print(f"📈 支持的周期: {', '.join(info['supported_periods'])}")
    print(f"📄 输出格式: {', '.join(info['output_formats'])}")

def quick_start_guide():
    """快速开始指南"""
    print("🚀 快速开始指南")
    print("="*50)
    print("1. 导入库:")
    print("   from futures_analyzer_final import analyze_futures")
    print()
    print("2. 分析合约:")
    print("   result = analyze_futures('CFFEX/IF2509')")
    print()
    print("3. 检查结果:")
    print("   if result['success']:")
    print("       print('分析成功！')")
    print("       print(f'文件: {result[\"saved_files\"]}')")
    print()
    print("4. 支持的合约格式:")
    print("   - CFFEX/IF2509 (中金所股指期货)")
    print("   - SHFE/ag2508 (上期所白银期货)")
    print("   - DCE/m2509 (大商所豆粕期货)")
    print("   - ZCE/CF509 (郑商所棉花期货)")
    print("   - GFEX/SI2509 (广期所工业硅期货)")
