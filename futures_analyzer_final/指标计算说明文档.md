# Futures Analyzer Final 指标计算说明文档

## 概述
本文档详细说明了futures_analyzer_final期货技术分析引擎中各项指标的计算方式、数值处理方法以及对应的分析结论。

## 1. 趋势指标

### 1.1 移动平均线 (MA/SMA)
**计算方式：**
- MA20 = 20期收盘价的简单移动平均
- MA60 = 60期收盘价的简单移动平均

**数值处理：**
- 价格偏离度 = (当前价格 - MA值) / MA值 × 100%

**分析结论：**
- 当前价格 > MA20 > MA60 → **多头排列**，趋势向好
- 当前价格 < MA20 < MA60 → **空头排列**，趋势偏弱
- 其他情况 → **均线纠缠**，趋势不明确

### 1.2 指数移动平均线 (EMA)
**计算方式：**
- EMA = 前一日EMA × (1-α) + 当日收盘价 × α
- α = 2/(周期+1)

### 1.3 趋势方向判断
**计算原则：**
使用线性回归拟合近期价格点，在拟合度(相关系数)大于0.7的情况下：
- 斜率 > 0 → **上升趋势**
- 斜率 < 0 → **下降趋势**
- 拟合度不够(r < 0.7) → **震荡**

**多周期趋势判断：**
- 短期趋势：5期
- 中期趋势：20期  
- 长期趋势：60期

**多周期一致性：**
- 三个周期trend value都向上向一致 → **上升趋势明确**
- 三个周期trend value都向下向一致 → **下跌趋势明确**
- 三个周期trend value都不一致 → **上升趋势模糊，震荡居多**

**综合趋势方向：**
加权计算：weighted_trend = 短期 × 0.5 + 中期 × 0.3 + 长期 × 0.2
- weighted_trend > 0.3 → **强势上升**
- 0 < weighted_trend ≤ 0.3 → **温和上升**
- weighted_trend < -0.3 → **强势下降**
- -0.3 ≤ weighted_trend < 0 → **温和下降**
- weighted_trend = 0 → **震荡整理**

### 1.4 趋势强度分类
- 强度 > 0.8 → **极强**
- 0.6 < 强度 ≤ 0.8 → **较强**
- 0.4 < 强度 ≤ 0.6 → **中等**
- 0.2 < 强度 ≤ 0.4 → **较弱**
- 强度 ≤ 0.2 → **极弱**

## 2. 动量指标

### 2.1 RSI (相对强弱指标)
**计算方式：**
- RS = 平均上涨幅度 / 平均下跌幅度 (14期)
- RSI = 100 - (100 / (1 + RS))

**分析结论：**
- RSI > 80 → **严重超买**，卖出信号
- 70 < RSI ≤ 80 → **超买**，谨慎信号
- RSI < 20 → **严重超卖**，买入信号
- 20 ≤ RSI < 30 → **超卖**，机会信号
- 30 ≤ RSI ≤ 70 → **正常区间**，中性信号

### 2.2 KDJ指标
**计算方式：**
- RSV = (收盘价 - 最低价) / (最高价 - 最低价) × 100
- K值 = RSV的指数移动平均
- D值 = K值的指数移动平均
- J值 = 3K - 2D

**分析结论：**
- K > 80 且 D > 80 → **超买状态**，关注回调风险
- K < 20 且 D < 20 → **超卖状态**，关注反弹机会
- 前一时刻K < D，当前K > D → **金叉**，买入信号
- 前一时刻K > D，当前K < D → **死叉**，卖出信号

### 2.3 MACD指标
**计算方式：**
- DIF (MACD) = EMA12 - EMA26 (快速线 - 慢速线)
- DEA (Signal) = MACD的9日指数移动平均 (信号线)
- Histogram = MACD - Signal

**交叉信号判断：**
- prev_diff = macd[-2] - signal[-2]  # 前一根K线的 DIF-DEA
- curr_diff = macd[-1] - signal[-1]  # 当前K线的 DIF-DEA
- 前一根 ≤ 0，当前 > 0 → **金叉**，看涨信号
- 前一根 ≥ 0，当前 < 0 → **死叉**，看跌信号

### 2.4 动量指标 (Momentum)
**计算方式：**
- Momentum = 当前收盘价 - N期前收盘价 (默认N=10)
- ROC (变化率) = (当前收盘价 - N期前收盘价) / N期前收盘价 × 100% (默认N=10)
- Williams %R = (最高价 - 收盘价) / (最高价 - 最低价) × (-100) (默认14期)

**动量信号分析：**
- mom_trend = momentum[-1] - momentum[-2]  # 动量变化趋势
- roc_trend = roc[-1] - roc[-2]  # 变化率趋势

**分析结论：**
- mom_trend > 0 且 roc_trend > 0 → **动量增强**，趋势可能延续
- mom_trend < 0 且 roc_trend < 0 → **动量减弱**，趋势可能转变
- 其他情况 → **动量信号混合**，需要观察
- 数据不足 → **动量中性**，无明确信号

**Williams %R解读：**
- Williams %R > -20 → **超买区域**
- Williams %R < -80 → **超卖区域**
- -80 ≤ Williams %R ≤ -20 → **正常区域**

## 3. 波动性指标

### 3.1 ATR (平均真实波幅)
**计算方式：**
- TR = max(高-低, |高-前收|, |低-前收|)
- ATR = TR的14期移动平均

**波动率计算：**
- ATR波动率 = ATR / 当前价格 × 100%

**等级分类：**
- ATR百分位 > 80% → **极高**
- 60% < ATR百分位 ≤ 80% → **较高**
- 40% < ATR百分位 ≤ 60% → **正常**
- 20% < ATR百分位 ≤ 40% → **较低**
- ATR百分位 ≤ 20% → **极低**

### 3.2 布林带 (Bollinger Bands)
**计算方式：**
- 中轨 = 20期简单移动平均
- 上轨 = 中轨 + 2×标准差
- 下轨 = 中轨 - 2×标准差
- 位置 = (当前价格 - 下轨) / (上轨 - 下轨)
- 宽度 = (上轨 - 下轨) / 中轨

**分析结论：**
- 位置 > 0.8 → **接近上轨**，可能回调
- 位置 < 0.2 → **接近下轨**，可能反弹
- 0.2 ≤ 位置 ≤ 0.8 → **位于中轨附近**，震荡状态
- 宽度 > 0.1 → **带宽较宽**，波动性高
- 宽度 < 0.05 → **带宽较窄**，可能突破

### 3.3 历史波动率
**计算方式：**
- 日收益率 = ln(今日收盘价/昨日收盘价)
- 历史波动率 = std(收益率) × √252 (年化)

**波动性等级：**
- 平均波动率 > 0.4 → **极高**
- 0.3 < 平均波动率 ≤ 0.4 → **较高**
- 0.2 < 平均波动率 ≤ 0.3 → **正常**
- 0.1 < 平均波动率 ≤ 0.2 → **较低**
- 平均波动率 ≤ 0.1 → **极低**

## 4. 成交量指标

### 4.1 成交量趋势分析
**计算方式：**
- 成交量比率 = 当前成交量 / 20期成交量移动平均

**分析结论：**
- 比率 > 2.0 → **成交量大幅放大**，市场活跃度高
- 1.5 < 比率 ≤ 2.0 → **成交量放大**，关注趋势延续
- 比率 < 0.5 → **成交量萎缩**，市场观望情绪浓厚
- 0.5 ≤ 比率 ≤ 1.5 → **成交量正常**

### 4.2 OBV (能量潮指标)
**计算方式：**
- 当日收盘价 > 昨日收盘价：OBV = 前日OBV + 今日成交量
- 当日收盘价 < 昨日收盘价：OBV = 前日OBV - 今日成交量
- 当日收盘价 = 昨日收盘价：OBV = 前日OBV

**背离分析：**
- OBV趋势与价格趋势相同 → **OBV配合**，趋势较为可靠
- OBV趋势与价格趋势相反 → **OBV背离**，可能预示趋势转变

### 4.3 价量关系
**判断原则：**
- 价格趋势与成交量趋势方向一致 → **价量配合**，走势良好
- 价格趋势与成交量趋势方向相反 → **价量背离**，需要关注趋势变化

## 5. 支撑压力位

### 5.1 计算原则
如果某个价格水平在过去的价格中多次被触及或接近，说明该水平具有较强的支撑或压力作用。

**具体算法：**
1. 在指定窗口宽度(默认20期)内寻找局部极值
2. 高点作为压力位候选，低点作为支撑位候选
3. 计算强度：在更大范围内(窗口×3)寻找相似价格
4. 与目标价格相差±1%以内，强度+1
5. 筛选强度≥2的位置作为有效支撑压力位

**距离计算：**
- 压力位距离 = (压力位价格 - 当前价格) / 当前价格 × 100%
- 支撑位距离 = (当前价格 - 支撑位价格) / 当前价格 × 100%

**关键位置识别：**
- 距离当前价格5%以内的强支撑/压力位标记为**关键位置**

## 6. 持仓量分析

### 6.1 持仓量趋势
**变化率计算：**
- 变化率 = (当前持仓量 - 前日持仓量) / 前日持仓量 × 100%
- 当变化率绝对值大于3，则认为显著变化
- 当变化率绝对值大于1，则认为有温和变化
- 否则认为有较小变化

**趋势判断：**
- 变化率 > 5% → **显著增长**，市场参与度提高
- 2% < 变化率 ≤ 5% → **温和增长**，市场活跃度上升
- 变化率 < -5% → **显著下降**，市场参与度降低
- -5% ≤ 变化率 < -2% → **温和下降**，市场活跃度减弱
- -2% ≤ 变化率 ≤ 2% → **相对稳定**

### 6.2 价格持仓量关系
**四象限分析：**
- 价格上涨 + 持仓量增加 → **价涨量增**，多头建仓，强烈看涨
- 价格上涨 + 持仓量减少 → **价涨量减**，空头平仓，温和看涨
- 价格下跌 + 持仓量增加 → **价跌量增**，空头建仓，强烈看跌
- 价格下跌 + 持仓量减少 → **价跌量减**，多头平仓，温和看跌

## 7. 综合评估

### 7.1 评分机制
**基础评分：** 50分

**趋势评分调整：**
- 强势上升：+25分
- 温和上升：+15分
- 强势下降：-25分
- 温和下降：-15分

**动量评分调整：**
- 偏多：+10分
- 偏空：-10分

**成交量评分调整：**
- 上升趋势中成交量放大：+5分
- 成交量萎缩：-5分

**反转信号调整：**
- 反转信号数量 > 2：-10分

### 7.2 投资建议
- 评分 ≥ 75 → **强烈看多**
- 60 ≤ 评分 < 75 → **谨慎看多**
- 评分 ≤ 25 → **强烈看空**
- 25 < 评分 ≤ 40 → **谨慎看空**
- 40 < 评分 < 60 → **观望等待**

### 7.3 风险等级
- 波动性为"极高"或"较高" → **较高风险**
- 波动性为"极低" → **较低风险**
- 其他情况 → **中等风险**

## 8. 反转信号识别

### 8.1 信号类型及概率
**高概率信号：**
- MACD金叉/死叉

**中等概率信号：**
- RSI超买/超卖 (>80 或 <20)
- KDJ超买/超卖
- 布林带上轨/下轨触及

**建议行动：**
- 金叉信号 → 考虑做多
- 死叉信号 → 考虑做空
- 超买信号 → 考虑减仓/关注回调
- 超卖信号 → 考虑建仓/关注反弹

---

*本文档基于futures_analyzer_final v0.2版本编写，涵盖了该分析引擎的所有核心指标计算方法和分析逻辑。*
