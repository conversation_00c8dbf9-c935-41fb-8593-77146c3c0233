# 期货技术分析库安装指南

## 📦 安装方式

### 方式1：本地安装（推荐）

```bash
# 1. 下载库文件
git clone <repository-url>
cd futures_analyzer_final

# 2. 安装依赖
pip install -r requirements.txt

# 3. 安装库（开发模式）
pip install -e .
```

### 方式2：直接使用

```bash
# 1. 下载库文件
git clone <repository-url>
cd futures_analyzer_final

# 2. 安装依赖
pip install pandas numpy python-dateutil

# 3. 直接使用（无需安装）
python main.py
```

### 方式3：构建wheel包

```bash
# 1. 安装构建工具
pip install build

# 2. 构建包
python -m build

# 3. 安装生成的wheel包
pip install dist/futures_analyzer_final-0.2.0-py3-none-any.whl
```

## 🚀 快速开始

### 命令行使用

```bash
# 直接运行
python main.py

# 或者安装后使用命令
futures-analyzer
```

### Python库使用

```python
# 方法1：便捷函数
from futures_analyzer import analyze_futures

result = analyze_futures("CFFEX/IF2509")
if result['success']:
    print(f"分析成功！文件: {result['saved_files']}")

# 方法2：类接口
from futures_analyzer import FuturesAnalyzer

analyzer = FuturesAnalyzer()
result = analyzer.analyze("SHFE/ag2508")
```

## 📋 系统要求

- Python 3.7+
- pandas >= 1.3.0
- numpy >= 1.20.0
- python-dateutil >= 2.8.0

## 📁 数据目录配置

默认数据目录：`/Users/<USER>/Downloads/data_index_0704`

自定义数据目录：

```python
from futures_analyzer import FuturesAnalyzer

# 使用自定义数据目录
analyzer = FuturesAnalyzer(
    data_dir="/path/to/your/data",
    output_dir="/path/to/output"
)
```

## 🔧 故障排除

### 常见问题

1. **导入错误**
   ```bash
   # 确保在正确目录
   cd futures_analyzer_final
   python -c "import futures_analyzer; print('导入成功')"
   ```

2. **数据路径错误**
   ```python
   # 检查数据目录是否存在
   import os
   print(os.path.exists("/Users/<USER>/Downloads/data_index_0704"))
   ```

3. **编码问题**
   ```bash
   # 确保使用UTF-8编码
   export PYTHONIOENCODING=utf-8
   ```

## 📞 技术支持

如有问题，请检查：
1. Python版本是否符合要求
2. 依赖包是否正确安装
3. 数据目录路径是否正确
4. 合约代码格式是否正确（交易所/合约代码）
