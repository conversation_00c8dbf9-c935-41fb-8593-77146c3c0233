#!/usr/bin/env python3
"""
技术指标一致性测试脚本
比较TA-Lib和NumPy实现的指标计算结果
"""

import sys
import os
import pandas as pd
import numpy as np
import logging
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from remote_data_loader import RemoteDataLoader
from talib_technical_indicators import TalibTechnicalIndicators
from numpy_technical_indicators import NumpyTechnicalIndicators

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IndicatorConsistencyTester:
    """指标一致性测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.loader = RemoteDataLoader()
        self.talib_calculator = TalibTechnicalIndicators()
        self.numpy_calculator = NumpyTechnicalIndicators()
        self.differences = []
        
    def test_contract(self, exchange, contract):
        """测试指定合约的指标一致性"""
        print(f"\n🔍 测试合约: {exchange}/{contract}")
        print("="*60)
        
        # 加载数据
        print("📊 加载数据...")
        daily_data = self.loader.load_contract_data(exchange, contract, '日线')
        minute_data = self.loader.load_contract_data(exchange, contract, '15分钟线')
        
        if daily_data is None or minute_data is None:
            print(f"❌ 数据加载失败")
            return False
        
        print(f"  ✅ 日线数据: {len(daily_data)} 条")
        print(f"  ✅ 15分钟线数据: {len(minute_data)} 条")
        
        # 计算指标
        print("\n🔧 计算技术指标...")
        
        # TA-Lib计算
        print("  📈 TA-Lib计算中...")
        talib_daily = self.talib_calculator.calculate_all_indicators(daily_data)
        talib_minute = self.talib_calculator.calculate_all_indicators(minute_data)
        
        # NumPy计算
        print("  📊 NumPy计算中...")
        numpy_daily = self.numpy_calculator.calculate_all_indicators(daily_data)
        numpy_minute = self.numpy_calculator.calculate_all_indicators(minute_data)
        
        if not all([talib_daily, talib_minute, numpy_daily, numpy_minute]):
            print(f"❌ 指标计算失败")
            return False
        
        # 比较指标
        print("\n🔍 比较指标一致性...")
        
        # 比较日线指标
        print("  📅 比较日线指标...")
        daily_diff = self._compare_indicators(talib_daily, numpy_daily, f"{exchange}_{contract}_daily")
        
        # 比较分钟线指标
        print("  ⏰ 比较15分钟线指标...")
        minute_diff = self._compare_indicators(talib_minute, numpy_minute, f"{exchange}_{contract}_minute")
        
        # 汇总结果
        total_diff = daily_diff + minute_diff
        if total_diff == 0:
            print("  ✅ 所有指标完全一致")
        else:
            print(f"  ⚠️  发现 {total_diff} 个差异")
        
        return True
    
    def _compare_indicators(self, talib_indicators, numpy_indicators, prefix):
        """比较两组指标"""
        differences = 0
        
        # 比较趋势指标
        differences += self._compare_trend_indicators(
            talib_indicators.get('trend', {}), 
            numpy_indicators.get('trend', {}), 
            prefix
        )
        
        # 比较动量指标
        differences += self._compare_momentum_indicators(
            talib_indicators.get('momentum', {}), 
            numpy_indicators.get('momentum', {}), 
            prefix
        )
        
        # 比较波动性指标
        differences += self._compare_volatility_indicators(
            talib_indicators.get('volatility', {}), 
            numpy_indicators.get('volatility', {}), 
            prefix
        )
        
        # 比较成交量指标
        differences += self._compare_volume_indicators(
            talib_indicators.get('volume', {}), 
            numpy_indicators.get('volume', {}), 
            prefix
        )
        
        return differences
    
    def _compare_trend_indicators(self, talib_trend, numpy_trend, prefix):
        """比较趋势指标"""
        differences = 0
        
        # 比较移动平均线
        for period in [5, 10, 20, 60]:
            talib_sma = talib_trend.get(f'sma_{period}')
            numpy_sma = numpy_trend.get(f'sma_{period}')
            
            if talib_sma is not None and numpy_sma is not None:
                diff = self._compare_arrays(talib_sma, numpy_sma, f"{prefix}_SMA_{period}")
                if diff:
                    differences += 1
        
        # 比较EMA
        for period in [12, 26]:
            talib_ema = talib_trend.get(f'ema_{period}')
            numpy_ema = numpy_trend.get(f'ema_{period}')
            
            if talib_ema is not None and numpy_ema is not None:
                diff = self._compare_arrays(talib_ema, numpy_ema, f"{prefix}_EMA_{period}")
                if diff:
                    differences += 1
        
        # 比较趋势强度
        talib_strength = talib_trend.get('trend_strength')
        numpy_strength = numpy_trend.get('trend_strength')
        
        if talib_strength is not None and numpy_strength is not None:
            if abs(talib_strength - numpy_strength) > 0.01:
                self._log_difference(f"{prefix}_trend_strength", talib_strength, numpy_strength)
                differences += 1
        
        return differences
    
    def _compare_momentum_indicators(self, talib_momentum, numpy_momentum, prefix):
        """比较动量指标"""
        differences = 0
        
        # 比较RSI
        talib_rsi = talib_momentum.get('rsi')
        numpy_rsi = numpy_momentum.get('rsi')
        
        if talib_rsi is not None and numpy_rsi is not None:
            diff = self._compare_arrays(talib_rsi, numpy_rsi, f"{prefix}_RSI")
            if diff:
                differences += 1
        
        # 比较MACD
        talib_macd = talib_momentum.get('macd')
        numpy_macd = numpy_momentum.get('macd')
        
        if talib_macd is not None and numpy_macd is not None:
            diff = self._compare_arrays(talib_macd, numpy_macd, f"{prefix}_MACD")
            if diff:
                differences += 1
        
        # 比较KDJ
        talib_kdj = talib_momentum.get('kdj', {})
        numpy_kdj = numpy_momentum.get('kdj', {})
        
        for key in ['k', 'd', 'j']:
            talib_val = talib_kdj.get(key)
            numpy_val = numpy_kdj.get(key)
            
            if talib_val is not None and numpy_val is not None:
                diff = self._compare_arrays(talib_val, numpy_val, f"{prefix}_KDJ_{key.upper()}")
                if diff:
                    differences += 1
        
        return differences
    
    def _compare_volatility_indicators(self, talib_vol, numpy_vol, prefix):
        """比较波动性指标"""
        differences = 0
        
        # 比较ATR
        talib_atr = talib_vol.get('atr')
        numpy_atr = numpy_vol.get('atr')
        
        if talib_atr is not None and numpy_atr is not None:
            diff = self._compare_arrays(talib_atr, numpy_atr, f"{prefix}_ATR")
            if diff:
                differences += 1
        
        # 比较布林带
        talib_bb = talib_vol.get('bollinger', {})
        numpy_bb = numpy_vol.get('bollinger', {})
        
        for key in ['upper', 'middle', 'lower']:
            talib_val = talib_bb.get(key)
            numpy_val = numpy_bb.get(key)
            
            if talib_val is not None and numpy_val is not None:
                diff = self._compare_arrays(talib_val, numpy_val, f"{prefix}_BB_{key}")
                if diff:
                    differences += 1
        
        # 比较历史波动率
        talib_hv = talib_vol.get('historical_volatility')
        numpy_hv = numpy_vol.get('historical_volatility')
        
        if talib_hv is not None and numpy_hv is not None:
            if abs(talib_hv - numpy_hv) > 0.01:
                self._log_difference(f"{prefix}_historical_volatility", talib_hv, numpy_hv)
                differences += 1
        
        return differences
    
    def _compare_volume_indicators(self, talib_vol, numpy_vol, prefix):
        """比较成交量指标"""
        differences = 0
        
        # 比较OBV
        talib_obv = talib_vol.get('obv')
        numpy_obv = numpy_vol.get('obv')
        
        if talib_obv is not None and numpy_obv is not None:
            diff = self._compare_arrays(talib_obv, numpy_obv, f"{prefix}_OBV")
            if diff:
                differences += 1
        
        return differences
    
    def _compare_arrays(self, arr1, arr2, name, tolerance=1e-6):
        """比较两个数组"""
        try:
            # 转换为numpy数组
            arr1 = np.array(arr1)
            arr2 = np.array(arr2)
            
            # 移除NaN值
            mask1 = ~np.isnan(arr1)
            mask2 = ~np.isnan(arr2)
            common_mask = mask1 & mask2
            
            if not np.any(common_mask):
                return False
            
            arr1_clean = arr1[common_mask]
            arr2_clean = arr2[common_mask]
            
            # 计算差异
            if len(arr1_clean) != len(arr2_clean):
                self._log_difference(name, f"长度不同: {len(arr1_clean)} vs {len(arr2_clean)}", "")
                return True
            
            # 计算相对误差
            max_diff = np.max(np.abs(arr1_clean - arr2_clean))
            mean_val = np.mean(np.abs(arr1_clean))
            relative_error = max_diff / mean_val if mean_val > 0 else max_diff
            
            if relative_error > tolerance:
                self._log_difference(name, f"最大差异: {max_diff:.6f}", f"相对误差: {relative_error:.6f}")
                return True
            
            return False
            
        except Exception as e:
            self._log_difference(name, f"比较失败: {str(e)}", "")
            return True
    
    def _log_difference(self, name, talib_val, numpy_val):
        """记录差异"""
        diff_info = {
            'indicator': name,
            'talib_value': talib_val,
            'numpy_value': numpy_val,
            'timestamp': datetime.now()
        }
        self.differences.append(diff_info)
        print(f"    ⚠️  {name}: TA-Lib={talib_val}, NumPy={numpy_val}")
    
    def save_differences_report(self, filename="indicator_differences.txt"):
        """保存差异报告"""
        if not self.differences:
            print(f"\n✅ 没有发现差异，无需生成报告")
            return
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("技术指标一致性测试差异报告\n")
            f.write("="*60 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总差异数量: {len(self.differences)}\n\n")
            
            for i, diff in enumerate(self.differences, 1):
                f.write(f"{i}. {diff['indicator']}\n")
                f.write(f"   TA-Lib值: {diff['talib_value']}\n")
                f.write(f"   NumPy值: {diff['numpy_value']}\n")
                f.write(f"   时间: {diff['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        print(f"\n📄 差异报告已保存: {filename}")

def main():
    """主函数"""
    print("🧪 技术指标一致性测试")
    print("="*60)
    
    tester = IndicatorConsistencyTester()
    
    # 测试合约列表
    test_contracts = [
        ('CFFEX', 'IF2509'),
        ('GFEX', 'lc2509'),
        ('SHFE', 'au2510')
    ]
    
    success_count = 0
    total_count = len(test_contracts)
    
    for exchange, contract in test_contracts:
        try:
            if tester.test_contract(exchange, contract):
                success_count += 1
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
    
    # 生成总结报告
    print(f"\n📊 测试总结")
    print("="*60)
    print(f"测试合约数量: {total_count}")
    print(f"成功测试数量: {success_count}")
    print(f"发现差异数量: {len(tester.differences)}")
    
    # 保存差异报告
    tester.save_differences_report()
    
    if len(tester.differences) == 0:
        print("\n🎉 所有指标计算完全一致！")
    else:
        print(f"\n⚠️  发现 {len(tester.differences)} 个差异，请查看差异报告")

if __name__ == "__main__":
    main()
