#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货技术分析库使用示例
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def example_basic_usage():
    """基本使用示例"""
    print("📊 基本使用示例")
    print("="*40)
    
    # 方法1：使用便捷函数
    from futures_analyzer import analyze_futures
    
    print("🔍 分析 CFFEX/IF2509...")
    result = analyze_futures("CFFEX/IF2509")
    
    if result['success']:
        print(f"✅ 分析成功！")
        print(f"📊 分析周期: {', '.join(result['analyzed_periods'])}")
        print(f"📁 生成文件: {len(result['saved_files'])} 个")
        for file_path in result['saved_files']:
            print(f"   - {os.path.basename(file_path)}")
    else:
        print(f"❌ 分析失败: {result['error']}")

def example_class_usage():
    """类接口使用示例"""
    print("\n🔧 类接口使用示例")
    print("="*40)
    
    from futures_analyzer import FuturesAnalyzer
    
    # 创建分析器
    analyzer = FuturesAnalyzer()
    
    # 获取可用合约
    symbols = analyzer.get_available_symbols()
    print(f"📋 可用合约: {len(symbols)} 个")
    print(f"   示例: {symbols[:3]}")
    
    # 验证合约
    test_symbol = "SHFE/ag2508"
    is_valid, msg = analyzer.validate_symbol(test_symbol)
    print(f"🔍 验证 {test_symbol}: {'✅ 有效' if is_valid else '❌ 无效'}")
    
    if is_valid:
        # 分析合约
        print(f"🔍 分析 {test_symbol}...")
        result = analyzer.analyze(test_symbol)
        
        if result['success']:
            print(f"✅ 分析成功！")
            print(f"📊 分析周期: {', '.join(result['analyzed_periods'])}")
        else:
            print(f"❌ 分析失败: {result['error']}")

def example_batch_analysis():
    """批量分析示例"""
    print("\n📦 批量分析示例")
    print("="*40)
    
    from futures_analyzer import analyze_futures
    
    # 要分析的合约列表
    symbols = ["CFFEX/IF2509", "SHFE/ag2508", "DCE/a2509"]
    
    print(f"🔍 批量分析 {len(symbols)} 个合约...")
    
    results = []
    for i, symbol in enumerate(symbols, 1):
        print(f"  [{i}/{len(symbols)}] 分析 {symbol}...")
        result = analyze_futures(symbol)
        results.append(result)
        
        if result['success']:
            print(f"    ✅ 成功")
        else:
            print(f"    ❌ 失败: {result['error']}")
    
    # 统计结果
    success_count = sum(1 for r in results if r['success'])
    print(f"\n📈 批量分析完成: {success_count}/{len(symbols)} 成功")

if __name__ == "__main__":
    print("🚀 期货技术分析库使用示例")
    print("="*60)
    
    try:
        example_basic_usage()
        example_class_usage()
        example_batch_analysis()
        
        print("\n🎉 所有示例运行完成！")
        
    except Exception as e:
        print(f"❌ 示例运行出错: {e}")
        import traceback
        traceback.print_exc()
