!pip install nibabel matplotlib


import os
import json
import nibabel as nib
import numpy as np
import matplotlib.pyplot as plt



def load_nifti(nifti_path):
    """
    Load a NIfTI file (.nii or .nii.gz) and return a NumPy array.
    """
    nifti_img = nib.load(nifti_path)
    data = nifti_img.get_fdata(dtype=np.float32)  # returns a float32 NumPy array
    return data

def show_image_and_mask(image_data, label_data, slice_idx=50, channel_idx=0):
    """
    Show a single slice from a 3D or 4D `image_data` with the `label_data` overlaid.
    
    Parameters:
      - image_data: 3D or 4D NumPy array (shape [X, Y, Z] or [X, Y, Z, C])
      - label_data: 3D NumPy array (shape [X, Y, Z]) with integer labels
      - slice_idx: which axial slice (index along Z dimension) to display
      - channel_idx: if image_data is 4D, specify which channel to show
    """
    # If image_data is 4D, select the channel
    if image_data.ndim == 4:
        # Usually nibabel returns shape (X, Y, Z, Channels)
        image_slice = image_data[:, :, slice_idx, channel_idx]
    else:
        # 3D: just slice along z-axis
        image_slice = image_data[:, :, slice_idx]

    label_slice = label_data[:, :, slice_idx]

    plt.figure(figsize=(6, 6))
    plt.title(f"Slice {slice_idx} (Channel {channel_idx})")

    # Show the grayscale image
    plt.imshow(image_slice, cmap='gray', origin='lower')

    # Overlay the label
    # Any non-zero label is shown with a jet colormap at partial transparency
    plt.imshow(label_slice, cmap='jet', alpha=0.3, origin='lower')
    plt.colorbar(label="Label ID")
    plt.axis('off')
    plt.show()


# 1. Path to your MSD task folder
msd_path = "./Task06_Lung"  # <-- Change to your actual path

# 2. Which training case to visualize from the dataset.json
case_index = 0

# 3. Which axial slice index and channel index to display
slice_idx = 30
channel_idx = 0

# -------------------------------------------
# 4. Load dataset.json
dataset_json_path = os.path.join(msd_path, "dataset.json")
if not os.path.exists(dataset_json_path):
    raise FileNotFoundError(f"Could not find dataset.json in {msd_path}")

with open(dataset_json_path, "r") as f:
    dataset_info = json.load(f)

train_cases = dataset_info.get("training", [])
if len(train_cases) == 0:
    raise ValueError("No training cases found in dataset.json!")

if case_index >= len(train_cases):
    raise ValueError(f"case_index {case_index} is out of range "
                     f"(there are {len(train_cases)} total).")

case_info = train_cases[case_index]
image_rel_path = case_info["image"]  # e.g. "./imagesTr/case_0001.nii.gz"
label_rel_path = case_info["label"]  # e.g. "./labelsTr/case_0001.nii.gz"

# Convert relative paths to absolute
image_path = os.path.join(msd_path, image_rel_path)
label_path = os.path.join(msd_path, label_rel_path)

# Load the image and label
image_data = load_nifti(image_path)
label_data = load_nifti(label_path)

# Show the slice
show_image_and_mask(image_data, label_data, slice_idx, channel_idx)


