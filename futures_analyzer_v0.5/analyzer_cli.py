#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货技术分析系统 v0.3 - 命令行版本
使用argparse进行参数解析，结合15分钟K线和日K线生成综合分析报告
"""

import sys
import os
import argparse
import logging
from datetime import datetime
import numpy as np

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from remote_data_loader import RemoteDataLoader
from local_technical_indicators import LocalTechnicalIndicators
from enhanced_analysis_engine import EnhancedAnalysisEngine
from enhanced_report_generator import EnhancedReportGenerator

# 配置日志
logging.basicConfig(
    level=logging.ERROR,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('analysis.log')
    ]
)
logger = logging.getLogger(__name__)

def validate_contract_format(contract_input):
    """验证合约代码格式"""
    if not contract_input or '/' not in contract_input:
        raise argparse.ArgumentTypeError("格式错误：请使用 '交易所/合约代码' 格式，如 CFFEX/IF2509")
    
    parts = contract_input.split('/')
    if len(parts) != 2:
        raise argparse.ArgumentTypeError("格式错误：请使用 '交易所/合约代码' 格式")
    
    exchange, contract = parts[0].strip().upper(), parts[1].strip()
    
    # 检查交易所是否支持
    supported_exchanges = ['SHFE', 'DCE', 'ZCE', 'CFFEX', 'GFEX']
    if exchange not in supported_exchanges:
        raise argparse.ArgumentTypeError(f"不支持的交易所: {exchange}，支持的交易所: {', '.join(supported_exchanges)}")
    
    return f"{exchange}/{contract}"

def check_data_availability(exchange, contract, loader):
    """检查数据可用性"""
    try:
        # 检查网络连接
        if not loader.test_connection():
            return False, "无法连接到数据服务器，请检查网络连接"

        # 检查交易所是否支持
        available_exchanges = loader.list_available_exchanges()
        if exchange not in available_exchanges:
            return False, f"交易所 {exchange} 不在支持列表中，支持的交易所: {', '.join(available_exchanges)}"

        # 检查必需的数据周期是否支持
        periods = loader.get_available_periods(exchange, contract)
        required_periods = ['15分钟线', '日线']
        missing_periods = [p for p in required_periods if p not in periods]

        if missing_periods:
            return False, f"缺少必需的数据周期: {', '.join(missing_periods)}"

        return True, ""

    except Exception as e:
        return False, f"数据检查失败: {str(e)}"

def load_dual_period_data(exchange, contract, loader):
    """加载15分钟和日线数据"""
    try:
        print("📊 加载数据...")
        
        # 加载15分钟数据
        minute_data = loader.load_contract_data(exchange, contract, '15分钟线')
        if minute_data is None or len(minute_data) < 100:
            raise ValueError("15分钟线数据不足（需要至少100条记录）")
        
        print(f"  ✅ 15分钟线数据: {len(minute_data)} 条记录")
        
        # 加载日线数据
        daily_data = loader.load_contract_data(exchange, contract, '日线')
        if daily_data is None or len(daily_data) < 60:
            raise ValueError("日线数据不足（需要至少60条记录）")
        
        print(f"  ✅ 日线数据: {len(daily_data)} 条记录")
        
        return minute_data, daily_data
        
    except Exception as e:
        raise Exception(f"数据加载失败: {str(e)}")

def calculate_indicators_for_both_periods(minute_data, daily_data, calculator):
    """计算两个周期的技术指标"""
    try:
        print("🔧 计算技术指标...")
        
        # 计算15分钟指标
        minute_indicators = calculator.calculate_all_indicators(minute_data)
        if minute_indicators is None:
            raise ValueError("15分钟线技术指标计算失败")
        
        print("  ✅ 15分钟线指标计算完成")
        
        # 计算日线指标
        daily_indicators = calculator.calculate_all_indicators(daily_data)
        if daily_indicators is None:
            raise ValueError("日线技术指标计算失败")
        
        print("  ✅ 日线指标计算完成")
        
        return minute_indicators, daily_indicators
        
    except Exception as e:
        raise Exception(f"指标计算失败: {str(e)}")

def generate_comprehensive_analysis(exchange, contract, minute_data, daily_data, 
                                  minute_indicators, daily_indicators, engine):
    """生成综合分析报告"""
    try:
        print("📝 生成综合分析...")
        
        # 使用增强分析引擎生成综合分析
        analysis = engine.generate_comprehensive_analysis(
            exchange=exchange,
            contract=contract,
            minute_data=minute_data,
            daily_data=daily_data,
            minute_indicators=minute_indicators,
            daily_indicators=daily_indicators
        )
        
        print("  ✅ 综合分析生成完成")
        return analysis
        
    except Exception as e:
        raise Exception(f"分析生成失败: {str(e)}")

def save_comprehensive_report(analysis, report_generator, output_dir="analysis_results"):
    """保存综合报告"""
    try:
        print("💾 保存分析报告...")
        
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 生成文件名
        timestamp = analysis['timestamp'].strftime("%Y%m%d_%H%M%S")
        filename = f"comprehensive_analysis_{analysis['exchange']}_{analysis['contract']}_{timestamp}.txt"
        
        # 保存报告
        file_path = report_generator.save_comprehensive_report(analysis, filename)
        
        print(f"  ✅ 报告已保存: {file_path}")
        return file_path
        
    except Exception as e:
        raise Exception(f"报告保存失败: {str(e)}")

def main():
    """主程序"""
    parser = argparse.ArgumentParser(
        description='期货技术分析系统 v0.3 - 综合分析版本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python analyzer_cli.py CFFEX/IF2509
  python analyzer_cli.py DCE/a2509
  python analyzer_cli.py SHFE/ag2508
        """
    )
    
    parser.add_argument(
        'contract',
        type=validate_contract_format,
        help='合约代码，格式: 交易所/合约代码 (如: CFFEX/IF2509)'
    )
    
    parser.add_argument(
        '--output-dir',
        default='analysis_results',
        help='输出目录 (默认: analysis_results)'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='显示详细输出'
    )


    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.INFO)
    
    print("🚀 期货技术分析系统 v0.5")
    print("🌐 基于远程数据API的综合技术分析")
    print("="*60)
    
    try:
        # 解析合约代码
        exchange, contract = args.contract.split('/')
        
        print(f"📈 分析合约: {args.contract}")
        print(f"   交易所: {exchange}")
        print(f"   合约代码: {contract}")
        
        # 初始化组件
        print("🔗 初始化远程数据连接...")
        loader = RemoteDataLoader()

        # 初始化技术指标计算器
        print("🔧 初始化技术指标计算器...")
        calculator = LocalTechnicalIndicators()

        engine = EnhancedAnalysisEngine()
        report_generator = EnhancedReportGenerator(args.output_dir)
        
        # 检查数据可用性
        is_available, error_msg = check_data_availability(exchange, contract, loader)
        if not is_available:
            print(f"❌ {error_msg}")
            sys.exit(1)
        
        # 加载数据
        minute_data, daily_data = load_dual_period_data(exchange, contract, loader)
        
        # 计算指标
        minute_indicators, daily_indicators = calculate_indicators_for_both_periods(
            minute_data, daily_data, calculator
        )
        
        # 生成综合分析
        analysis = generate_comprehensive_analysis(
            exchange, contract, minute_data, daily_data,
            minute_indicators, daily_indicators, engine
        )
        
        # 保存报告
        file_path = save_comprehensive_report(analysis, report_generator, args.output_dir)
        
        print("\n✅ 分析完成！")
        print(f"📁 报告文件: {file_path}")
        
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 分析失败: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
