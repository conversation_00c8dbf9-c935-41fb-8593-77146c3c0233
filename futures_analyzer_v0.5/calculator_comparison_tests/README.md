# 技术指标计算器比较测试

本文件夹包含了不同技术指标计算方式的比较测试工具和结果。

## 📁 文件说明

### 计算器实现
- `talib_technical_indicators.py` - 基于TA-Lib库的技术指标计算器
- `numpy_technical_indicators.py` - 基于NumPy/SciPy的自主实现计算器

### 测试工具
- `test_indicators_consistency.py` - 指标一致性测试脚本
- `compare_reports.py` - 报告比较工具

### 测试结果
- `report_comparison.txt` - 详细差异报告
- `implementation_summary.md` - 完整实现总结

## 🚀 使用方法

### 运行一致性测试
```bash
python test_indicators_consistency.py
```

### 比较报告差异
```bash
python compare_reports.py
```

## 📊 测试结果摘要

### 指标计算一致性
- ✅ 核心指标（趋势、动量、成交量）完全一致
- ⚠️ 波动性指标存在细微差异

### 主要差异
1. **波动率百分位**: Local(76.3%) vs TA-Lib/NumPy(36.2%) - 差异52.56%
2. **ATR点数**: 差异约0.57%

### 差异原因
- 不同的历史波动率计算窗口
- ATR计算中的舍入差异
- 百分位计算方法不同

## 🎯 结论

三种计算方式在核心交易信号方面保持高度一致，细微差异主要集中在波动性评估上，不影响主要的交易决策。用户可以根据需要选择最适合的计算引擎。
