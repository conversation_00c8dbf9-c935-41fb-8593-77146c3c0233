# 期货技术分析系统 - 版本更新日志

## v0.5 (2025-07-24) - 格式优化版本

### 🎯 主要改进

#### 📊 报告格式优化
- **✅ 列对齐修复**: 解决了波动性分析中长字段名导致的对齐问题
  - 使用固定宽度格式化 (`{field:<16}`)
  - 确保所有数值列正确对齐
  
- **✅ 支撑压力位表格优化**: 修复了价格列对齐问题
  - 统一表格列宽度
  - 修复"压力位"和"支撑位"下方价格对齐问题
  
- **✅ 综合评估整合**: 解决重复突破信号问题
  - 将支撑压力位相关信号合并到风险提示中
  - 移除重复的"突破信号"标识
  - 保持信息完整性的同时避免冗余

#### 🔧 代码结构优化
- **✅ 核心功能聚焦**: 移除多计算器选择功能
  - 专注于核心分析功能
  - 简化用户使用流程
  - 提高系统稳定性

- **✅ 最小可执行版本**: 只包含运行必需的核心文件
  - `analyzer_cli.py` - 主程序
  - `enhanced_analysis_engine.py` - 分析引擎
  - `enhanced_report_generator.py` - 报告生成器
  - `remote_data_loader.py` - 数据加载器
  - `local_technical_indicators.py` - 指标计算器

- **✅ 测试功能分离**: 将计算器比较测试移至独立文件夹
  - `calculator_comparison_tests/` 文件夹
  - 包含完整的测试工具和文档
  - 独立的README说明

### 📋 格式对比

#### 修复前 (v0.4)
```
波动性等级	较低		当前波动率处于历史中高位(76.3%)
历史波动率	0.2560	
近14日平均ATR波动率	0.0099	
```

#### 修复后 (v0.5)
```
波动性等级           	较低      	当前波动率处于历史中高位(76.3%)
历史波动率           	0.2560
近14日平均ATR波动率    	0.0099
```

#### 支撑压力位修复前
```
压力位	3931.40		+0.3%		43	接近关键压力
	4132.00		+5.5%		22	最强压力位
```

#### 支撑压力位修复后
```
压力位     	3931.40		+0.3%		43	接近关键压力
        	4132.00		+5.5%		22	最强压力位
```

### 🗂️ 文件结构

```
futures_analyzer_v0.5/
├── README.md                    # 项目说明文档
├── VERSION_CHANGELOG.md         # 版本更新日志
├── analyzer_cli.py              # 主程序入口
├── enhanced_analysis_engine.py  # 增强分析引擎
├── enhanced_report_generator.py # 报告生成器 (格式优化)
├── remote_data_loader.py        # 远程数据加载器
├── local_technical_indicators.py # 技术指标计算器
├── test_requests.py             # 数据请求测试
├── analysis_results/            # 分析报告输出目录
└── calculator_comparison_tests/ # 计算器比较测试
    ├── README.md                # 测试说明文档
    ├── implementation_summary.md # 详细实现总结
    ├── talib_technical_indicators.py
    ├── numpy_technical_indicators.py
    ├── test_indicators_consistency.py
    ├── compare_reports.py
    └── report_comparison.txt
```

### 🚀 使用方式

```bash
# 基本使用
python analyzer_cli.py CFFEX/IF2509

# 详细输出
python analyzer_cli.py CFFEX/IF2509 --verbose

# 自定义输出目录
python analyzer_cli.py CFFEX/IF2509 --output-dir my_reports
```

### 🧪 测试功能

```bash
# 进入测试目录
cd calculator_comparison_tests/

# 运行指标一致性测试
python test_indicators_consistency.py

# 运行报告比较测试
python compare_reports.py
```

### ⚠️ 已知问题修复

1. **✅ 列对齐问题**: 长字段名导致的显示错乱已修复
2. **✅ 重复信息**: 综合评估中的重复突破信号已合并
3. **✅ 表格格式**: 支撑压力位表格对齐问题已解决

### 🔄 向后兼容性

- ✅ 完全兼容v0.4的数据格式
- ✅ 保持所有分析功能不变
- ✅ 报告内容完整性保持一致
- ✅ API接口保持不变

### 📈 性能优化

- ✅ 移除未使用的计算器选择逻辑
- ✅ 简化导入依赖
- ✅ 优化报告生成效率

---

## v0.4 (2025-07-24) - 多计算器支持版本

### 主要特性
- 集成TA-Lib专业技术分析库
- 实现NumPy/SciPy自主计算引擎
- 提供三种计算方式选择
- 完整的一致性测试框架

### 发现的问题
- 报告格式对齐问题
- 重复的突破信号显示
- 代码复杂度较高

---

## v0.3 (2025-07-23) - 远程数据版本

### 主要特性
- 基于HTTP API的远程数据获取
- 自动数据清洗和验证
- 支持多交易所数据源

---

## v0.2 - 本地数据版本

### 主要特性
- 基于本地文件的数据分析
- 完整的技术指标计算
- 基础报告生成功能

---

## v0.1 - 初始版本

### 主要特性
- 基础技术分析功能
- 简单的报告输出
- 核心指标计算
