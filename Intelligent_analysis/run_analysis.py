#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货分析系统主入口脚本
根据命令行参数选择运行不同的分析模式
支持命令行参数传递
"""

import sys
import os
import argparse
from pathlib import Path

def main():
    # 检查是否有新的输入格式 (如: 2#SHFE/ag2501)
    if len(sys.argv) > 1 and '#' in sys.argv[1]:
        # 新格式: mode#symbol
        input_arg = sys.argv[1]
        try:
            mode_part, symbol_part = input_arg.split('#', 1)
            mode = int(mode_part)
            symbol = symbol_part
            
            if mode not in [1, 2]:
                print(f"❌ 错误: 不支持的模式 {mode}，支持的模式: 1, 2")
                sys.exit(1)
            
            # 创建简化的参数对象
            class SimpleArgs:
                def __init__(self, mode, symbol):
                    self.mode = mode
                    self.symbol = symbol
                    self.symbol_alt = None
                    self.exchange = None
                    self.contract = None
                    self.output_dir = 'output_1'
                    self.verbose = False
                    self.length = 20  # 模式2默认使用20
            
            args = SimpleArgs(mode, symbol)
            print(f"📝 检测到新格式输入: 模式{mode}, 合约{symbol}")
            
        except ValueError:
            print("❌ 错误: 输入格式错误，正确格式: mode#symbol (如: 2#SHFE/ag2501)")
            sys.exit(1)
    else:
        # 原有的命令行参数解析
        parser = argparse.ArgumentParser(
            description='期货分析系统 - 支持多种分析模式',
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
使用示例:
  新格式: python run_analysis.py 2#SHFE/ag2501              # 模式2分析SHFE/ag2501
  新格式: python run_analysis.py 1#CFFEX/IF2509             # 模式1分析CFFEX/IF2509
  原格式: python run_analysis.py --mode 1 CFFEX/IF2509      # 运行模式1 (技术指标分析)
  原格式: python run_analysis.py --mode 2 DCE/a2509         # 运行模式2 (相似性分析)
  原格式: python run_analysis.py -m 1 --symbol SHFE/ag2508  # 指定合约代码
  python run_analysis.py --help                              # 显示帮助信息
            """
        )
        
        parser.add_argument(
            '-m', '--mode',
            type=int,
            choices=[1, 2],
            required=True,
            help='选择运行模式: 1=技术指标分析, 2=相似性分析'
        )
        
        # 添加合约代码相关参数
        parser.add_argument(
            'symbol', 
            nargs='?', 
            help='合约代码 (格式: 交易所/合约代码，如 CFFEX/IF2509)'
        )
        parser.add_argument(
            '--symbol', 
            dest='symbol_alt',
            help='合约代码 (格式: 交易所/合约代码，如 CFFEX/IF2509)'
        )
        parser.add_argument(
            '--exchange', 
            help='交易所代码 (SHFE/DCE/ZCE/CFFEX/GFEX)'
        )
        parser.add_argument(
            '--contract', 
            help='合约代码 (如 IF2509, a2509)'
        )
        parser.add_argument(
            '--output-dir', 
            default='output_1',
            help='输出目录 (默认: output_1)'
        )
        parser.add_argument(
            '--verbose', '-v',
            action='store_true',
            help='显示详细输出'
        )
        
        # 添加相似性分析专用参数
        parser.add_argument(
            '--length',
            type=int,
            default=20,
            help='K线长度(天数，仅模式2使用，默认20)'
        )
        
        # 解析命令行参数
        args = parser.parse_args()
    
    # 检查主函数文件是否存在
    main_files = {
        1: 'analyze_1.py',  # 技术指标分析
        2: 'analyze_2.py'   # 相似性分析
    }
    
    main_file = main_files[args.mode]
    
    if not os.path.exists(main_file):
        print(f"❌ 错误: 找不到文件 {main_file}")
        print(f"请确保 {main_file} 文件存在于当前目录中")
        sys.exit(1)
    
    # 显示模式信息
    mode_descriptions = {
        1: "技术指标分析模式 - 计算各种技术指标并生成分析报告",
        2: "相似性分析模式 - 分析期货合约的K线相似性"
    }
    
    print("=" * 60)
    print(f"🚀 启动期货分析系统 - 模式 {args.mode}")
    print(f"📋 {mode_descriptions[args.mode]}")
    print(f"📁 运行文件: {main_file}")
    print("=" * 60)
    
    try:
        # 构建传递给子程序的参数
        subprocess_args = []
        
        # 添加合约代码参数
        symbol = get_symbol_from_args(args)
        if symbol:
            if args.mode == 1:
                # 模式1：技术指标分析，直接传递symbol
                subprocess_args.append(symbol)
            else:
                # 模式2：相似性分析，使用--symbol参数
                subprocess_args.extend(['--symbol', symbol])
        
        # 添加其他可选参数
        if args.output_dir != 'output_1':  # 如果不是默认值
            subprocess_args.extend(['--output-dir', args.output_dir])
        
        if args.verbose:
            subprocess_args.append('--verbose')
        
        # 模式2专用参数 - 不传递length参数，使用analyze_2.py的默认值20
        # if args.mode == 2:  # 注释掉，不传递length参数
        #     subprocess_args.extend(['--length', str(args.length)])
        
        # 根据模式运行对应的主函数
        if args.mode == 1:
            # 导入并运行analyze_1 (技术指标分析)
            print("🔄 导入技术指标分析模块...")
            from analyze_1 import main as analyze_1_func
            # 模拟命令行参数传递
            original_argv = sys.argv
            sys.argv = [main_file] + subprocess_args
            try:
                analyze_1_func()
            finally:
                sys.argv = original_argv
            
        elif args.mode == 2:
            # 导入并运行analyze_2 (相似性分析)
            print("🔄 导入相似性分析模块...")
            from analyze_2 import main as analyze_2_func
            # 模拟命令行参数传递
            original_argv = sys.argv
            sys.argv = [main_file] + subprocess_args
            try:
                analyze_2_func()
            finally:
                sys.argv = original_argv
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请检查相关模块文件是否存在")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  用户中断程序")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    print("\n✅ 分析完成!")

def get_symbol_from_args(args):
    """从命令行参数中获取合约代码"""
    symbol = None
    
    # 方式1: 直接位置参数
    if args.symbol:
        symbol = args.symbol
    
    # 方式2: --symbol 参数
    elif args.symbol_alt:
        symbol = args.symbol_alt
    
    # 方式3: 分别指定交易所和合约
    elif args.exchange and args.contract:
        symbol = f"{args.exchange.upper()}/{args.contract}"
    
    return symbol

if __name__ == "__main__":
    main() 