"""
基于本地数据的技术指标计算模块
只使用OHLCV数据计算可用的技术指标
"""

import pandas as pd
import numpy as np
try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    print("警告: talib库未安装，将使用简化的技术指标计算")

try:
    from scipy import stats
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    print("警告: scipy库未安装，将使用简化的统计计算")

import warnings
warnings.filterwarnings('ignore')

class LocalTechnicalIndicators:
    """基于本地数据的技术指标计算器"""
    
    def __init__(self):
        """初始化技术指标计算器"""
        pass
    
    def calculate_ma(self, data, period=20):
        """计算移动平均线"""
        if TALIB_AVAILABLE:
            close_values = data['close'].values.astype(np.float64)
            return talib.SMA(close_values, timeperiod=period)
        else:
            return data['close'].rolling(window=period).mean().values

    # 添加别名方法
    def calculate_sma(self, data, period=20):
        """计算简单移动平均线（别名）"""
        return self.calculate_ma(data, period)
    
    def calculate_ema(self, data, period=20):
        """计算指数移动平均线"""
        if TALIB_AVAILABLE:
            close_values = data['close'].values.astype(np.float64)
            return talib.EMA(close_values, timeperiod=period)
        else:
            return data['close'].ewm(span=period).mean().values
    
    def calculate_bollinger_bands(self, data, period=20, std_dev=2):
        """计算布林带"""
        if TALIB_AVAILABLE:
            close_values = data['close'].values.astype(np.float64)
            upper, middle, lower = talib.BBANDS(
                close_values,
                timeperiod=period,
                nbdevup=std_dev,
                nbdevdn=std_dev
            )
        else:
            # 简化的布林带计算
            close = data['close']
            middle = close.rolling(window=period).mean().values
            std = close.rolling(window=period).std().values
            upper = middle + (std * std_dev)
            lower = middle - (std * std_dev)

        return {
            'upper': upper,
            'middle': middle,
            'lower': lower,
            'width': (upper - lower) / middle,
            'position': (data['close'].values - lower) / (upper - lower)
        }
    
    def calculate_rsi(self, data, period=14):
        """计算RSI指标"""
        if TALIB_AVAILABLE:
            close_values = data['close'].values.astype(np.float64)
            return talib.RSI(close_values, timeperiod=period)
        else:
            # 简化的RSI计算
            close = data['close']
            delta = close.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi.values
    
    def calculate_macd(self, data, fast=12, slow=26, signal=9):
        """计算MACD指标"""
        if TALIB_AVAILABLE:
            close_values = data['close'].values.astype(np.float64)
            macd, signal_line, histogram = talib.MACD(
                close_values,
                fastperiod=fast,
                slowperiod=slow,
                signalperiod=signal
            )
        else:
            # 简化的MACD计算
            import pandas as pd
            close = data['close']
            ema_fast = close.ewm(span=fast).mean()
            ema_slow = close.ewm(span=slow).mean()
            macd = (ema_fast - ema_slow).values
            signal_line = pd.Series(macd).ewm(span=signal).mean().values
            histogram = macd - signal_line

        return {
            'macd': macd,
            'signal': signal_line,
            'histogram': histogram,
            'cross_signal': self._detect_macd_cross(macd, signal_line)
        }
    
    def calculate_kdj(self, data, k_period=9, d_period=3, j_period=3):
        """计算KDJ指标"""
        if TALIB_AVAILABLE:
            high = data['high'].values.astype(np.float64)
            low = data['low'].values.astype(np.float64)
            close = data['close'].values.astype(np.float64)

            # 计算K值和D值
            k_values, d_values = talib.STOCH(high, low, close,
                                  fastk_period=k_period,
                                  slowk_period=d_period,
                                  slowd_period=j_period)
        else:
            # 简化的KDJ计算
            import pandas as pd
            high = data['high']
            low = data['low']
            close = data['close']

            lowest_low = low.rolling(window=k_period).min()
            highest_high = high.rolling(window=k_period).max()

            rsv = 100 * (close - lowest_low) / (highest_high - lowest_low)
            k_values = rsv.ewm(alpha=1/d_period).mean().values
            d_values = pd.Series(k_values).ewm(alpha=1/j_period).mean().values

        # 计算J值
        j_values = 3 * k_values - 2 * d_values

        return {
            'k': k_values,
            'd': d_values,
            'j': j_values,
            'signal': self._analyze_kdj_signal(k_values, d_values, j_values)
        }
    
    def calculate_atr(self, data, period=14):
        """计算平均真实波幅"""
        if TALIB_AVAILABLE:
            high = data['high'].values.astype(np.float64)
            low = data['low'].values.astype(np.float64)
            close = data['close'].values.astype(np.float64)
            return talib.ATR(high, low, close, timeperiod=period)
        else:
            # 简化的ATR计算
            import pandas as pd
            high = data['high']
            low = data['low']
            close = data['close']
            prev_close = close.shift(1)

            tr1 = high - low
            tr2 = abs(high - prev_close)
            tr3 = abs(low - prev_close)

            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            atr = tr.rolling(window=period).mean()
            return atr.values
    
    def calculate_volume_indicators(self, data):
        """计算成交量相关指标"""
        volume = data['volume'].values
        close = data['close'].values
        
        # OBV (On Balance Volume)
        obv = talib.OBV(close, volume)
        
        # 成交量移动平均
        volume_ma = talib.SMA(volume, timeperiod=20)
        
        # 成交量比率
        volume_ratio = volume / volume_ma
        
        # 价量背离检测
        price_trend = self._calculate_trend(close, 10)
        volume_trend = self._calculate_trend(volume, 10)
        divergence = (price_trend * volume_trend) < 0  # 这是一个布尔值
        
        return {
            'obv': obv,
            'volume_ma': volume_ma,
            'volume_ratio': volume_ratio,
            'price_volume_divergence': divergence,
            'volume_surge': bool((volume_ratio > 2.0).any()) if hasattr(volume_ratio, 'any') else volume_ratio > 2.0  # 成交量放大
        }
    
    def calculate_support_resistance(self, data, window=20, min_touches=2):
        """计算支撑压力位"""
        high_prices = data['high'].values
        low_prices = data['low'].values
        close_prices = data['close'].values
        
        # 寻找局部极值
        resistance_levels = []
        support_levels = []
        
        for i in range(window, len(high_prices) - window):
            # 检查是否为局部最高点
            if high_prices[i] == max(high_prices[i-window:i+window+1]):
                resistance_levels.append({
                    'price': high_prices[i],
                    'index': i,
                    'strength': self._calculate_level_strength(high_prices, i, window)
                })
            
            # 检查是否为局部最低点
            if low_prices[i] == min(low_prices[i-window:i+window+1]):
                support_levels.append({
                    'price': low_prices[i],
                    'index': i,
                    'strength': self._calculate_level_strength(low_prices, i, window)
                })
        
        # 筛选强度较高的支撑压力位
        current_price = close_prices[-1]
        resistance = [r for r in resistance_levels if r['strength'] >= min_touches and r['price'] > current_price]
        support = [s for s in support_levels if s['strength'] >= min_touches and s['price'] < current_price]
        
        # 按距离当前价格排序
        resistance.sort(key=lambda x: x['price'])
        support.sort(key=lambda x: x['price'], reverse=True)
        
        return {
            'resistance': resistance[:5],  # 最近的5个压力位
            'support': support[:5],        # 最近的5个支撑位
            'current_price': current_price
        }
    
    def calculate_trend_indicators(self, data):
        """计算趋势指标"""
        close = data['close'].values
        
        # 趋势强度
        trend_strength = self._calculate_trend_strength(close, 20)
        
        # 趋势方向
        short_trend = self._calculate_trend(close, 5)
        medium_trend = self._calculate_trend(close, 20)
        long_trend = self._calculate_trend(close, 60)
        
        # 趋势一致性
        trend_consistency = self._analyze_trend_consistency(short_trend, medium_trend, long_trend)
        
        return {
            'trend_strength': trend_strength,
            'short_trend': short_trend,
            'medium_trend': medium_trend,
            'long_trend': long_trend,
            'trend_consistency': trend_consistency,
            'overall_direction': self._determine_overall_direction(short_trend, medium_trend, long_trend)
        }
    
    def calculate_volatility_indicators(self, data):
        """计算波动性指标"""
        close = data['close'].values
        high = data['high'].values
        low = data['low'].values
        
        # 历史波动率
        returns = np.diff(np.log(close))
        volatility = np.std(returns) * np.sqrt(252)  # 年化波动率
        
        # ATR波动率
        atr = self.calculate_atr(data)
        atr_volatility = atr[-1] / close[-1] if len(atr) > 0 and close[-1] > 0 else 0
        
        # 价格波动幅度
        price_range = (high - low) / close
        avg_range = np.mean(price_range[-20:])  # 最近20期平均波动幅度
        
        # 波动率等级
        volatility_level = self._classify_volatility(volatility, atr_volatility)
        
        return {
            'historical_volatility': volatility,
            'atr_volatility': atr_volatility,
            'average_range': avg_range,
            'volatility_level': volatility_level,
            'volatility_percentile': self._calculate_volatility_percentile(volatility, returns)
        }
    
    def calculate_momentum_indicators(self, data):
        """计算动量指标"""
        close = data['close'].values
        
        # 动量指标
        momentum = talib.MOM(close, timeperiod=10)
        
        # 变化率
        roc = talib.ROC(close, timeperiod=10)
        
        # 威廉指标
        williams_r = talib.WILLR(data['high'].values, data['low'].values, close, timeperiod=14)
        
        return {
            'momentum': momentum,
            'roc': roc,
            'williams_r': williams_r,
            'momentum_signal': self._analyze_momentum_signal(momentum, roc)
        }
    
    def calculate_all_indicators(self, data):
        """计算所有可用的技术指标"""
        try:
            if len(data) < 60:  # 确保有足够的数据
                return None

            # 确保数据类型正确
            data = data.copy()
            for col in ['open', 'high', 'low', 'close', 'volume']:
                if col in data.columns:
                    data[col] = pd.to_numeric(data[col], errors='coerce').astype(np.float64)

            indicators = {}

            # 趋势指标
            indicators['ma20'] = self.calculate_ma(data, 20)
            indicators['ma60'] = self.calculate_ma(data, 60)
            indicators['ema20'] = self.calculate_ema(data, 20)
            indicators['trend'] = self.calculate_trend_indicators(data)

            # 动量指标
            indicators['rsi'] = self.calculate_rsi(data)
            indicators['kdj'] = self.calculate_kdj(data)
            indicators['macd'] = self.calculate_macd(data)
            indicators['momentum'] = self.calculate_momentum_indicators(data)

            # 波动性指标
            indicators['bollinger'] = self.calculate_bollinger_bands(data)
            indicators['atr'] = self.calculate_atr(data)
            indicators['volatility'] = self.calculate_volatility_indicators(data)

            # 成交量指标
            indicators['volume'] = self.calculate_volume_indicators(data)

            # 支撑压力位
            indicators['support_resistance'] = self.calculate_support_resistance(data)

            return indicators

        except Exception as e:
            print(f"计算技术指标时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    # 辅助方法
    def _detect_macd_cross(self, macd, signal):
        """检测MACD金叉死叉"""
        if len(macd) < 2 or len(signal) < 2:
            return 'none'
        
        prev_diff = macd[-2] - signal[-2]
        curr_diff = macd[-1] - signal[-1]
        
        if prev_diff <= 0 and curr_diff > 0:
            return 'golden_cross'
        elif prev_diff >= 0 and curr_diff < 0:
            return 'death_cross'
        else:
            return 'none'
    
    def _analyze_kdj_signal(self, k, d, j):
        """分析KDJ信号"""
        if len(k) < 2:
            return 'none'
        
        k_val = k[-1]
        d_val = d[-1]
        j_val = j[-1]
        
        if k_val > 80 and d_val > 80:
            return 'overbought'
        elif k_val < 20 and d_val < 20:
            return 'oversold'
        elif k[-2] <= d[-2] and k[-1] > d[-1]:
            return 'golden_cross'
        elif k[-2] >= d[-2] and k[-1] < d[-1]:
            return 'death_cross'
        else:
            return 'neutral'
    
    def _calculate_trend(self, prices, period):
        """计算趋势方向"""
        if len(prices) < period:
            return 0

        recent_prices = prices[-period:]

        if SCIPY_AVAILABLE:
            slope, _, r_value, _, _ = stats.linregress(range(len(recent_prices)), recent_prices)
            # 考虑斜率和相关性
            if abs(r_value) > 0.7:  # 强相关性
                return 1 if slope > 0 else -1
            else:
                return 0  # 震荡
        else:
            # 简化的趋势计算
            start_price = np.mean(recent_prices[:3])
            end_price = np.mean(recent_prices[-3:])
            change_pct = (end_price - start_price) / start_price

            if abs(change_pct) > 0.02:  # 2%以上变化认为有趋势
                return 1 if change_pct > 0 else -1
            else:
                return 0
    
    def _calculate_trend_strength(self, prices, period):
        """计算趋势强度"""
        if len(prices) < period:
            return 0

        recent_prices = prices[-period:]

        if SCIPY_AVAILABLE:
            slope, _, r_value, _, _ = stats.linregress(range(len(recent_prices)), recent_prices)
            return abs(r_value)  # 相关系数的绝对值表示趋势强度
        else:
            # 简化的趋势强度计算
            # 使用价格变化的一致性来衡量趋势强度
            changes = np.diff(recent_prices)
            positive_changes = np.sum(changes > 0)
            negative_changes = np.sum(changes < 0)
            total_changes = len(changes)

            if total_changes == 0:
                return 0

            # 计算方向一致性
            consistency = max(positive_changes, negative_changes) / total_changes
            return consistency
    
    def _analyze_trend_consistency(self, short, medium, long):
        """分析趋势一致性"""
        trends = [short, medium, long]
        
        if all(t > 0 for t in trends):
            return 'strong_uptrend'
        elif all(t < 0 for t in trends):
            return 'strong_downtrend'
        elif short == medium == long == 0:
            return 'sideways'
        else:
            return 'mixed'
    
    def _determine_overall_direction(self, short, medium, long):
        """确定整体趋势方向"""
        # 加权计算，短期权重最高
        weighted_trend = short * 0.5 + medium * 0.3 + long * 0.2
        
        if weighted_trend > 0.3:
            return '强势上升'
        elif weighted_trend > 0:
            return '温和上升'
        elif weighted_trend < -0.3:
            return '强势下降'
        elif weighted_trend < 0:
            return '温和下降'
        else:
            return '震荡整理'
    
    def _calculate_level_strength(self, prices, index, window):
        """计算支撑压力位强度"""
        target_price = prices[index]
        strength = 0
        
        # 在更大范围内寻找相似价格
        search_range = min(len(prices), window * 3)
        start_idx = max(0, index - search_range)
        end_idx = min(len(prices), index + search_range)
        
        for i in range(start_idx, end_idx):
            if i != index:
                price_diff = abs(prices[i] - target_price) / target_price
                if price_diff < 0.01:  # 1%以内认为是同一水平
                    strength += 1
        
        return strength
    
    def _classify_volatility(self, hist_vol, atr_vol):
        """分类波动率等级"""
        avg_vol = (hist_vol + atr_vol) / 2
        
        if avg_vol > 0.4:
            return '极高'
        elif avg_vol > 0.3:
            return '较高'
        elif avg_vol > 0.2:
            return '正常'
        elif avg_vol > 0.1:
            return '较低'
        else:
            return '极低'
    
    def _calculate_volatility_percentile(self, current_vol, returns):
        """计算波动率百分位"""
        if len(returns) < 60:
            return 50
        
        # 计算历史波动率序列
        window = 20
        hist_vols = []
        for i in range(window, len(returns)):
            vol = np.std(returns[i-window:i]) * np.sqrt(252)
            hist_vols.append(vol)
        
        if not hist_vols:
            return 50
        
        # 计算当前波动率的百分位
        percentile = (np.sum(np.array(hist_vols) <= current_vol) / len(hist_vols)) * 100
        return percentile
    
    def _analyze_momentum_signal(self, momentum, roc):
        """分析动量信号"""
        if len(momentum) < 2 or len(roc) < 2:
            return 'neutral'
        
        mom_trend = momentum[-1] - momentum[-2]
        roc_trend = roc[-1] - roc[-2]
        
        if mom_trend > 0 and roc_trend > 0:
            return 'strengthening'
        elif mom_trend < 0 and roc_trend < 0:
            return 'weakening'
        else:
            return 'mixed'
