import pandas as pd
import numpy as np
from concurrent.futures import ProcessPoolExecutor
import os
import warnings
from numba import jit
from data_process import process_data_file
pd.set_option('expand_frame_repr', False)
pd.set_option('display.max_rows', 5000)
warnings.filterwarnings("ignore")



@jit(nopython=True)
def calculate_correlation(x, y):
    # 计算 x 和 y 的均值
    mean_x = np.mean(x)
    mean_y = np.mean(y)
    # 计算协方差
    covariance = np.sum((x - mean_x) * (y - mean_y))
    # 计算标准差
    std_x = np.sqrt(np.sum((x - mean_x) ** 2))
    std_y = np.sqrt(np.sum((y - mean_y) ** 2))
    # 计算相关系数
    correlation = covariance / (std_x * std_y)
    return correlation


def cal_r_stock(file, futures_df, start_time, end_time, stock_name, length, df_self):
    file_name = file.split('\\')[-1].split('.')[0]
    print(f"正在期货合约: {file_name}")
    df = futures_df
    
    # 将字符串时间转换为datetime.date类型
    start_time_date = pd.to_datetime(start_time).date()
    end_time_date = pd.to_datetime(end_time).date()
    df = df[(df['交易日期'] >= start_time_date) & (df['交易日期'] <= end_time_date)]
    
    if file_name == stock_name:
        for_range = len(df) - length * 2 + 1
    else:
        for_range = len(df) - length * 2 + 1 + 1
    results = []

    for i in range(for_range):
        open_corr = calculate_correlation(df['open'][i:i + length].to_numpy(), df_self['open'].to_numpy())
        close_corr = calculate_correlation(df['close'][i:i + length].to_numpy(), df_self['close'].to_numpy())
        high_corr = calculate_correlation(df['high'][i:i + length].to_numpy(), df_self['high'].to_numpy())
        low_corr = calculate_correlation(df['low'][i:i + length].to_numpy(), df_self['low'].to_numpy())
        r = (open_corr + close_corr + high_corr + low_corr) / 4
        results.append({
            'stock': file_name,
            'startdate': df['交易日期'].iloc[i],
            'enddate': df['交易日期'].iloc[i + length - 1],
            'r': r
        })
    return results


def calculate_similarity(length, start_time, end_time, stock_name, stock_start_time, stock_end_time, futures_df,
                         stock_path, max_num, least_r, only_self=False):
    """
    计算指定期货与其他期货的K线相似度。

    参数:
    - length: K线的长度（天数）。
    - start_time: 检索的开始时间（格式为 'YYYYMMDD'）。
    - end_time: 检索的结束时间（格式为 'YYYYMMDD'）。
    - stock_name: 需要计算相似度的期货合约（'IF2509'）。
    - stock_end_time: 要比较的股票/期货数据的截止时间。
    - stock_path: 存储数据的路径。
    - max_num: 最多返回的相似度个数。
    - least_r: 最低相似度阈值。

    返回:
    - 返回符合相似度条件的 DataFrame。
    """
    # 判断是用length 还是 stock_end_time
    if stock_start_time is not None:
        length = len(futures_df[(futures_df['交易日期'] > stock_start_time) &
                              (futures_df['交易日期'] <= stock_end_time)])
    else:
        stock_start_time = futures_df[futures_df['交易日期'] <= stock_end_time].iloc[-length]['交易日期']

    # 读取期货数据
    base_df = futures_df
    stock_end_time_date = pd.to_datetime(stock_end_time).date()
    base_df = base_df[base_df['交易日期'] <= stock_end_time_date]
    df_self = base_df.tail(length)

    # 期货数据
    file_list = [stock_name]
    all_results = []

    with ProcessPoolExecutor(max_workers=os.cpu_count()) as executor:
        # 提交每个文件到进程池
        futures = [
            executor.submit(cal_r_stock, file, futures_df, start_time, end_time, stock_name, length, df_self)
            for file in file_list
        ]
        # 收集所有结果
        for future in futures:
            all_results.extend(future.result())
    results = all_results

    # 将结果整理为 DataFrame 并按相似度排序
    df_result = pd.DataFrame(results)
    df_result = df_result.sort_values(by='r', ascending=False)

    # 删掉r太小的
    df_result = df_result[df_result['r'] >= least_r]
    
    # 计算startdate与enddate之间的实际天数
    df_result['actual_days'] = (pd.to_datetime(df_result['enddate']) - pd.to_datetime(df_result['startdate'])).dt.days + 1
    
    # 过滤掉时间跨度超过50天的结果
    df_result = df_result[df_result['actual_days'] <= 50]
    
    # 根据时间排序删掉中间有间断的
    time_col = '交易日期'
    
    df_result['start_idx'] = df_result['startdate'].apply(lambda x: futures_df[time_col].searchsorted(x))
    df_result['end_idx'] = df_result['enddate'].apply(lambda x: futures_df[time_col].searchsorted(x))
    df_result['days_diff_index'] = df_result['end_idx'] - df_result['start_idx'] + 1
    df_result = df_result[df_result['days_diff_index'] == length]
    # 留下符合条件的相似度，且最大为max_num 条的数据
    df_result = df_result.head(max_num)
    
    # 保留自己的结果 - 确保列数匹配
    if len(df_result) > 0:
        # 获取当前DataFrame的列名
        columns = df_result.columns.tolist()
        # 创建基准K线结果，包含所有必要的列
        base_row = {}
        base_row['stock'] = stock_name
        base_row['startdate'] = stock_start_time
        base_row['enddate'] = stock_end_time
        base_row['r'] = 100
        base_row['start_idx'] = None
        base_row['end_idx'] = None
        base_row['days_diff_index'] = None
        if 'actual_days' in columns:
            base_row['actual_days'] = 1  # 基准K线的时间跨度设为1天
        
        df_result.loc[len(df_result)] = base_row
    else:
        # 如果没有找到相似K线，创建包含基准K线的DataFrame
        df_result = pd.DataFrame([{
            'stock': stock_name,
            'startdate': stock_start_time,
            'enddate': stock_end_time,
            'r': 100,
            'start_idx': None,
            'end_idx': None,
            'days_diff_index': None,
            'actual_days': 1
        }])
    df_result = df_result.sort_values(by='r', ascending=False)
    df_result['startdate'] = pd.to_datetime(df_result['startdate'])
    df_result['enddate'] = pd.to_datetime(df_result['enddate'])
    
    # 删除临时列，保持输出格式整洁
    if 'actual_days' in df_result.columns:
        df_result = df_result.drop('actual_days', axis=1)
    
    print(f"最终结果:\n{df_result}")
    return df_result


def load_file(path, file):
    """
    加载数据文件
    """        
    if '/' in file:
        # 新格式：CFFEX/IF2509
        parts = file.split('/')
        if len(parts) != 2:
            raise ValueError(f"期货代码格式错误: {file}，应为 '交易所/合约代码' 格式")
        exchange, contract = parts
    else:
        raise ValueError(f"期货代码格式错误: {file}，必须为 '交易所/合约代码' 格式")
    
    # 直接使用交易所文件夹下的Day.txt文件
    day_file_path = f"Data/{exchange}/{contract}_Day.txt"
    
    if not os.path.exists(day_file_path):
        raise FileNotFoundError(f"找不到期货日线文件: {day_file_path}")
    
    # 使用data_process.py处理数据
    df = process_data_file(day_file_path)
    
    if df is None or df.empty:
        raise ValueError(f"期货数据处理失败: {day_file_path}")
    
    # 转换为标准格式
    df_result = pd.DataFrame()
    # 使用trading_date字段作为交易日期
    df_result['交易日期'] = pd.to_datetime(df['trading_date']).dt.date
    df_result['成交量'] = df['volume']
    df_result['持仓量'] = df['open_interest']
    
    # 设置价格字段
    df_result['开盘价'] = df['open']
    df_result['最高价'] = df['high']
    df_result['最低价'] = df['low']
    df_result['收盘价'] = df['close']
    
    # 同时设置兼容性字段
    df_result['open'] = df['open']
    df_result['high'] = df['high']
    df_result['low'] = df['low']
    df_result['close'] = df['close']
    
    # 计算前收盘价
    df_result['前收盘价'] = df_result['收盘价'].shift(1)
    df_result['前收盘价'].fillna(value=df_result['开盘价'], inplace=True)
    
    # 添加期货相关字段（用于兼容性）
    df_result['期货合约'] = file
    
    # 按日期排序
    df_result = df_result.sort_values('交易日期').reset_index(drop=True)    
    return df_result
    


def merge_index(df, index_df):
    # ===将股票数据和上证指数合并，结果已经排序
    df = pd.merge(left=df, right=index_df, on='交易日期', how='right', sort=True, indicator=True)

    # ===对开、高、收、低、前收盘价价格进行补全处理
    # 用前一天的收盘价，补全收盘价的空值
    df['收盘价'].fillna(method='ffill', inplace=True)
    # 用收盘价补全开盘价、最高价、最低价的空值
    df['开盘价'].fillna(value=df['收盘价'], inplace=True)
    df['最高价'].fillna(value=df['收盘价'], inplace=True)
    df['最低价'].fillna(value=df['收盘价'], inplace=True)
    # 补全前收盘价
    df['前收盘价'].fillna(value=df['收盘价'].shift(), inplace=True)

    # ===用前一天的数据，补全其余空值
    df.fillna(method='ffill', inplace=True)

    # ===去除上市之前的数据

    df = df[df['期货合约'].notnull()]

    # ===判断计算当天是否交易
    df['是否交易'] = 1
    df.loc[df['_merge'] == 'right_only', '是否交易'] = 0
    del df['_merge']

    df.reset_index(drop=True, inplace=True)

    return df


def rehabilitation(df: pd.DataFrame) -> object:
    # =计算涨跌幅
    df['涨跌幅'] = df['收盘价'] / df['前收盘价'] - 1
    # =计算复权价：计算所有因子当中用到的价格，都使用复权价
    df['复权因子'] = (1 + df['涨跌幅']).cumprod()
    df['收盘价_复权'] = df['复权因子'] * (df.iloc[0]['收盘价'] / df.iloc[0]['复权因子'])
    df['开盘价_复权'] = df['开盘价'] / df['收盘价'] * df['收盘价_复权']
    df['最高价_复权'] = df['最高价'] / df['收盘价'] * df['收盘价_复权']
    df['最低价_复权'] = df['最低价'] / df['收盘价'] * df['收盘价_复权']
    # 原始的价格叫xx_原，复权两个字去掉
    df.rename({'开盘价': '开盘价_原', '最高价': '最高价_原', '最低价': '最低价_原', '收盘价': '收盘价_原'},
              inplace=True)
    df.rename({'开盘价_复权': '开盘价', '最高价_复权': '最高价', '最低价_复权': '最低价', '收盘价_复权': '收盘价'},
              inplace=True)

    return df


def process_r_file(futures_df, day_list, enddate_list):
    # 加载数据
    df = futures_df.copy()
    df['是否交易'] = 1  # 期货数据假设都是交易日
    # 计算交易天数
    df['交易天数'] = df.index + 1

    if df.empty:
        return None
    
    # 确保日期类型一致

    df['交易日期'] = pd.to_datetime(df['交易日期']).dt.date

    # 将所有 enddate_list 元素都统一转换为 datetime.date 类型
    # enddate_list = [pd.to_datetime(d).date() if isinstance(d, str) else d for d in enddate_list]
    enddate_list = [pd.to_datetime(d).date() for d in enddate_list]

    print(f"数据日期范围: {df['交易日期'].min()} 到 {df['交易日期'].max()}")
    print(f"查找的日期: {enddate_list}")

    df['signal'] = None
    df.loc[df['交易日期'].isin(enddate_list), 'signal'] = 1

    # 检查是否有匹配的日期
    matching_count = df['signal'].notna().sum()
    print(f"找到匹配的日期数量: {matching_count}")
    
    if matching_count == 0:
        print("警告: 没有找到匹配的日期，使用数据中的最后几个日期作为示例")
        # 如果没有找到匹配的日期，使用数据中的最后几个日期作为示例
        last_dates = df['交易日期'].tail(5).tolist()
        print(f"使用最后5个日期作为示例: {last_dates}")
        df.loc[df['交易日期'].isin(last_dates), 'signal'] = 1
    
    # 只计算day_list天后的涨跌幅
    for day in day_list:
        # 计算指定日期后day天的涨跌幅
        # 使用收盘价计算：未来day天后的收盘价 / 当前收盘价 - 1
        df['%s日后涨跌幅' % day] = df['收盘价'].shift(-day) / df['收盘价'] - 1

    # 选取指定时间范围内的期货
    df['event'] = 1
    df = df[df['成交量'] > 0]
    df = df[df['signal'].notna()]
    
    # 过滤掉涨跌幅为NaN的行（数据末尾不足day天的行）
    for day in day_list:
        column_name = f'{day}日后涨跌幅'
        df = df[df[column_name].notna()]
    
    print(f"过滤后剩余行数: {len(df)}")
    
    # 只保留必要的列
    new_columns = ['signal', '交易日期', '期货合约', 'event']
    for day in day_list:
        new_columns.extend(['%s日后涨跌幅' % day])

    df = df[new_columns]
    return df

