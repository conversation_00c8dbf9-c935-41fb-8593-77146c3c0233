from similarity import *
import warnings
import os
import pandas as pd
import argparse

def calculate_similarity_process(args):
    return calculate_similarity(*args)

def save_results_to_txt(df_result_r_original, dfs, day_list, output_filename):
    """
    保存结果到txt文件
    结合df_result_r和dfs中的数据
    包含字段：stock，startdate，enddate，r(百分数形式，保留两位小数)，5日后涨跌幅，10日后涨跌幅度
    按r从高到低排序
    """
    print(f"\n正在保存结果到 {output_filename}...")
    
    # 确保输出目录存在
    import os
    output_dir = os.path.dirname(output_filename)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建目录: {output_dir}")
    
    # 创建结果列表
    results = []
    
    # 处理df_result_r_original中的每一行数据
    for index, row in df_result_r_original.iterrows():
        stock = row['stock']
        startdate = row['startdate']
        enddate = row['enddate']
        r = row['r']
        
        # 将r转换为百分数形式，保留两位小数
        # 如果r已经是100（基准K线），则直接显示为100%
        if r == 100:
            r_percent = "100.00%"
        else:
            r_percent = f"{r * 100:.2f}%"
        
        # 如果r为100%，则5日后和10日后涨跌幅写为none
        if r == 100:
            day5_return = "none"
            day10_return = "none"
        else:
            # 从dfs中查找对应的涨跌幅数据
            day5_return = "none"
            day10_return = "none"
            
            # 在所有dfs中查找匹配的enddate
            for df in dfs:
                if not df.empty:
                    # 查找匹配的enddate
                    enddate_date = pd.to_datetime(enddate).date()
                    matching_rows = df[df['交易日期'] == enddate_date]
                    
                    if not matching_rows.empty:
                        # 找到匹配的行，获取涨跌幅数据
                        if '5日后涨跌幅' in df.columns:
                            day5_value = matching_rows['5日后涨跌幅'].iloc[0]
                            if pd.notna(day5_value):
                                day5_return = f"{day5_value * 100:.2f}%"
                        
                        if '10日后涨跌幅' in df.columns:
                            day10_value = matching_rows['10日后涨跌幅'].iloc[0]
                            if pd.notna(day10_value):
                                day10_return = f"{day10_value * 100:.2f}%"
                        break
        
        # 添加到结果列表
        results.append({
            'stock': stock,
            'startdate': startdate.strftime('%Y-%m-%d'),
            'enddate': enddate.strftime('%Y-%m-%d'),
            'r': r_percent,
            '5日后涨跌幅': day5_return,
            '10日后涨跌幅度': day10_return
        })
    
    # 按r从高到低排序（去掉百分号后排序）
    results.sort(key=lambda x: float(x['r'].rstrip('%')), reverse=True)
    
    # 写入txt文件
    with open(output_filename, 'w', encoding='utf-8') as f:
        # 写入表头
        f.write("stock\tstartdate\tenddate\tr\t5日后涨跌幅\t10日后涨跌幅度\n")
        
        # 写入数据
        for result in results:
            f.write(f"{result['stock']}\t{result['startdate']}\t{result['enddate']}\t{result['r']}\t{result['5日后涨跌幅']}\t{result['10日后涨跌幅度']}\n")
    
    print(f"结果已保存到 {output_filename}")
    print(f"共保存 {len(results)} 条记录")

def main():
    warnings.filterwarnings("ignore")
    pd.set_option('expand_frame_repr', False)
    pd.set_option('display.max_rows', 5000)

    # 新增命令行参数解析
    parser = argparse.ArgumentParser(description='期货技术分析系统 v0.2')
    parser.add_argument('--symbol', type=str, help="期货合约号，格式如 CFFEX/IF2509")
    parser.add_argument('--length', type=int, default=20, help="K线长度(天数，默认20)")
    args, unknown = parser.parse_known_args()

    # 让用户输入期货合约号
    futures_symbol = None
    if args.symbol:
        futures_symbol = args.symbol.strip()
    else:
        print("格式: 交易所/合约代码 (如: CFFEX/IF2509)")
        while True:
            try:
                futures_symbol = input("请输入期货合约号: ").strip()
                if not futures_symbol:
                    print("输入不能为空，请重新输入")
                    continue
                if '/' not in futures_symbol:
                    raise ValueError(f"格式错误: {futures_symbol}，必须使用 '交易所/合约代码' 格式，如 CFFEX/IF2509")
                parts = futures_symbol.split('/')
                if len(parts) != 2:
                    raise ValueError(f"格式错误: {futures_symbol}，必须使用 '交易所/合约代码' 格式，如 CFFEX/IF2509")
                exchange, contract = parts
                if not exchange or not contract:
                    raise ValueError(f"格式错误: {futures_symbol}，交易所和合约代码都不能为空")
                supported_exchanges = ['CFFEX', 'DCE', 'GFEX', 'SHFE', 'ZCE']
                if exchange not in supported_exchanges:
                    raise ValueError(f"不支持的交易所: {exchange}，支持的交易所: {', '.join(supported_exchanges)}")
                break
            except ValueError as e:
                print(f"错误: {e}")
                continue
            except KeyboardInterrupt:
                print("\n程序已退出")
                return
            except Exception as e:
                print(f"输入错误: {e}")
                continue

    # length 指定看多少个交易日的K线
    length = args.length  # 直接使用命令行参数，默认值为20

    stock_start_time = None

    # 根据期货代码确定数据路径
    parts = futures_symbol.split('/')
    exchange, contract = parts
    stock_path = f"Data/{exchange}/"  # 使用对应的交易所文件夹

    # 最多会选多少组相似K线做评价
    max_num = 10000

    # 确认在自己历史上找相似K线还是在所有合约上找
    # False 是在所有合约，True是在自己历史上
    only_self = True

    # 选择大于该相似度的相似K线做评价
    least_r = 0.9

    # 未来1/2/3/5/10/20天的表现
    day_list = [5, 10]

    # 输出图片中，向后画多少天的表现
    future_days = 5
    # 输出图片中，画多少组K线
    future_pics = 5

    # 加载期货数据
    print(f"\n正在加载数据: {futures_symbol}")
    try:
        futures_df = load_file(stock_path, futures_symbol)
        
        # 从期货数据中获取实际的时间范围
        start_time = futures_df['交易日期'].min().strftime('%Y/%m/%d')
        end_time = futures_df['交易日期'].max().strftime('%Y/%m/%d')
        stock_end_time = end_time  # 使用数据的实际结束时间
        
        print(f"数据加载成功！")
        print(f"数据时间范围: {start_time} 到 {end_time}")
        print(f"数据总行数: {len(futures_df)}")
        
    except Exception as e:
        print(f"数据加载失败: {e}")
        print("请检查:")
        print("1. 期货代码是否正确")
        print("2. 对应的数据文件是否存在")
        print("3. 数据文件格式是否正确")
        return

    if stock_start_time is not None:
        # 统一时间类型为datetime.date
        stock_start_time = pd.to_datetime(stock_start_time).date()
        stock_end_time = pd.to_datetime(stock_end_time).date()
    else:
        # 统一时间类型为datetime.date
        stock_end_time = pd.to_datetime(stock_end_time).date()

    print(f"\n开始计算相似度...")    
    # ======================第一步，计算相似度
    params = length, start_time, end_time, futures_symbol, stock_start_time, stock_end_time, futures_df, stock_path, max_num, least_r, only_self
    df_result_r = calculate_similarity_process(params)
    # df_result_r.to_csv('相识度计算结果.csv', index=False)
    # print(f"相似度计算完成，结果已保存到 '相似度计算结果.csv'")

    # 保存原始的df_result_r用于后续处理
    df_result_r_original = df_result_r.copy()

    # ======================第三步，分析后续涨幅
    print(f"分析后续涨幅...")
    dfs = []
    df_result_r_grouped = df_result_r.groupby('stock',as_index=False).agg({'enddate': list})
    for index, row in df_result_r_grouped.iterrows():
        f = row['stock']
        enddate_list = row['enddate']
        print('enddate_list',enddate_list)
        mini_df = process_r_file(futures_df, day_list, enddate_list)
        if mini_df is not None:
            dfs.append(mini_df)
    print('dfs',dfs)
    
    # ======================第四步，保存txt结果文件
    output_filename = f"output_2/{futures_symbol}.txt"
    save_results_to_txt(df_result_r_original, dfs, day_list, output_filename)
    

if __name__ == '__main__':
    main()