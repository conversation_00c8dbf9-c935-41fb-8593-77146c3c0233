# 期货分析系统依赖包
# 核心数据处理
pandas>=1.3.0
numpy>=1.21.0

# 技术指标计算（可选，但推荐安装）
TA-Lib>=0.4.24

# 科学计算（可选，用于高级统计功能）
scipy>=1.7.0

# 性能优化
numba>=0.56.0

# 并发处理
# concurrent.futures 是Python标准库，无需安装

# 其他标准库（无需安装）
# os, sys, argparse, logging, datetime, pathlib, warnings, re

# 安装说明：
# 1. 基础安装（必需）：
#    pip install pandas numpy numba
#
# 2. 完整安装（推荐）：
#    pip install -r requirements.txt
#
# 3. 如果TA-Lib安装失败，可以跳过：
#    pip install pandas numpy numba scipy
#
# 4. Windows用户安装TA-Lib可能需要先安装预编译版本：
#    pip install TA-Lib --index-url https://pypi.org/simple/
#    或者从 https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib 下载对应版本 