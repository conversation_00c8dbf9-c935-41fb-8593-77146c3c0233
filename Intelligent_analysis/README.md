# 期货分析系统使用说明

## 概述

本系统包含两种不同的分析模式，可以通过统一的入口脚本运行，支持命令行参数输入，无需交互式操作。

## 文件说明

- `run_analysis.py` - 主入口脚本（支持命令行参数）
- `analyze_1.py` - 技术指标分析模式
- `analyze_2.py` - 相似性分析模式

## 使用方法

### 方法一：使用Python脚本（推荐）

#### 命令行直接运行

**模式1：技术指标分析**
```bash
# 基本用法
python run_analysis.py --mode 1 CFFEX/IF2509

# 使用--symbol参数
python run_analysis.py --mode 1 --symbol DCE/a2509

# 分别指定交易所和合约
python run_analysis.py --mode 1 --exchange SHFE --contract ag2508

```

**模式2：相似性分析**
```bash
# 基本用法（使用默认K线长度20天）
python run_analysis.py --mode 2 CFFEX/IF2509

# 指定K线长度
python run_analysis.py --mode 2 DCE/a2509 --length 15

# 使用--symbol参数
python run_analysis.py --mode 2 --symbol SHFE/ag2508 --length 20

# 分别指定交易所和合约
python run_analysis.py --mode 2 --exchange CFFEX --contract IF2509 --length 20
```


### 方法二：直接运行分析脚本

**技术指标分析**
```bash
python analyze_1.py CFFEX/IF2509
python analyze_1.py --symbol DCE/a2509 --output-dir output_1
```

**相似性分析**
```bash
python analyze_2.py --symbol CFFEX/IF2509 --length 20
```

## 分析模式说明

### 模式1：技术指标分析 (analyze_1.py)
- **功能**：计算各种技术指标并生成分析报告
- **输入参数**：期货合约代码（如：CFFEX/IF2509）
- **输出**：技术指标分析报告，包含趋势、动量、波动性等分析
- **特点**：全面的技术分析，包含RSI、MACD、布林带等指标
- **输出目录**：`output_1/` 目录

### 模式2：相似性分析 (analyze_2.py)
- **功能**：分析期货合约的K线相似性
- **输入参数**：
  - 期货合约代码（如：CFFEX/IF2509）
  - K线长度（天数，默认20天）
- **输出**：相似K线分析结果，包含未来涨跌幅预测
- **特点**：基于历史K线模式匹配，寻找相似走势
- **输出目录**：`output_2/` 目录

## 支持的交易所

- **CFFEX** - 中国金融期货交易所
- **DCE** - 大连商品交易所
- **GFEX** - 广州期货交易所
- **SHFE** - 上海期货交易所
- **ZCE** - 郑州商品交易所

## 数据要求

确保 `Data/` 目录下包含相应交易所的数据文件，格式为：
```
Data/
├── CFFEX/
│   ├── IF2509_Day.txt
│   ├── IF2509_Minute_1.txt
│   └── ...
├── DCE/
│   ├── a2509_Day.txt
│   └── ...
└── ...
```

## 命令行参数说明

### 通用参数
- `--mode, -m`：选择运行模式（1=技术指标分析，2=相似性分析）
- `--symbol`：合约代码（格式：交易所/合约代码）
- `--exchange`：交易所代码
- `--contract`：合约代码
- `--output-dir`：输出目录（默认：output_1）
- `--verbose, -v`：显示详细输出

### 模式2专用参数
- `--length`：K线长度（天数，默认20）

## 注意事项

1. 确保Python环境已正确安装
2. 确保所有依赖模块文件存在（similarity.py, local_data_loader.py等）
3. 确保数据文件格式正确
4. 建议在运行前备份重要数据
5. 命令行参数支持多种输入方式，选择最方便的方式即可

## 错误处理

如果遇到错误：
1. 检查文件路径是否正确
2. 检查数据文件是否存在
3. 检查Python环境是否正常
4. 查看错误信息进行相应处理
5. 使用 `--help` 参数查看详细使用说明

``` 