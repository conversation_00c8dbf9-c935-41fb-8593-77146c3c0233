import pandas as pd
import os
import re
from datetime import datetime

def clean_data_line(line):
    """
    清理数据行，删除不符合格式的数据
    只保留以有效交易所开头的记录，如果发现异常字符会清理
    支持的交易所：CFFEX, DCE, GFEX, SHFE, ZCE
    """
    # 按分号分割每条记录
    records = line.strip().split(';')
    cleaned_records = []
    
    # 定义有效的交易所列表
    valid_exchanges = ['CFFEX', 'DCE', 'GFEX', 'SHFE', 'ZCE']
    
    for record in records:
        if record.strip():
            # 检查记录是否以任何有效交易所开头
            valid_record = False
            for exchange in valid_exchanges:
                if record.strip().startswith(exchange):
                    # 按逗号分割字段
                    fields = record.split(',')
                    # 检查是否有足够的字段且第一个字段是有效交易所
                    if len(fields) >= 14 and fields[0] in valid_exchanges:
                        cleaned_records.append(record)
                        valid_record = True
                        break  # 找到匹配的交易所后跳出循环
            
            # 如果没有直接匹配，尝试清理异常字符
            if not valid_record:
                for exchange in valid_exchanges:
                    # 查找交易所名称在记录中的位置
                    exchange_pos = record.find(exchange)
                    if exchange_pos > 0:  # 找到了交易所，但不在开头
                        # 提取从交易所开始的部分
                        cleaned_record = record[exchange_pos:]
                        # 验证清理后的记录是否有效
                        fields = cleaned_record.split(',')
                        if len(fields) >= 14 and fields[0] in valid_exchanges:
                            cleaned_records.append(cleaned_record)
                            break
    
    # 重新组合清理后的记录
    return ';'.join(cleaned_records)

def parse_data_line(line):
    """
    解析数据行，返回DataFrame
    字段映射：
    字段[2]: trading_date - 交易日
    字段[3]: update_time - 更新时间  
    字段[5]: open - 开盘价
    字段[6]: high - 最高价
    字段[7]: low - 最低价
    字段[8]: close - 收盘价
    字段[9]: volume - 成交量
    字段[10]: open_interest - 持仓量
    字段[11]: prev_open_interest - 前一根持仓
    字段[12]: cumulative_volume - 累计成交量
    """
    # 首先清理数据
    cleaned_line = clean_data_line(line)
    
    # 按分号分割每条记录
    records = cleaned_line.strip().split(';')
    data_list = []
    
    for record in records:
        if record.strip():
            # 按逗号分割字段
            fields = record.split(',')
            if len(fields) >= 14:  # 确保有足够的字段
                try:
                    data_list.append({
                        'trading_date': fields[2],      # 交易日
                        'update_time': fields[3],       # 更新时间
                        'open': float(fields[5]),       # 开盘价
                        'high': float(fields[6]),       # 最高价
                        'low': float(fields[7]),        # 最低价
                        'close': float(fields[8]),      # 收盘价
                        'volume': int(fields[9]),       # 成交量
                        'open_interest': int(fields[10]), # 持仓量
                        'prev_open_interest': int(fields[11]), # 前一根持仓
                        'cumulative_volume': int(fields[12])   # 累计成交量
                    })
                except (ValueError, IndexError) as e:
                    # 跳过无法解析的记录
                    continue
    
    df = pd.DataFrame(data_list)
    if not df.empty:
        # 合并日期和时间创建datetime索引
        df['datetime'] = pd.to_datetime(df['trading_date'] + ' ' + df['update_time'], format='%Y%m%d %H:%M:%S')
        df.set_index('datetime', inplace=True)
        # 按交易日和更新时间排序
        df = df.sort_index()
    
    return df

def read_data_file(file_path):
    """
    读取数据文件，支持多种编码格式
    """
    encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'latin1']
    data_line = None
    
    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                data_line = f.read()
            print(f"成功使用 {encoding} 编码读取文件")
            break
        except UnicodeDecodeError:
            continue
    
    if data_line is None:
        raise ValueError("无法使用任何编码格式读取文件，请检查文件编码")
    
    return data_line

def process_data_file(input_file_path):
    """
    处理数据文件的主函数
    
    参数:
    input_file_path: 输入文件路径
    """
    print(f"开始处理文件: {input_file_path}")
    
    # 读取原始数据
    data_line = read_data_file(input_file_path)
    
    # 显示原始数据统计
    original_records = len([r for r in data_line.strip().split(';') if r.strip()])
    print(f"原始数据记录数: {original_records}")
    
    # 解析数据
    df = parse_data_line(data_line)
    
    # 显示清理后的数据统计
    cleaned_records = len(df)
    print(f"清理后有效记录数: {cleaned_records}")
    if original_records > cleaned_records:
        print(f"已删除 {original_records - cleaned_records} 条无效记录")
    
    if df.empty:
        print("数据解析失败或数据为空")
        return None
    
    print(f"数据时间范围: {df.index.min()} 到 {df.index.max()}")
    
    return df