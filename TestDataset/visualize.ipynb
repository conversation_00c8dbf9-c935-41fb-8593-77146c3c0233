import os
import json
import numpy as np
from tqdm import tqdm
from pycocotools import mask as coco_mask
import cv2

# Paths
IMAGES_DIR = "./ph2_dataset/trainx"  # Path to the trainx folder (images)
MASKS_DIR = "./ph2_dataset/trainy"  # Path to the trainy folder (masks)
OUTPUT_JSON = "./ph2_dataset/all_coco.json"  # Output COCO-format JSON file

# COCO format placeholders
coco = {
    "images": [],
    "annotations": [],
    "categories": [{"id": 1, "name": "lesion", "supercategory": "skin"}]
}

def extract_bbox(binary_mask):
    """Extract bounding box from binary mask."""
    coords = np.argwhere(binary_mask)
    if coords.shape[0] == 0:  # Handle cases where the mask is empty
        return [0, 0, 0, 0]
    ymin, xmin = coords.min(axis=0)
    ymax, xmax = coords.max(axis=0)
    width = xmax - xmin
    height = ymax - ymin
    return [xmin, ymin, width, height]

def encode_mask(binary_mask):
    """Encode binary mask to COCO RLE format."""
    rle = coco_mask.encode(np.asfortranarray(binary_mask))
    rle["counts"] = rle["counts"].decode("utf-8")  # Make JSON serializable
    return rle


from pycocotools.coco import COCO
import matplotlib.pyplot as plt
import matplotlib.patches as patches

# Load COCO dataset
coco = COCO("./ph2_dataset/all_coco.json")

def visualize_coco_image(image_id):
    """Visualize an image and its annotations from the COCO dataset."""
    # Load image metadata and annotations
    image_info = coco.loadImgs(image_id)[0]
    img_path = os.path.join(IMAGES_DIR, image_info["file_name"])
    img = cv2.imread(img_path)
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

    ann_ids = coco.getAnnIds(imgIds=image_id)
    anns = coco.loadAnns(ann_ids)

    # Plot the image
    plt.figure(figsize=(10, 8))
    plt.imshow(img)
    plt.axis("off")
    ax = plt.gca()

    # Plot annotations
    for ann in anns:
        bbox = ann["bbox"]
        category_id = ann["category_id"]
        category_name = coco.loadCats([category_id])[0]["name"]

        # Draw bounding box
        rect = patches.Rectangle(
            (bbox[0], bbox[1]), bbox[2], bbox[3],
            linewidth=2, edgecolor="red", facecolor="none"
        )
        ax.add_patch(rect)
        plt.text(
            bbox[0], bbox[1] - 10, category_name,
            color="red", fontsize=12, backgroundcolor="white"
        )

    plt.show()

# Visualize the first image
visualize_coco_image(1)


import os
import cv2
from tqdm import tqdm

# Paths
INPUT_DIR = "./ph2_dataset/trainy"  # Directory containing .bmp images
OUTPUT_DIR = "./ph2_dataset/masks"  # Directory to save .png images

os.makedirs(OUTPUT_DIR, exist_ok=True)

# Convert .bmp to .png
for bmp_file in tqdm(os.listdir(INPUT_DIR), desc="Converting BMP to PNG"):
    if bmp_file.endswith(".bmp"):
        bmp_path = os.path.join(INPUT_DIR, bmp_file)
        png_path = os.path.join(OUTPUT_DIR, os.path.splitext(bmp_file)[0] + ".png")
        
        # Read and save as PNG
        img = cv2.imread(bmp_path)
        cv2.imwrite(png_path, img)

print(f"Conversion complete. PNG files saved in {OUTPUT_DIR}")


import json

COCO_JSON_PATH = "./ph2_dataset/all_coco.json"  # Path to your COCO JSON file
OUTPUT_JSON_PATH = "./ph2_dataset/all_coco_png.json"  # Save updated JSON file

# Load COCO JSON
with open(COCO_JSON_PATH, "r") as f:
    coco_data = json.load(f)

# Update filenames in the images section
for image in coco_data["images"]:
    image["file_name"] = image["file_name"].replace(".bmp", ".png")

# Save updated JSON
with open(OUTPUT_JSON_PATH, "w") as f:
    json.dump(coco_data, f, indent=4)

print(f"Updated COCO JSON saved to {OUTPUT_JSON_PATH}")


